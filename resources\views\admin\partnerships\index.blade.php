@extends('admin.layouts.app')

@section('title', 'Partnerships')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Partnerships</h1>
        <a href="{{ route('admin.partnerships.create') }}" class="btn btn-success">
            <i class="fas fa-plus me-1"></i> Add New
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            @if($partnerships->isEmpty())
                <div class="text-center py-5">
                    <i class="fas fa-handshake fa-3x text-muted mb-3"></i>
                    <p class="mb-0">No partnerships found. Click the "Add New" button to create one.</p>
                </div>
            @else
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="80">Logo</th>
                                <th>Name</th>
                                <th>Website</th>
                                <th>Since</th>
                                <th width="120">Country</th>
                                <th width="150">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($partnerships as $partnership)
                                <tr>
                                    <td>
                                        @if($partnership->logo)
                                            <img src="{{ asset('storage/' . $partnership->logo) }}" alt="{{ $partnership->name }}" class="img-thumbnail" width="60">
                                        @else
                                            <div class="bg-light text-center p-2 rounded">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="fw-bold">{{ $partnership->name }}</div>
                                        <small class="text-muted">{{ $partnership->name_en }}</small>
                                    </td>
                                    <td>
                                        @if($partnership->website)
                                            <a href="{{ $partnership->website }}" target="_blank">{{ $partnership->website }}</a>
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td>{{ $partnership->partnership_since ? $partnership->partnership_since->format('d M Y') : '-' }}</td>
                                    <td>
                                        @if($partnership->country_code)
                                            <img src="https://flagcdn.com/24x18/{{ strtolower($partnership->country_code) }}.png" class="me-2" alt="{{ $partnership->country_name }}" width="24" height="18">
                                            {{ $partnership->country_name }}
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('admin.partnerships.edit', $partnership) }}" class="btn btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ route('admin.partnerships.show', $partnership) }}" class="btn btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ $partnership->id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Delete Modal -->
                                        <div class="modal fade" id="deleteModal{{ $partnership->id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ $partnership->id }}" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="deleteModalLabel{{ $partnership->id }}">Confirm Delete</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        Are you sure you want to delete the partnership with "{{ $partnership->name }}"?
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                        <form action="{{ route('admin.partnerships.destroy', $partnership) }}" method="POST">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-danger">Delete</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @endif
        </div>
    </div>
@endsection
