<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\ImageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class UploadController extends Controller
{
    /**
     * The image service instance.
     *
     * @var \App\Services\ImageService
     */
    protected $imageService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\ImageService  $imageService
     * @return void
     */
    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Upload an image from Summernote editor
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadSummernoteImage(Request $request)
    {
        // Validate the request with detailed error messages
        $validator = \Illuminate\Support\Facades\Validator::make($request->all(), [
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:10240', // 10MB limit
        ], [
            'image.required' => 'No image was provided',
            'image.image' => 'The file must be an image',
            'image.mimes' => 'Only JPEG, PNG, and GIF images are allowed',
            'image.max' => 'The image size must not exceed 10MB',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
                'message' => $validator->errors()->first('image')
            ], 422);
        }

        try {
            // Get the image file
            $image = $request->file('image');

            // Make sure the directory exists
            $directory = 'uploads/summernote';
            if (!Storage::disk('public')->exists($directory)) {
                Storage::disk('public')->makeDirectory($directory);
            }

            // Use ImageService to compress and save the image
            // For Summernote, we'll use higher quality to ensure good image display
            $path = $this->imageService->compressAndSave(
                $image,
                $directory,
                90, // Higher quality for editor images
                config('image.max_width', 1920),
                config('image.max_height', 1080)
            );

            // Verify the file was stored successfully
            if (!$path || !Storage::disk('public')->exists($path)) {
                throw new \Exception('Failed to store the image');
            }

            // Log the image path for debugging
            \Illuminate\Support\Facades\Log::info('Summernote image uploaded successfully', [
                'path' => $path,
                'full_url' => asset('storage/' . $path)
            ]);

            // Return success response with the image URL
            // Make sure the URL is absolute and properly formatted

            // Ensure the path is correctly formatted
            $path = str_replace('\\', '/', $path); // Replace backslashes with forward slashes

            // Get the absolute URL using the current request
            $baseUrl = request()->getSchemeAndHttpHost();
            $imageUrl = $baseUrl . '/storage/' . $path;

            // Also create a relative URL as a fallback
            $relativeUrl = '/storage/' . $path;

            // Verify the image exists and is accessible
            $publicPath = public_path('storage/' . $path);
            $fileExists = file_exists($publicPath);

            if (!$fileExists) {
                \Illuminate\Support\Facades\Log::warning('Image file not found at path: ' . $publicPath);

                // Try to create the directory if it doesn't exist
                $directory = dirname($publicPath);
                if (!file_exists($directory)) {
                    mkdir($directory, 0755, true);
                    \Illuminate\Support\Facades\Log::info('Created directory: ' . $directory);
                }

                // Copy the file from storage to public path as a fallback
                $storagePath = storage_path('app/public/' . $path);
                if (file_exists($storagePath)) {
                    copy($storagePath, $publicPath);
                    \Illuminate\Support\Facades\Log::info('Copied file from ' . $storagePath . ' to ' . $publicPath);
                    $fileExists = true;
                } else {
                    \Illuminate\Support\Facades\Log::warning('Source file not found at: ' . $storagePath);
                }
            }

            // Log the final URL for debugging
            \Illuminate\Support\Facades\Log::info('Final image URL', [
                'base_url' => $baseUrl,
                'path' => $path,
                'full_url' => $imageUrl,
                'relative_url' => $relativeUrl,
                'public_path' => $publicPath,
                'file_exists' => $fileExists,
                'storage_path' => storage_path('app/public/' . $path),
                'storage_exists' => file_exists(storage_path('app/public/' . $path))
            ]);

            return response()->json([
                'success' => true,
                'url' => $relativeUrl, // Use relative URL for better compatibility
                'absolute_url' => $imageUrl,
                'filename' => basename($path)
            ]);
        } catch (\Exception $e) {
            // Log the error
            \Illuminate\Support\Facades\Log::error('Summernote image upload failed: ' . $e->getMessage());

            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload image: ' . $e->getMessage()
            ], 500);
        }
    }
}
