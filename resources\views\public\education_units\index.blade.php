@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? 'Unit Pendidikan' : 'Education Units')

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? 'Unit Pendidikan' : 'Education Units' }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? 'Unit Pendidikan' : 'Education Units' }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Education Units Section -->
    <section class="section-padding bg-light">
        <div class="container">
            <div class="section-title text-center fade-in">
                <h2>{{ app()->getLocale() == 'id' ? 'Program Pendidikan Kami' : 'Our Educational Programs' }}</h2>
                <p>{{ app()->getLocale() == 'id' ? 'Berbagai jenjang pendidikan berkualitas di ' . \App\Helpers\SettingHelper::getInstitutionName() . ' untuk membentuk generasi unggul.' : 'Quality education at various levels at ' . \App\Helpers\SettingHelper::getInstitutionNameEn() . ' to shape an excellent generation.' }}</p>
            </div>

            <!-- Tabs Navigation -->
            <ul class="nav nav-pills mb-4 justify-content-center edu-unit-tabs fade-in" id="educationTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link {{ $activeTab == 'formal' ? 'active' : '' }}" id="formal-tab" data-bs-toggle="pill" data-bs-target="#formal" type="button" role="tab" aria-controls="formal" aria-selected="{{ $activeTab == 'formal' ? 'true' : 'false' }}">
                        {{ app()->getLocale() == 'id' ? 'Pendidikan Formal' : 'Formal Education' }}
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link {{ $activeTab == 'non-formal' ? 'active' : '' }}" id="non-formal-tab" data-bs-toggle="pill" data-bs-target="#non-formal" type="button" role="tab" aria-controls="non-formal" aria-selected="{{ $activeTab == 'non-formal' ? 'true' : 'false' }}">
                        {{ app()->getLocale() == 'id' ? 'Pendidikan Non-Formal' : 'Non-Formal Education' }}
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="educationTabsContent">
                <!-- Formal Education Tab -->
                <div class="tab-pane fade {{ $activeTab == 'formal' ? 'show active' : '' }}" id="formal" role="tabpanel" aria-labelledby="formal-tab">
                    <div class="row">
                        @forelse($formalUnits as $unit)
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="edu-unit-card fade-in" style="transition-delay: {{ $loop->iteration * 0.1 }}s">
                                    <div class="edu-unit-image">
                                        @if($unit->image)
                                            <img src="{{ asset('storage/' . $unit->image) }}" alt="{{ app()->getLocale() == 'id' ? $unit->name : $unit->name_en }}" class="img-fluid">
                                        @else
                                            <img src="{{ asset('images/education-placeholder.jpg') }}" alt="{{ app()->getLocale() == 'id' ? $unit->name : $unit->name_en }}" class="img-fluid">
                                        @endif
                                        <div class="edu-unit-level">
                                            <span>{{ app()->getLocale() == 'id' ? $unit->level : $unit->level_en }}</span>
                                        </div>
                                    </div>
                                    <div class="edu-unit-info">
                                        <h3>{{ app()->getLocale() == 'id' ? $unit->name : $unit->name_en }}</h3>
                                        <div class="edu-unit-desc">
                                            {!! \Illuminate\Support\Str::limit(strip_tags(app()->getLocale() == 'id' ? $unit->description : $unit->description_en), 120) !!}
                                        </div>
                                        <div class="edu-unit-features">
                                            <div class="feature-item">
                                                <i class="fas fa-graduation-cap"></i>
                                                <span>{{ app()->getLocale() == 'id' ? 'Kurikulum Terintegrasi' : 'Integrated Curriculum' }}</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-users"></i>
                                                <span>{{ app()->getLocale() == 'id' ? 'Pengajar Profesional' : 'Professional Teachers' }}</span>
                                            </div>
                                        </div>
                                        <button class="btn btn-outline-success mt-3 edu-unit-details-btn" data-bs-toggle="modal" data-bs-target="#eduUnitModal{{ $unit->id }}">
                                            {{ app()->getLocale() == 'id' ? 'Lihat Detail' : 'View Details' }}
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Education Unit Modal -->
                            <div class="modal fade" id="eduUnitModal{{ $unit->id }}" tabindex="-1" aria-labelledby="eduUnitModalLabel{{ $unit->id }}" aria-hidden="true">
                                <div class="modal-dialog modal-lg modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="eduUnitModalLabel{{ $unit->id }}">{{ app()->getLocale() == 'id' ? $unit->name : $unit->name_en }}</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row">
                                                <div class="col-md-5 mb-4 mb-md-0">
                                                    <div class="edu-unit-modal-image">
                                                        @if($unit->image)
                                                            <img src="{{ asset('storage/' . $unit->image) }}" alt="{{ app()->getLocale() == 'id' ? $unit->name : $unit->name_en }}" class="img-fluid rounded">
                                                        @else
                                                            <img src="{{ asset('images/education-placeholder.jpg') }}" alt="{{ app()->getLocale() == 'id' ? $unit->name : $unit->name_en }}" class="img-fluid rounded">
                                                        @endif
                                                        <div class="edu-unit-modal-level">
                                                            {{ app()->getLocale() == 'id' ? $unit->level : $unit->level_en }}
                                                        </div>
                                                    </div>
                                                    <div class="edu-unit-modal-contact mt-3">
                                                        @if($unit->address)
                                                            <div class="mb-2"><i class="fas fa-map-marker-alt me-2"></i> {{ $unit->address }}</div>
                                                        @endif
                                                        @if($unit->phone)
                                                            <div class="mb-2"><i class="fas fa-phone me-2"></i> {{ $unit->phone }}</div>
                                                        @endif
                                                        @if($unit->email)
                                                            <div class="mb-2"><i class="fas fa-envelope me-2"></i> {{ $unit->email }}</div>
                                                        @endif
                                                        @if($unit->website)
                                                            <div class="mb-2"><i class="fas fa-globe me-2"></i> <a href="{{ $unit->website }}" target="_blank">{{ $unit->website }}</a></div>
                                                        @endif
                                                    </div>
                                                </div>
                                                <div class="col-md-7">
                                                    <h4 class="mb-1">{{ app()->getLocale() == 'id' ? $unit->name : $unit->name_en }}</h4>
                                                    <p class="text-muted mb-3">{{ app()->getLocale() == 'id' ? $unit->level : $unit->level_en }}</p>

                                                    <!-- Tabs for additional information -->
                                                    <ul class="nav nav-tabs" id="unitTabs{{ $unit->id }}" role="tablist">
                                                        <li class="nav-item" role="presentation">
                                                            <button class="nav-link active" id="overview-tab{{ $unit->id }}" data-bs-toggle="tab" data-bs-target="#overview{{ $unit->id }}" type="button" role="tab" aria-controls="overview{{ $unit->id }}" aria-selected="true">
                                                                {{ app()->getLocale() == 'id' ? 'Gambaran Umum' : 'Overview' }}
                                                            </button>
                                                        </li>
                                                        <li class="nav-item" role="presentation">
                                                            <button class="nav-link" id="facilities-tab{{ $unit->id }}" data-bs-toggle="tab" data-bs-target="#facilities{{ $unit->id }}" type="button" role="tab" aria-controls="facilities{{ $unit->id }}" aria-selected="false">
                                                                {{ app()->getLocale() == 'id' ? 'Fasilitas' : 'Facilities' }}
                                                            </button>
                                                        </li>

                                                    </ul>

                                                    <div class="tab-content pt-3" id="unitTabsContent{{ $unit->id }}">
                                                        <!-- Overview Tab -->
                                                        <div class="tab-pane fade show active" id="overview{{ $unit->id }}" role="tabpanel" aria-labelledby="overview-tab{{ $unit->id }}">
                                                            <div class="edu-unit-modal-desc">
                                                                {!! app()->getLocale() == 'id' ? $unit->description : $unit->description_en !!}
                                                            </div>
                                                        </div>

                                                        <!-- Facilities Tab -->
                                                        <div class="tab-pane fade" id="facilities{{ $unit->id }}" role="tabpanel" aria-labelledby="facilities-tab{{ $unit->id }}">
                                                            <div class="edu-unit-modal-facilities">
                                                                {!! app()->getLocale() == 'id' ? $unit->facilities : $unit->facilities_en !!}
                                                            </div>
                                                        </div>


                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <a href="{{ route('education-units.show', $unit->id) }}" class="btn btn-success">{{ app()->getLocale() == 'id' ? 'Kunjungi' : 'Visit' }} <i class="fas fa-arrow-right ms-1"></i></a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="col-12 text-center">
                                <div class="alert alert-info">
                                    {{ app()->getLocale() == 'id' ? 'Belum ada unit pendidikan formal yang tersedia.' : 'No formal education units available yet.' }}
                                </div>
                            </div>
                        @endforelse
                    </div>
                </div>

                <!-- Non-Formal Education Tab -->
                <div class="tab-pane fade {{ $activeTab == 'non-formal' ? 'show active' : '' }}" id="non-formal" role="tabpanel" aria-labelledby="non-formal-tab">
                    <div class="row">
                        @forelse($nonFormalUnits as $unit)
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="edu-unit-card non-formal fade-in" style="transition-delay: {{ $loop->iteration * 0.1 }}s">
                                    <div class="edu-unit-image">
                                        @if($unit->image)
                                            <img src="{{ asset('storage/' . $unit->image) }}" alt="{{ app()->getLocale() == 'id' ? $unit->name : $unit->name_en }}" class="img-fluid">
                                        @else
                                            <img src="{{ asset('images/education-placeholder.jpg') }}" alt="{{ app()->getLocale() == 'id' ? $unit->name : $unit->name_en }}" class="img-fluid">
                                        @endif
                                        <div class="edu-unit-level">
                                            <span>{{ app()->getLocale() == 'id' ? $unit->level : $unit->level_en }}</span>
                                        </div>
                                    </div>
                                    <div class="edu-unit-info">
                                        <h3>{{ app()->getLocale() == 'id' ? $unit->name : $unit->name_en }}</h3>
                                        <div class="edu-unit-desc">
                                            {!! \Illuminate\Support\Str::limit(strip_tags(app()->getLocale() == 'id' ? $unit->description : $unit->description_en), 120) !!}
                                        </div>
                                        <div class="edu-unit-features">
                                            <div class="feature-item">
                                                <i class="fas fa-book"></i>
                                                <span>{{ app()->getLocale() == 'id' ? 'Program Unggulan' : 'Featured Program' }}</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-certificate"></i>
                                                <span>{{ app()->getLocale() == 'id' ? 'Sertifikasi' : 'Certification' }}</span>
                                            </div>
                                        </div>
                                        <button class="btn btn-outline-success mt-3 edu-unit-details-btn" data-bs-toggle="modal" data-bs-target="#eduUnitModalNF{{ $unit->id }}">
                                            {{ app()->getLocale() == 'id' ? 'Lihat Detail' : 'View Details' }}
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Education Unit Modal -->
                            <div class="modal fade" id="eduUnitModalNF{{ $unit->id }}" tabindex="-1" aria-labelledby="eduUnitModalNFLabel{{ $unit->id }}" aria-hidden="true">
                                <div class="modal-dialog modal-lg modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="eduUnitModalNFLabel{{ $unit->id }}">{{ app()->getLocale() == 'id' ? $unit->name : $unit->name_en }}</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row">
                                                <div class="col-md-5 mb-4 mb-md-0">
                                                    <div class="edu-unit-modal-image">
                                                        @if($unit->image)
                                                            <img src="{{ asset('storage/' . $unit->image) }}" alt="{{ app()->getLocale() == 'id' ? $unit->name : $unit->name_en }}" class="img-fluid rounded">
                                                        @else
                                                            <img src="{{ asset('images/education-placeholder.jpg') }}" alt="{{ app()->getLocale() == 'id' ? $unit->name : $unit->name_en }}" class="img-fluid rounded">
                                                        @endif
                                                        <div class="edu-unit-modal-level">
                                                            {{ app()->getLocale() == 'id' ? $unit->level : $unit->level_en }}
                                                        </div>
                                                    </div>
                                                    <div class="edu-unit-modal-contact mt-3">
                                                        @if($unit->address)
                                                            <div class="mb-2"><i class="fas fa-map-marker-alt me-2"></i> {{ $unit->address }}</div>
                                                        @endif
                                                        @if($unit->phone)
                                                            <div class="mb-2"><i class="fas fa-phone me-2"></i> {{ $unit->phone }}</div>
                                                        @endif
                                                        @if($unit->email)
                                                            <div class="mb-2"><i class="fas fa-envelope me-2"></i> {{ $unit->email }}</div>
                                                        @endif
                                                        @if($unit->website)
                                                            <div class="mb-2"><i class="fas fa-globe me-2"></i> <a href="{{ $unit->website }}" target="_blank">{{ $unit->website }}</a></div>
                                                        @endif
                                                    </div>
                                                </div>
                                                <div class="col-md-7">
                                                    <h4 class="mb-1">{{ app()->getLocale() == 'id' ? $unit->name : $unit->name_en }}</h4>
                                                    <p class="text-muted mb-3">{{ app()->getLocale() == 'id' ? $unit->level : $unit->level_en }}</p>

                                                    <!-- Tabs for additional information -->
                                                    <ul class="nav nav-tabs" id="unitTabsNF{{ $unit->id }}" role="tablist">
                                                        <li class="nav-item" role="presentation">
                                                            <button class="nav-link active" id="overview-tabNF{{ $unit->id }}" data-bs-toggle="tab" data-bs-target="#overviewNF{{ $unit->id }}" type="button" role="tab" aria-controls="overviewNF{{ $unit->id }}" aria-selected="true">
                                                                {{ app()->getLocale() == 'id' ? 'Gambaran Umum' : 'Overview' }}
                                                            </button>
                                                        </li>
                                                        <li class="nav-item" role="presentation">
                                                            <button class="nav-link" id="facilities-tabNF{{ $unit->id }}" data-bs-toggle="tab" data-bs-target="#facilitiesNF{{ $unit->id }}" type="button" role="tab" aria-controls="facilitiesNF{{ $unit->id }}" aria-selected="false">
                                                                {{ app()->getLocale() == 'id' ? 'Fasilitas' : 'Facilities' }}
                                                            </button>
                                                        </li>

                                                    </ul>

                                                    <div class="tab-content pt-3" id="unitTabsContentNF{{ $unit->id }}">
                                                        <!-- Overview Tab -->
                                                        <div class="tab-pane fade show active" id="overviewNF{{ $unit->id }}" role="tabpanel" aria-labelledby="overview-tabNF{{ $unit->id }}">
                                                            <div class="edu-unit-modal-desc">
                                                                {!! app()->getLocale() == 'id' ? $unit->description : $unit->description_en !!}
                                                            </div>
                                                        </div>

                                                        <!-- Facilities Tab -->
                                                        <div class="tab-pane fade" id="facilitiesNF{{ $unit->id }}" role="tabpanel" aria-labelledby="facilities-tabNF{{ $unit->id }}">
                                                            <div class="edu-unit-modal-facilities">
                                                                {!! app()->getLocale() == 'id' ? $unit->facilities : $unit->facilities_en !!}
                                                            </div>
                                                        </div>


                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <a href="{{ route('education-units.show', $unit->id) }}" class="btn btn-success">{{ app()->getLocale() == 'id' ? 'Kunjungi' : 'Visit' }} <i class="fas fa-arrow-right ms-1"></i></a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="col-12 text-center">
                                <div class="alert alert-info">
                                    {{ app()->getLocale() == 'id' ? 'Belum ada unit pendidikan non-formal yang tersedia.' : 'No non-formal education units available yet.' }}
                                </div>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
    /* Education Unit Tabs Styles */
    .edu-unit-tabs {
        margin-bottom: 40px;
    }

    .edu-unit-tabs .nav-link {
        padding: 12px 25px;
        border-radius: 30px;
        font-weight: 600;
        color: #333;
        background-color: #f8f9fa;
        margin: 0 5px;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .edu-unit-tabs .nav-link:hover {
        background-color: #e9ecef;
        transform: translateY(-3px);
    }

    .edu-unit-tabs .nav-link.active {
        background-color: #198754;
        color: white;
        box-shadow: 0 5px 15px rgba(25, 135, 84, 0.2);
    }

    /* Education Unit Card Styles */
    .edu-unit-card {
        background-color: #fff;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative;
    }

    .edu-unit-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    }

    .edu-unit-card.non-formal {
        border-top: 4px solid #c9a55a;
    }

    .edu-unit-image {
        position: relative;
        overflow: hidden;
    }

    .edu-unit-image img {
        width: 100%;
        height: 220px;
        object-fit: cover;
        transition: all 0.5s ease;
    }

    .edu-unit-card:hover .edu-unit-image img {
        transform: scale(1.05);
    }

    .edu-unit-level {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(25, 135, 84, 0.8);
        color: #fff;
        padding: 8px 15px;
        font-size: 14px;
        font-weight: 600;
        text-align: center;
    }

    .edu-unit-card.non-formal .edu-unit-level {
        background-color: rgba(201, 165, 90, 0.8);
    }

    .edu-unit-info {
        padding: 20px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
    }

    .edu-unit-info h3 {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #333;
    }

    .edu-unit-desc {
        font-size: 14px;
        color: #666;
        margin-bottom: 15px;
        flex-grow: 1;
    }

    .edu-unit-features {
        margin-bottom: 15px;
    }

    .feature-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 14px;
    }

    .feature-item i {
        color: #198754;
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    .edu-unit-card.non-formal .feature-item i {
        color: #c9a55a;
    }

    .edu-unit-details-btn {
        align-self: flex-start;
        transition: all 0.3s ease;
    }

    .edu-unit-details-btn:hover {
        background-color: #198754;
        color: #fff;
    }

    .edu-unit-card.non-formal .edu-unit-details-btn:hover {
        background-color: #c9a55a;
        border-color: #c9a55a;
    }

    /* Modal Styles */
    .edu-unit-modal-image {
        position: relative;
        overflow: hidden;
        border-radius: 10px;
    }

    .edu-unit-modal-image img {
        width: 100%;
        height: auto;
        object-fit: cover;
    }

    .edu-unit-modal-level {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(25, 135, 84, 0.8);
        color: #fff;
        padding: 8px 15px;
        font-size: 14px;
        font-weight: 600;
        text-align: center;
    }

    .edu-unit-modal-contact {
        font-size: 14px;
    }

    .edu-unit-modal-contact i {
        color: #198754;
        width: 20px;
        text-align: center;
    }

    .edu-unit-modal-desc,
    .edu-unit-modal-facilities,
    .edu-unit-modal-curriculum {
        font-size: 14px;
        line-height: 1.6;
    }

    /* Responsive Styles */
    @media (max-width: 991.98px) {
        .edu-unit-tabs .nav-link {
            padding: 10px 20px;
            font-size: 14px;
        }
    }

    @media (max-width: 767.98px) {
        .edu-unit-tabs .nav-link {
            padding: 8px 15px;
            font-size: 13px;
            margin-bottom: 10px;
        }

        .edu-unit-info h3 {
            font-size: 18px;
        }

        .edu-unit-image img {
            height: 180px;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize animations for education unit cards
        const eduUnitCards = document.querySelectorAll('.edu-unit-card');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('active');
                }
            });
        }, {
            threshold: 0.1
        });

        eduUnitCards.forEach(card => {
            observer.observe(card);
        });

        // Initialize animations for section title
        const sectionTitle = document.querySelector('.section-title');
        if (sectionTitle) {
            observer.observe(sectionTitle);
        }

        // Initialize tab animations
        const tabLinks = document.querySelectorAll('.nav-link[data-bs-toggle="tab"]');

        tabLinks.forEach(link => {
            link.addEventListener('click', function() {
                // Get the target tab content
                const targetId = this.getAttribute('data-bs-target');
                const targetPane = document.querySelector(targetId);

                // Add a slight delay to allow the Bootstrap tab transition to complete
                setTimeout(() => {
                    // Trigger animations for elements inside the active tab
                    const animElements = targetPane.querySelectorAll('.fade-in, .fade-in-left, .fade-in-right, .zoom-in');
                    animElements.forEach(element => {
                        // Reset animation by removing and re-adding the class
                        element.classList.remove('active');

                        // Force a reflow to restart the animation
                        void element.offsetWidth;

                        // Add the active class to start the animation
                        element.classList.add('active');
                    });
                }, 150);
            });
        });

        // Update URL when tab changes
        const educationTabs = document.getElementById('educationTabs');
        if (educationTabs) {
            const tabLinks = educationTabs.querySelectorAll('.nav-link');

            tabLinks.forEach(link => {
                link.addEventListener('shown.bs.tab', function(event) {
                    const tabId = event.target.id;
                    let tabType = 'formal';

                    if (tabId === 'non-formal-tab') {
                        tabType = 'non-formal';
                    }

                    // Update URL without reloading the page
                    const url = new URL(window.location);
                    url.searchParams.set('tab', tabType);
                    window.history.pushState({}, '', url);
                });
            });
        }
    });
</script>
@endpush