@extends('admin.layouts.app')

@section('title', 'Create Curriculum Item')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Create Curriculum Item</h1>
        <a href="{{ route('admin.curricula.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="{{ route('admin.curricula.store') }}" method="POST" enctype="multipart/form-data">
                @csrf

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="edu_type" class="form-label">Education Type <span class="text-danger">*</span></label>
                            <select class="form-select @error('edu_type') is-invalid @enderror" id="edu_type" name="edu_type" required>
                                <option value="">Select Type</option>
                                <option value="formal" {{ old('edu_type') == 'formal' ? 'selected' : '' }}>Formal</option>
                                <option value="non-formal" {{ old('edu_type') == 'non-formal' ? 'selected' : '' }}>Non-Formal</option>
                            </select>
                            @error('edu_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="title" class="form-label">Title (Indonesian) <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" id="title" name="title" value="{{ old('title') }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="title_en" class="form-label">Title (English) <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title_en') is-invalid @enderror" id="title_en" name="title_en" value="{{ old('title_en') }}" required>
                            @error('title_en')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Dynamic fields based on education type -->
                <div id="formal-fields" style="display: none;">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="education_unit_id" class="form-label">Education Unit</label>
                                <select class="form-select @error('education_unit_id') is-invalid @enderror" id="education_unit_id" name="education_unit_id">
                                    <option value="">Select Education Unit</option>
                                    @foreach($educationUnits as $unit)
                                        @if($unit->edu_type == 'formal')
                                            <option value="{{ $unit->id }}" {{ old('education_unit_id') == $unit->id ? 'selected' : '' }}>{{ $unit->name }}</option>
                                        @endif
                                    @endforeach
                                </select>
                                @error('education_unit_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="national_curriculum" class="form-label">National Curriculum (Indonesian)</label>
                                <textarea class="form-control @error('national_curriculum') is-invalid @enderror" id="national_curriculum" name="national_curriculum" rows="4">{{ old('national_curriculum') }}</textarea>
                                @error('national_curriculum')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="national_curriculum_en" class="form-label">National Curriculum (English)</label>
                                <textarea class="form-control @error('national_curriculum_en') is-invalid @enderror" id="national_curriculum_en" name="national_curriculum_en" rows="4">{{ old('national_curriculum_en') }}</textarea>
                                @error('national_curriculum_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="general_subjects" class="form-label">General Subjects (Indonesian)</label>
                                <textarea class="form-control @error('general_subjects') is-invalid @enderror" id="general_subjects" name="general_subjects" rows="4">{{ old('general_subjects') }}</textarea>
                                @error('general_subjects')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="general_subjects_en" class="form-label">General Subjects (English)</label>
                                <textarea class="form-control @error('general_subjects_en') is-invalid @enderror" id="general_subjects_en" name="general_subjects_en" rows="4">{{ old('general_subjects_en') }}</textarea>
                                @error('general_subjects_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="local_content" class="form-label">Local Content (Indonesian)</label>
                                <textarea class="form-control @error('local_content') is-invalid @enderror" id="local_content" name="local_content" rows="4">{{ old('local_content') }}</textarea>
                                @error('local_content')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="local_content_en" class="form-label">Local Content (English)</label>
                                <textarea class="form-control @error('local_content_en') is-invalid @enderror" id="local_content_en" name="local_content_en" rows="4">{{ old('local_content_en') }}</textarea>
                                @error('local_content_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="supporting_activities" class="form-label">Supporting Activities (Indonesian)</label>
                                <textarea class="form-control @error('supporting_activities') is-invalid @enderror" id="supporting_activities" name="supporting_activities" rows="4">{{ old('supporting_activities') }}</textarea>
                                @error('supporting_activities')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="supporting_activities_en" class="form-label">Supporting Activities (English)</label>
                                <textarea class="form-control @error('supporting_activities_en') is-invalid @enderror" id="supporting_activities_en" name="supporting_activities_en" rows="4">{{ old('supporting_activities_en') }}</textarea>
                                @error('supporting_activities_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="language_of_instruction_formal" class="form-label">Language of Instruction (Indonesian)</label>
                                <textarea class="form-control @error('language_of_instruction') is-invalid @enderror" id="language_of_instruction_formal" name="language_of_instruction" rows="4">{{ old('language_of_instruction') }}</textarea>
                                @error('language_of_instruction')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="language_of_instruction_en_formal" class="form-label">Language of Instruction (English)</label>
                                <textarea class="form-control @error('language_of_instruction_en') is-invalid @enderror" id="language_of_instruction_en_formal" name="language_of_instruction_en" rows="4">{{ old('language_of_instruction_en') }}</textarea>
                                @error('language_of_instruction_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="teaching_methods_formal" class="form-label">Teaching Methods (Indonesian)</label>
                                <textarea class="form-control @error('teaching_methods') is-invalid @enderror" id="teaching_methods_formal" name="teaching_methods" rows="4">{{ old('teaching_methods') }}</textarea>
                                @error('teaching_methods')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="teaching_methods_en_formal" class="form-label">Teaching Methods (English)</label>
                                <textarea class="form-control @error('teaching_methods_en') is-invalid @enderror" id="teaching_methods_en_formal" name="teaching_methods_en" rows="4">{{ old('teaching_methods_en') }}</textarea>
                                @error('teaching_methods_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="assessment_evaluation" class="form-label">Assessment and Evaluation (Indonesian)</label>
                                <textarea class="form-control @error('assessment_evaluation') is-invalid @enderror" id="assessment_evaluation" name="assessment_evaluation" rows="4">{{ old('assessment_evaluation') }}</textarea>
                                @error('assessment_evaluation')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="assessment_evaluation_en" class="form-label">Assessment and Evaluation (English)</label>
                                <textarea class="form-control @error('assessment_evaluation_en') is-invalid @enderror" id="assessment_evaluation_en" name="assessment_evaluation_en" rows="4">{{ old('assessment_evaluation_en') }}</textarea>
                                @error('assessment_evaluation_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="graduation_certificates" class="form-label">Graduation Certificates (Indonesian)</label>
                                <textarea class="form-control @error('graduation_certificates') is-invalid @enderror" id="graduation_certificates" name="graduation_certificates" rows="4">{{ old('graduation_certificates') }}</textarea>
                                @error('graduation_certificates')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="graduation_certificates_en" class="form-label">Graduation Certificates (English)</label>
                                <textarea class="form-control @error('graduation_certificates_en') is-invalid @enderror" id="graduation_certificates_en" name="graduation_certificates_en" rows="4">{{ old('graduation_certificates_en') }}</textarea>
                                @error('graduation_certificates_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <div id="non-formal-fields" style="display: none;">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="education_unit_id" class="form-label">Education Unit</label>
                                <select class="form-select @error('education_unit_id') is-invalid @enderror" id="education_unit_id_non_formal" name="education_unit_id">
                                    <option value="">Select Education Unit</option>
                                    @foreach($educationUnits as $unit)
                                        @if($unit->edu_type == 'non-formal')
                                            <option value="{{ $unit->id }}" {{ old('education_unit_id') == $unit->id ? 'selected' : '' }}>{{ $unit->name }}</option>
                                        @endif
                                    @endforeach
                                </select>
                                @error('education_unit_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="educational_goals" class="form-label">Educational Goals (Indonesian)</label>
                                <textarea class="form-control @error('educational_goals') is-invalid @enderror" id="educational_goals" name="educational_goals" rows="4">{{ old('educational_goals') }}</textarea>
                                @error('educational_goals')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="educational_goals_en" class="form-label">Educational Goals (English)</label>
                                <textarea class="form-control @error('educational_goals_en') is-invalid @enderror" id="educational_goals_en" name="educational_goals_en" rows="4">{{ old('educational_goals_en') }}</textarea>
                                @error('educational_goals_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="core_textbooks_studied" class="form-label">Core Textbooks Studied (Indonesian)</label>
                                <textarea class="form-control @error('core_textbooks_studied') is-invalid @enderror" id="core_textbooks_studied" name="core_textbooks_studied" rows="4">{{ old('core_textbooks_studied') }}</textarea>
                                @error('core_textbooks_studied')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="core_textbooks_studied_en" class="form-label">Core Textbooks Studied (English)</label>
                                <textarea class="form-control @error('core_textbooks_studied_en') is-invalid @enderror" id="core_textbooks_studied_en" name="core_textbooks_studied_en" rows="4">{{ old('core_textbooks_studied_en') }}</textarea>
                                @error('core_textbooks_studied_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="fields_of_study" class="form-label">Fields of Study (Indonesian)</label>
                                <textarea class="form-control @error('fields_of_study') is-invalid @enderror" id="fields_of_study" name="fields_of_study" rows="4">{{ old('fields_of_study') }}</textarea>
                                @error('fields_of_study')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="fields_of_study_en" class="form-label">Fields of Study (English)</label>
                                <textarea class="form-control @error('fields_of_study_en') is-invalid @enderror" id="fields_of_study_en" name="fields_of_study_en" rows="4">{{ old('fields_of_study_en') }}</textarea>
                                @error('fields_of_study_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="class_levels" class="form-label">Class Levels (Indonesian)</label>
                                <textarea class="form-control @error('class_levels') is-invalid @enderror" id="class_levels" name="class_levels" rows="4">{{ old('class_levels') }}</textarea>
                                @error('class_levels')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="class_levels_en" class="form-label">Class Levels (English)</label>
                                <textarea class="form-control @error('class_levels_en') is-invalid @enderror" id="class_levels_en" name="class_levels_en" rows="4">{{ old('class_levels_en') }}</textarea>
                                @error('class_levels_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="study_schedule" class="form-label">Study Schedule (Indonesian)</label>
                                <textarea class="form-control @error('study_schedule') is-invalid @enderror" id="study_schedule" name="study_schedule" rows="4">{{ old('study_schedule') }}</textarea>
                                @error('study_schedule')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="study_schedule_en" class="form-label">Study Schedule (English)</label>
                                <textarea class="form-control @error('study_schedule_en') is-invalid @enderror" id="study_schedule_en" name="study_schedule_en" rows="4">{{ old('study_schedule_en') }}</textarea>
                                @error('study_schedule_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="teaching_methods_nonformal" class="form-label">Teaching Methods (Indonesian)</label>
                                <textarea class="form-control @error('teaching_methods') is-invalid @enderror" id="teaching_methods_nonformal" name="teaching_methods" rows="4">{{ old('teaching_methods') }}</textarea>
                                @error('teaching_methods')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="teaching_methods_en_nonformal" class="form-label">Teaching Methods (English)</label>
                                <textarea class="form-control @error('teaching_methods_en') is-invalid @enderror" id="teaching_methods_en_nonformal" name="teaching_methods_en" rows="4">{{ old('teaching_methods_en') }}</textarea>
                                @error('teaching_methods_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="language_of_instruction_nonformal" class="form-label">Language of Instruction (Indonesian)</label>
                                <textarea class="form-control @error('language_of_instruction') is-invalid @enderror" id="language_of_instruction_nonformal" name="language_of_instruction" rows="4">{{ old('language_of_instruction') }}</textarea>
                                @error('language_of_instruction')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="language_of_instruction_en_nonformal" class="form-label">Language of Instruction (English)</label>
                                <textarea class="form-control @error('language_of_instruction_en') is-invalid @enderror" id="language_of_instruction_en_nonformal" name="language_of_instruction_en" rows="4">{{ old('language_of_instruction_en') }}</textarea>
                                @error('language_of_instruction_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="graduation_certificate" class="form-label">Graduation Certificate (Indonesian)</label>
                                <textarea class="form-control @error('graduation_certificate') is-invalid @enderror" id="graduation_certificate" name="graduation_certificate" rows="4">{{ old('graduation_certificate') }}</textarea>
                                @error('graduation_certificate')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="graduation_certificate_en" class="form-label">Graduation Certificate (English)</label>
                                <textarea class="form-control @error('graduation_certificate_en') is-invalid @enderror" id="graduation_certificate_en" name="graduation_certificate_en" rows="4">{{ old('graduation_certificate_en') }}</textarea>
                                @error('graduation_certificate_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="order" class="form-label">Order</label>
                            <input type="number" class="form-control @error('order') is-invalid @enderror" id="order" name="order" value="{{ old('order') }}">
                            @error('order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>



                <!-- Hidden fields to ensure data is always submitted -->
                <div style="display: none;">
                    <input type="hidden" name="supporting_activities_hidden" id="supporting_activities_hidden">
                    <input type="hidden" name="supporting_activities_en_hidden" id="supporting_activities_en_hidden">
                    <input type="hidden" name="language_of_instruction_hidden" id="language_of_instruction_hidden">
                    <input type="hidden" name="language_of_instruction_en_hidden" id="language_of_instruction_en_hidden">
                    <input type="hidden" name="teaching_methods_hidden" id="teaching_methods_hidden">
                    <input type="hidden" name="teaching_methods_en_hidden" id="teaching_methods_en_hidden">
                    <input type="hidden" name="assessment_evaluation_hidden" id="assessment_evaluation_hidden">
                    <input type="hidden" name="assessment_evaluation_en_hidden" id="assessment_evaluation_en_hidden">
                    <input type="hidden" name="graduation_certificates_hidden" id="graduation_certificates_hidden">
                    <input type="hidden" name="graduation_certificates_en_hidden" id="graduation_certificates_en_hidden">
                </div>

                <div class="d-grid">
                    <button type="submit" class="btn btn-primary" id="submit-btn">
                        <i class="fas fa-save"></i> Save
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Show/hide fields based on education type
        const eduTypeSelect = document.getElementById('edu_type');
        const formalFields = document.getElementById('formal-fields');
        const nonFormalFields = document.getElementById('non-formal-fields');
        const formalUnitSelect = document.getElementById('education_unit_id');
        const nonFormalUnitSelect = document.getElementById('education_unit_id_non_formal');

        function toggleFields() {
            if (eduTypeSelect.value === 'formal') {
                formalFields.style.display = 'block';
                nonFormalFields.style.display = 'none';

                // Disable all non-formal fields
                const nonFormalInputs = nonFormalFields.querySelectorAll('input, textarea, select');
                nonFormalInputs.forEach(input => {
                    input.disabled = true;
                });

                // Enable all formal fields
                const formalInputs = formalFields.querySelectorAll('input, textarea, select');
                formalInputs.forEach(input => {
                    input.disabled = false;
                });

                // Ensure the correct education_unit_id is used
                if (nonFormalUnitSelect) {
                    nonFormalUnitSelect.disabled = true;
                }
                if (formalUnitSelect) {
                    formalUnitSelect.disabled = false;
                }
            } else if (eduTypeSelect.value === 'non-formal') {
                formalFields.style.display = 'none';
                nonFormalFields.style.display = 'block';

                // Disable all formal fields
                const formalInputs = formalFields.querySelectorAll('input, textarea, select');
                formalInputs.forEach(input => {
                    input.disabled = true;
                });

                // Enable all non-formal fields
                const nonFormalInputs = nonFormalFields.querySelectorAll('input, textarea, select');
                nonFormalInputs.forEach(input => {
                    input.disabled = false;
                });

                // Ensure the correct education_unit_id is used
                if (formalUnitSelect) {
                    formalUnitSelect.disabled = true;
                }
                if (nonFormalUnitSelect) {
                    nonFormalUnitSelect.disabled = false;
                }
            } else {
                formalFields.style.display = 'none';
                nonFormalFields.style.display = 'none';

                // Disable all fields
                const allInputs = document.querySelectorAll('#formal-fields input, #formal-fields textarea, #formal-fields select, #non-formal-fields input, #non-formal-fields textarea, #non-formal-fields select');
                allInputs.forEach(input => {
                    input.disabled = true;
                });

                // Disable both selects when no type is selected
                if (formalUnitSelect) {
                    formalUnitSelect.disabled = true;
                }
                if (nonFormalUnitSelect) {
                    nonFormalUnitSelect.disabled = true;
                }
            }
        }

        eduTypeSelect.addEventListener('change', toggleFields);

        // Initialize on page load
        toggleFields();

        // Set up form submission
        const form = document.querySelector('form');
        const submitBtn = document.getElementById('submit-btn');

        // Set up hidden fields to ensure data is always submitted
        form.addEventListener('submit', function(e) {
            // Copy values from visible fields to hidden fields
            document.getElementById('supporting_activities_hidden').value = document.getElementById('supporting_activities').value;
            document.getElementById('supporting_activities_en_hidden').value = document.getElementById('supporting_activities_en').value;

            // For fields that have different IDs in formal and non-formal sections
            if (eduTypeSelect.value === 'formal') {
                document.getElementById('language_of_instruction_hidden').value = document.getElementById('language_of_instruction_formal').value;
                document.getElementById('language_of_instruction_en_hidden').value = document.getElementById('language_of_instruction_en_formal').value;
                document.getElementById('teaching_methods_hidden').value = document.getElementById('teaching_methods_formal').value;
                document.getElementById('teaching_methods_en_hidden').value = document.getElementById('teaching_methods_en_formal').value;
            } else {
                document.getElementById('language_of_instruction_hidden').value = document.getElementById('language_of_instruction_nonformal').value;
                document.getElementById('language_of_instruction_en_hidden').value = document.getElementById('language_of_instruction_en_nonformal').value;
                document.getElementById('teaching_methods_hidden').value = document.getElementById('teaching_methods_nonformal').value;
                document.getElementById('teaching_methods_en_hidden').value = document.getElementById('teaching_methods_en_nonformal').value;
            }

            document.getElementById('assessment_evaluation_hidden').value = document.getElementById('assessment_evaluation').value;
            document.getElementById('assessment_evaluation_en_hidden').value = document.getElementById('assessment_evaluation_en').value;
            document.getElementById('graduation_certificates_hidden').value = document.getElementById('graduation_certificates').value;
            document.getElementById('graduation_certificates_en_hidden').value = document.getElementById('graduation_certificates_en').value;
        });
    });
</script>
@endpush
