@extends('admin.layouts.app')

@section('title', 'Profil')

@section('content')
    <div class="container-fluid px-4">
        <h1 class="mt-4">Profil</h1>
        <ol class="breadcrumb mb-4">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item active">Profil</li>
        </ol>

        <div class="row">
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-user me-1"></i>
                        Edit Profil
                    </div>
                    <div class="card-body">
                        <form action="{{ route('admin.profile.update') }}" method="POST">
                            @csrf
                            @method('PUT')

                            <div class="mb-3">
                                <label for="name" class="form-label">Nama</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $user->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                @if(auth()->user()->isAdmin())
                                <input type="text" class="form-control @error('username') is-invalid @enderror" id="username" name="username" value="{{ old('username', $user->username) }}" required>
                                @else
                                <input type="text" class="form-control" id="username" value="{{ $user->username }}" disabled readonly>
                                <input type="hidden" name="username" value="{{ $user->username }}">
                                <div class="form-text text-muted">Username hanya dapat diubah oleh administrator.</div>
                                @endif
                                @error('username')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email', $user->email) }}" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="role" class="form-label">Peran</label>
                                <input type="text" class="form-control" id="role" value="{{ ucfirst($user->role) }}" disabled>
                            </div>

                            <div class="mb-3">
                                <label for="telegram_id" class="form-label">Telegram ID</label>
                                <input type="text" class="form-control @error('telegram_id') is-invalid @enderror" id="telegram_id" name="telegram_id" value="{{ old('telegram_id', $user->telegram_id) }}">
                                <div class="form-text">ID Telegram Anda untuk integrasi notifikasi. Biarkan kosong jika tidak digunakan.</div>
                                @error('telegram_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                            <a href="{{ route('admin.profile.change-password') }}" class="btn btn-outline-secondary ms-2">Ganti Password</a>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-info-circle me-1"></i>
                        Informasi Akun
                    </div>
                    <div class="card-body">
                        <p><strong>Login Terakhir:</strong> {{ $user->last_login_at ? $user->last_login_at->format('d M Y, H:i') : 'Belum Pernah' }}</p>
                        <p><strong>Akun Dibuat:</strong> {{ $user->created_at->format('d M Y, H:i') }}</p>
                        <p><strong>Akun Diperbarui:</strong> {{ $user->updated_at->format('d M Y, H:i') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
