<?php

namespace App\Http\Controllers;

use App\Models\ActivitySchedule;
use Illuminate\Http\Request;

class ActivityScheduleController extends Controller
{
    /**
     * Display a listing of the activity schedules.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $activeTab = $request->query('tab', 'daily');

        // Validate tab parameter
        if (!in_array($activeTab, ['daily', 'weekly', 'monthly', 'yearly'])) {
            $activeTab = 'daily';
        }

        // Get schedules based on active tab
        $dailySchedules = ActivitySchedule::where('activity_type', 'daily')
            ->where('is_active', true)
            ->orderBy('start_time', 'asc')
            ->get();

        $weeklySchedules = ActivitySchedule::where('activity_type', 'weekly')
            ->where('is_active', true)
            ->orderBy('day_of_week', 'asc')
            ->orderBy('start_time', 'asc')
            ->get();

        $monthlySchedules = ActivitySchedule::where('activity_type', 'monthly')
            ->where('is_active', true)
            ->orderBy('week_number', 'asc')
            ->orderBy('start_time', 'asc')
            ->get();

        $yearlySchedules = ActivitySchedule::where('activity_type', 'yearly')
            ->where('is_active', true)
            ->orderBy('month_number', 'asc')
            ->orderBy('start_time', 'asc')
            ->get();

        // Get unique schedule categories for filtering
        $scheduleCategories = ActivitySchedule::where('is_active', true)
            ->whereNotNull('category')
            ->pluck('category')
            ->unique()
            ->values();

        return view('public.schedules.index', compact(
            'dailySchedules',
            'weeklySchedules',
            'monthlySchedules',
            'yearlySchedules',
            'activeTab',
            'scheduleCategories'
        ));
    }


}
