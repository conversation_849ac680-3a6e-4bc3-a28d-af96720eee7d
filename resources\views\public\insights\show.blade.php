@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? $insight->title : $insight->title_en)

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? 'DIRECTOR\'s INSIGHT' : 'DIRECTOR\'s INSIGHT' }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? 'Director\'s Insight' : 'Director\'s Insight' }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Insight Content -->
    <section class="section-padding">
        <div class="container">
            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8 main-content">
                    <div class="card border-0 shadow-sm mb-4 fade-in">
                        <div class="card-body p-4">
                            <h2 class="insight-title mb-3">{{ app()->getLocale() == 'id' ? $insight->title : $insight->title_en }}</h2>

                            <div class="insight-meta mb-4">
                                <span class="me-3"><i class="far fa-calendar-alt me-1"></i> {{ $insight->published_at->format('d M Y') }}</span>
                                @if($insight->is_featured)
                                    <span class="badge bg-warning">{{ app()->getLocale() == 'id' ? 'Unggulan' : 'Featured' }}</span>
                                @endif
                            </div>

                            @if($insight->image)
                                <div class="insight-image mb-4">
                                    <img src="{{ asset('storage/' . $insight->image) }}" class="img-fluid rounded w-100" alt="{{ app()->getLocale() == 'id' ? $insight->title : $insight->title_en }}">
                                </div>
                            @endif

                            <div class="insight-content summernote-content">
                                {!! app()->getLocale() == 'id' ? $insight->content : $insight->content_en !!}
                            </div>

                            <!-- Social Share -->
                            <div class="social-share mt-5 pt-4 border-top">
                                <h5 class="mb-3">{{ app()->getLocale() == 'id' ? 'Bagikan' : 'Share' }}</h5>
                                <div class="d-flex">
                                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->url()) }}" target="_blank" class="btn btn-outline-primary me-2">
                                        <i class="fab fa-facebook-f"></i>
                                    </a>
                                    <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->url()) }}&text={{ urlencode(app()->getLocale() == 'id' ? $insight->title : $insight->title_en) }}" target="_blank" class="btn btn-outline-info me-2">
                                        <i class="fab fa-twitter"></i>
                                    </a>
                                    <a href="https://wa.me/?text={{ urlencode((app()->getLocale() == 'id' ? $insight->title : $insight->title_en) . ' ' . request()->url()) }}" target="_blank" class="btn btn-outline-success me-2">
                                        <i class="fab fa-whatsapp"></i>
                                    </a>
                                    <a href="https://t.me/share/url?url={{ urlencode(request()->url()) }}&text={{ urlencode(app()->getLocale() == 'id' ? $insight->title : $insight->title_en) }}" target="_blank" class="btn btn-outline-info">
                                        <i class="fab fa-telegram-plane"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Director Profile Card -->
                    @if($director)
                    <div class="card border-0 shadow-sm mb-4 fade-in">
                        <div class="card-body p-4">
                            <div class="row align-items-center">
                                <div class="col-md-4 text-center">
                                    @if($director->image)
                                        <img src="{{ asset('storage/' . $director->image) }}" alt="{{ $director->name }}" class="img-fluid rounded-circle director-profile-image" style="width: 150px; height: 150px; object-fit: cover;">
                                    @else
                                        <img src="{{ asset('images/director-placeholder.jpg') }}" alt="Director" class="img-fluid rounded-circle director-profile-image" style="width: 150px; height: 150px; object-fit: cover;">
                                    @endif
                                </div>
                                <div class="col-md-8">
                                    <h4 class="mb-2">{{ app()->getLocale() == 'id' ? $director->name : $director->name_en }}</h4>
                                    <p class="text-muted mb-3">{{ app()->getLocale() == 'id' ? $director->position : $director->position_en }}</p>
                                    <div class="director-bio">
                                        <p>{{ app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit(strip_tags($director->bio), 200) : \Illuminate\Support\Str::limit(strip_tags($director->bio_en), 200) }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Other Insights -->
                    @if($otherInsights->isNotEmpty())
                    <div class="other-insights mt-5">
                        <h3 class="section-title mb-4">{{ app()->getLocale() == 'id' ? 'Insight Lainnya' : 'Other Insights' }}</h3>
                        <div class="row">
                            @foreach($otherInsights as $otherInsight)
                                <div class="col-md-4 mb-4">
                                    <div class="card h-100 border-0 shadow-sm hover-card">
                                        <div class="card-img-top-container">
                                            @if($otherInsight->image)
                                                <img src="{{ asset('storage/' . $otherInsight->image) }}" class="card-img-top" alt="{{ app()->getLocale() == 'id' ? $otherInsight->title : $otherInsight->title_en }}">
                                            @else
                                                <img src="{{ asset('images/insight-placeholder.jpg') }}" class="card-img-top" alt="{{ app()->getLocale() == 'id' ? $otherInsight->title : $otherInsight->title_en }}">
                                            @endif
                                        </div>
                                        <div class="card-body">
                                            <div class="small text-muted mb-2">
                                                <i class="far fa-calendar-alt me-1"></i> {{ $otherInsight->published_at->format('d M Y') }}
                                            </div>
                                            <h5 class="card-title">{{ app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit($otherInsight->title, 50) : \Illuminate\Support\Str::limit($otherInsight->title_en, 50) }}</h5>
                                            <a href="{{ route('insights.show', $otherInsight->id) }}" class="btn btn-sm btn-outline-success mt-2">{{ app()->getLocale() == 'id' ? 'Baca Selengkapnya' : 'Read More' }}</a>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4 sidebar">
                    <!-- Latest News -->
                    <div class="card border-0 shadow-sm mb-4 fade-in">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">{{ app()->getLocale() == 'id' ? 'Berita Terbaru' : 'Latest News' }}</h5>
                        </div>
                        <div class="card-body">
                            @forelse($latestNews as $news)
                                <div class="sidebar-news-item mb-3 pb-3 border-bottom">
                                    <div class="row g-0">
                                        <div class="col-4">
                                            <img src="{{ \App\Helpers\ImageHelper::getImageUrl($news->thumbnail, 'news') }}" class="img-fluid rounded" alt="{{ app()->getLocale() == 'id' ? $news->title : $news->title_en }}">
                                        </div>
                                        <div class="col-8 ps-3">
                                            <div class="small text-muted mb-1">
                                                <i class="far fa-calendar-alt me-1"></i> {{ $news->published_at->format('d M Y') }}
                                            </div>
                                            <h6 class="mb-0"><a href="{{ route('news.show', app()->getLocale() == 'id' ? $news->slug : $news->slug_en) }}" class="text-decoration-none text-dark">{{ app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit($news->title, 50) : \Illuminate\Support\Str::limit($news->title_en, 50) }}</a></h6>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <p class="text-center py-3">{{ app()->getLocale() == 'id' ? 'Belum ada berita yang tersedia.' : 'No news available yet.' }}</p>
                            @endforelse

                            <div class="text-center mt-3">
                                <a href="{{ route('news') }}" class="btn btn-outline-success btn-sm">{{ app()->getLocale() == 'id' ? 'Lihat Semua Berita' : 'View All News' }}</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
    .insight-title {
        font-size: 2rem;
        font-weight: 700;
        color: #333;
    }

    .insight-meta {
        color: #6c757d;
        font-size: 0.9rem;
    }

    /* Insight content styling is now handled by summernote-content class */
    .insight-content.summernote-content {
        font-size: 1.1rem;
    }

    .insight-content img {
        max-width: 100%;
        height: auto;
        margin: 1.5rem 0;
        border-radius: 0.5rem;
    }

    .director-profile-image {
        border: 5px solid #fff;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .hover-card {
        transition: transform 0.3s ease;
    }

    .hover-card:hover {
        transform: translateY(-5px);
    }

    .card-img-top-container {
        height: 180px;
        overflow: hidden;
    }

    .card-img-top-container img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .section-title {
        position: relative;
        padding-bottom: 10px;
    }

    .section-title:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 50px;
        height: 3px;
        background-color: #28a745;
    }

    .social-share .btn {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
@endpush
