<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;

class Literature extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'literature';

    protected $fillable = [
        'title',
        'title_en',
        'content',
        'content_en',
        'author',
        'author_en',
        'year',
        'type',
        'type_en',
        'image',
        'file',
        'category_id',
        'is_active',
        'view_count',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'year' => 'integer',
        'view_count' => 'integer',
    ];

    /**
     * Get the type that owns the literature.
     */
    public function litType()
    {
        return $this->belongsTo(LitType::class, 'type', 'slug');
    }

    /**
     * Scope a query to only include active literature.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Handle image upload
     *
     * @param  \Illuminate\Http\UploadedFile  $file
     * @param  string  $field
     * @return string
     */
    public function handleImageUpload($file, $field = 'image')
    {
        // Delete old image if exists
        $this->deleteImage($field);

        // Store the new image
        $path = $file->store('literature', 'public');
        return $path;
    }

    /**
     * Handle file upload
     *
     * @param  \Illuminate\Http\UploadedFile  $file
     * @return string
     */
    public function handleFileUpload($file)
    {
        // Delete old file if exists
        $this->deleteFile();

        // Store the new file
        $path = $file->store('literature/files', 'public');
        return $path;
    }

    /**
     * Delete image
     *
     * @param  string  $field
     * @return void
     */
    public function deleteImage($field = 'image')
    {
        if ($this->{$field} && Storage::disk('public')->exists($this->{$field})) {
            Storage::disk('public')->delete($this->{$field});
        }
    }

    /**
     * Delete file
     *
     * @return void
     */
    public function deleteFile()
    {
        if ($this->file && Storage::disk('public')->exists($this->file)) {
            Storage::disk('public')->delete($this->file);
        }
    }
}
