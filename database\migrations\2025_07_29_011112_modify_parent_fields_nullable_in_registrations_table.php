<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('registrations', function (Blueprint $table) {
            // Modify existing parent_name and parent_occupation columns to be nullable
            $table->string('parent_name')->nullable()->change();
            $table->string('parent_occupation')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('registrations', function (Blueprint $table) {
            // Revert back to NOT NULL (but this might fail if there are null values)
            $table->string('parent_name')->nullable(false)->change();
            $table->string('parent_occupation')->nullable(false)->change();
        });
    }
};
