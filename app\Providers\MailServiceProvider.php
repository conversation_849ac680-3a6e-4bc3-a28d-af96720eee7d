<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\View;

class MailServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register mail component namespace
        View::addNamespace('mail', resource_path('views/components/mail'));
        
        // Register mail components
        Blade::componentNamespace('App\\View\\Components\\Mail', 'mail');
    }
}
