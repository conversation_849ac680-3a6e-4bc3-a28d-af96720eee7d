<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ContactController extends Controller
{
    /**
     * Display a listing of the contacts.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $contacts = \App\Models\Contact::latest()->paginate(10);
        return view('admin.contacts.index', compact('contacts'));
    }

    /**
     * Display the specified contact.
     *
     * @param  \App\Models\Contact  $contact
     * @return \Illuminate\View\View
     */
    public function show(\App\Models\Contact $contact)
    {
        // Mark as read if not already read
        if (!$contact->is_read) {
            $contact->update(['is_read' => true]);
        }

        return view('admin.contacts.show', compact('contact'));
    }

    /**
     * Mark the specified contact as read.
     *
     * @param  \App\Models\Contact  $contact
     * @return \Illuminate\Http\RedirectResponse
     */
    public function markAsRead(\App\Models\Contact $contact)
    {
        $contact->update(['is_read' => true]);

        return redirect()->route('admin.contacts.index')
            ->with('success', 'Message marked as read.');
    }

    /**
     * Reply to the specified contact.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Contact  $contact
     * @return \Illuminate\Http\RedirectResponse
     */
    public function reply(Request $request, \App\Models\Contact $contact)
    {
        $request->validate([
            'reply_message' => 'required|string',
        ]);

        // Update contact with reply
        $contact->update([
            'reply_message' => $request->reply_message,
            'replied_at' => now(),
            'replied_by' => auth()->id(),
            'is_read' => true,
            'read_at' => now(),
        ]);

        // Send reply email
        $emailSent = $this->sendReplyEmail($contact);

        if ($emailSent) {
            return redirect()->route('admin.contacts.show', $contact)
                ->with('success', 'Reply sent successfully and email notification has been sent.');
        } else {
            return redirect()->route('admin.contacts.show', $contact)
                ->with('success', 'Reply saved successfully, but there was an issue sending the email notification.');
        }
    }

    /**
     * Send reply email to the contact.
     *
     * @param  \App\Models\Contact  $contact
     * @return bool Whether the email was sent successfully
     */
    protected function sendReplyEmail(\App\Models\Contact $contact)
    {
        // Only send email if an email address is provided
        if (!$contact->email) {
            \Illuminate\Support\Facades\Log::warning("Cannot send reply email: No email address provided for contact ID {$contact->id}");
            return false;
        }

        try {
            // Use EmailService to send contact reply email
            $emailService = new \App\Services\EmailService();

            // Get the current locale
            $locale = app()->getLocale();

            // Prepare data for the email template
            $data = [
                'name' => $contact->name,
                'subject' => $contact->subject,
                'date' => $contact->created_at->format('d M Y H:i'),
                'original_message' => $contact->message,
                'reply_message' => $contact->reply_message,
            ];

            \Illuminate\Support\Facades\Log::info("Attempting to send reply email to: {$contact->email} with subject: " . __('emails.contact_reply_subject'));

            // Send email using the 'notifications' email type with the template
            $result = $emailService->sendEmailWithView(
                $contact->email,
                __('emails.contact_reply_subject'),
                'emails.contact-reply',
                $data,
                'notifications',
                $locale
            );

            if ($result) {
                // Log that we sent an email
                \Illuminate\Support\Facades\Log::info("Contact reply email sent successfully to: {$contact->email} in {$locale} language");
                return true;
            } else {
                \Illuminate\Support\Facades\Log::error("Failed to send contact reply email to: {$contact->email}");
                return false;
            }
        } catch (\Exception $e) {
            // Log the error but don't fail the contact process
            \Illuminate\Support\Facades\Log::error("Exception when sending contact reply email: " . $e->getMessage());
            \Illuminate\Support\Facades\Log::error("Stack trace: " . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Remove the specified contact from storage.
     *
     * @param  \App\Models\Contact  $contact
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(\App\Models\Contact $contact)
    {
        $contact->delete();

        return redirect()->route('admin.contacts.index')
            ->with('success', 'Message deleted successfully.');
    }
}
