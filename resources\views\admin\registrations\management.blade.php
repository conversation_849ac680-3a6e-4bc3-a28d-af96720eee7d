@extends('admin.layouts.app')

@section('title', 'Registration Management')

@push('styles')
<style>
    /* Custom styles for registration management page */
    .accordion-button:not(.collapsed) {
        background-color: rgba(25, 135, 84, 0.1);
        color: #198754;
    }
</style>
@endpush

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Registration Management</h1>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {{ $activeTab == 'management' ? 'active' : '' }}" href="{{ route('admin.registrations.index', ['tab' => 'management']) }}">
                        <i class="fas fa-cogs me-1"></i> Management
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ $activeTab == 'registration' ? 'active' : '' }}" href="{{ route('admin.registrations.index', ['tab' => 'registration']) }}">
                        <i class="fas fa-list me-1"></i> Registration
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="accordion" id="registrationManagementAccordion">
                <!-- Registration Schedule Section -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingSchedule">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSchedule" aria-expanded="true" aria-controls="collapseSchedule">
                            <i class="fas fa-calendar-alt me-2"></i> Registration Schedule
                        </button>
                    </h2>
                    <div id="collapseSchedule" class="accordion-collapse collapse show" aria-labelledby="headingSchedule" data-bs-parent="#registrationManagementAccordion">
                        <div class="accordion-body">
                            <div class="mb-4">
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addScheduleModal">
                                    <i class="fas fa-plus me-1"></i> Add Schedule
                                </button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Title</th>
                                            <th>Period</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($schedules as $schedule)
                                            <tr>
                                                <td>{{ $schedule->title }}</td>
                                                <td>{{ $schedule->start_date->format('d M Y') }} - {{ $schedule->end_date->format('d M Y') }}</td>
                                                <td>
                                                    @if($schedule->is_active)
                                                        <span class="badge bg-success">Active</span>
                                                    @else
                                                        <span class="badge bg-secondary">Inactive</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-info edit-schedule-btn"
                                                        data-id="{{ $schedule->id }}"
                                                        data-title="{{ $schedule->title }}"
                                                        data-titleen="{{ $schedule->title_en }}"
                                                        data-startdate="{{ $schedule->start_date->format('Y-m-d') }}"
                                                        data-enddate="{{ $schedule->end_date->format('Y-m-d') }}"
                                                        data-description="{{ $schedule->description }}"
                                                        data-descriptionen="{{ $schedule->description_en }}"
                                                        data-isactive="{{ $schedule->is_active ? 1 : 0 }}"
                                                        data-bs-toggle="modal" data-bs-target="#editScheduleModal">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <form action="{{ route('admin.registrations.schedules.destroy', $schedule) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this schedule?')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="4" class="text-center">No schedules found.</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Registration Requirements Section -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingRequirements">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseRequirements" aria-expanded="false" aria-controls="collapseRequirements">
                            <i class="fas fa-file-alt me-2"></i> Document Requirements
                        </button>
                    </h2>
                    <div id="collapseRequirements" class="accordion-collapse collapse" aria-labelledby="headingRequirements" data-bs-parent="#registrationManagementAccordion">
                        <div class="accordion-body">
                            <div class="mb-4">
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRequirementModal">
                                    <i class="fas fa-plus me-1"></i> Add Requirement
                                </button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Description</th>
                                            <th>Order</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($requirements as $requirement)
                                            <tr>
                                                <td>{{ $requirement->name }}</td>
                                                <td>{{ Str::limit($requirement->description, 50) }}</td>
                                                <td>{{ $requirement->order }}</td>
                                                <td>
                                                    @if($requirement->is_active)
                                                        <span class="badge bg-success">Active</span>
                                                    @else
                                                        <span class="badge bg-secondary">Inactive</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-info edit-requirement-btn"
                                                        data-id="{{ $requirement->id }}"
                                                        data-name="{{ $requirement->name }}"
                                                        data-nameen="{{ $requirement->name_en }}"
                                                        data-description="{{ $requirement->description }}"
                                                        data-descriptionen="{{ $requirement->description_en }}"
                                                        data-order="{{ $requirement->order }}"
                                                        data-isactive="{{ $requirement->is_active ? 1 : 0 }}"
                                                        data-bs-toggle="modal" data-bs-target="#editRequirementModal">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <form action="{{ route('admin.registrations.requirements.destroy', $requirement) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this requirement?')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="5" class="text-center">No requirements found.</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Registration Fees Section -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingFees">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFees" aria-expanded="false" aria-controls="collapseFees">
                            <i class="fas fa-money-bill-wave me-2"></i> Registration Fees
                        </button>
                    </h2>
                    <div id="collapseFees" class="accordion-collapse collapse" aria-labelledby="headingFees" data-bs-parent="#registrationManagementAccordion">
                        <div class="accordion-body">
                            <div class="mb-4">
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFeeModal">
                                    <i class="fas fa-plus me-1"></i> Add Fee
                                </button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Amount</th>
                                            <th>Description</th>
                                            <th>Order</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($fees as $fee)
                                            <tr>
                                                <td>{{ $fee->name }}</td>
                                                <td>Rp {{ number_format($fee->amount, 0, ',', '.') }}</td>
                                                <td>{{ Str::limit($fee->description, 50) }}</td>
                                                <td>{{ $fee->order }}</td>
                                                <td>
                                                    @if($fee->is_active)
                                                        <span class="badge bg-success">Active</span>
                                                    @else
                                                        <span class="badge bg-secondary">Inactive</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-info edit-fee-btn"
                                                        data-id="{{ $fee->id }}"
                                                        data-name="{{ $fee->name }}"
                                                        data-nameen="{{ $fee->name_en }}"
                                                        data-amount="{{ $fee->amount }}"
                                                        data-description="{{ $fee->description }}"
                                                        data-descriptionen="{{ $fee->description_en }}"
                                                        data-order="{{ $fee->order }}"
                                                        data-isactive="{{ $fee->is_active ? 1 : 0 }}"
                                                        data-bs-toggle="modal" data-bs-target="#editFeeModal">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <form action="{{ route('admin.registrations.fees.destroy', $fee) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this fee?')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="6" class="text-center">No fees found.</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information Section -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingInfo">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseInfo" aria-expanded="false" aria-controls="collapseInfo">
                            <i class="fas fa-info-circle me-2"></i> Additional Information
                        </button>
                    </h2>
                    <div id="collapseInfo" class="accordion-collapse collapse" aria-labelledby="headingInfo" data-bs-parent="#registrationManagementAccordion">
                        <div class="accordion-body">
                            <form action="{{ route('admin.registrations.info.store') }}" method="POST" enctype="multipart/form-data">
                                @csrf

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="content" class="form-label">Content (Indonesian)</label>
                                            <textarea class="form-control" id="content" name="content" rows="5" required>{{ $info->content ?? '' }}</textarea>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="content_en" class="form-label">Content (English)</label>
                                            <textarea class="form-control" id="content_en" name="content_en" rows="5" required>{{ $info->content_en ?? '' }}</textarea>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="image" class="form-label">Image</label>
                                            <input type="file" class="form-control" id="image" name="image">
                                            <small class="text-muted">Leave empty to keep the current image.</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Current Image</label>
                                            <div>
                                                @if(isset($info) && $info->image)
                                                    <img src="{{ asset('storage/' . $info->image) }}" alt="Registration Info Image" class="img-thumbnail" style="max-height: 150px;">
                                                @else
                                                    <p class="text-muted">No image uploaded.</p>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <input type="hidden" name="is_active" value="1">

                                <div class="text-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> Save Information
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Schedule Modal -->
    <div class="modal fade" id="addScheduleModal" tabindex="-1" aria-labelledby="addScheduleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addScheduleModalLabel">Add Registration Schedule</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="{{ route('admin.registrations.schedules.store') }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="title" class="form-label">Title (Indonesian)</label>
                                    <input type="text" class="form-control" id="title" name="title" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="title_en" class="form-label">Title (English)</label>
                                    <input type="text" class="form-control" id="title_en" name="title_en" required>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">Start Date</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_date" class="form-label">End Date</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" required>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description (Indonesian)</label>
                                    <textarea class="form-control" id="description" name="description" rows="5"></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="description_en" class="form-label">Description (English)</label>
                                    <textarea class="form-control" id="description_en" name="description_en" rows="5"></textarea>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" name="is_active" value="1">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Schedule Modal -->
    <div class="modal fade" id="editScheduleModal" tabindex="-1" aria-labelledby="editScheduleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editScheduleModalLabel">Edit Registration Schedule</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="editScheduleForm" action="" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_title" class="form-label">Title (Indonesian)</label>
                                    <input type="text" class="form-control" id="edit_title" name="title" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_title_en" class="form-label">Title (English)</label>
                                    <input type="text" class="form-control" id="edit_title_en" name="title_en" required>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_start_date" class="form-label">Start Date</label>
                                    <input type="date" class="form-control" id="edit_start_date" name="start_date" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_end_date" class="form-label">End Date</label>
                                    <input type="date" class="form-control" id="edit_end_date" name="end_date" required>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_description" class="form-label">Description (Indonesian)</label>
                                    <textarea class="form-control" id="edit_description" name="description" rows="5"></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_description_en" class="form-label">Description (English)</label>
                                    <textarea class="form-control" id="edit_description_en" name="description_en" rows="5"></textarea>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" name="is_active" value="1">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

    <!-- Add Requirement Modal -->
    <div class="modal fade" id="addRequirementModal" tabindex="-1" aria-labelledby="addRequirementModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addRequirementModalLabel">Add Document Requirement</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="{{ route('admin.registrations.requirements.store') }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Name (Indonesian)</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name_en" class="form-label">Name (English)</label>
                                    <input type="text" class="form-control" id="name_en" name="name_en" required>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description (Indonesian)</label>
                                    <textarea class="form-control" id="description" name="description" rows="5"></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="description_en" class="form-label">Description (English)</label>
                                    <textarea class="form-control" id="description_en" name="description_en" rows="5"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="order" class="form-label">Order</label>
                            <input type="number" class="form-control" id="order" name="order" min="1">
                            <small class="text-muted">Leave empty for automatic ordering.</small>
                        </div>

                        <input type="hidden" name="is_active" value="1">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Requirement Modal -->
    <div class="modal fade" id="editRequirementModal" tabindex="-1" aria-labelledby="editRequirementModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editRequirementModalLabel">Edit Document Requirement</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="editRequirementForm" action="" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_req_name" class="form-label">Name (Indonesian)</label>
                                    <input type="text" class="form-control" id="edit_req_name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_req_name_en" class="form-label">Name (English)</label>
                                    <input type="text" class="form-control" id="edit_req_name_en" name="name_en" required>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_req_description" class="form-label">Description (Indonesian)</label>
                                    <textarea class="form-control" id="edit_req_description" name="description" rows="5"></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_req_description_en" class="form-label">Description (English)</label>
                                    <textarea class="form-control" id="edit_req_description_en" name="description_en" rows="5"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_req_order" class="form-label">Order</label>
                            <input type="number" class="form-control" id="edit_req_order" name="order" min="1">
                        </div>

                        <input type="hidden" name="is_active" value="1">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Fee Modal -->
    <div class="modal fade" id="addFeeModal" tabindex="-1" aria-labelledby="addFeeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addFeeModalLabel">Add Registration Fee</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="{{ route('admin.registrations.fees.store') }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="fee_name" class="form-label">Name (Indonesian)</label>
                                    <input type="text" class="form-control" id="fee_name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="fee_name_en" class="form-label">Name (English)</label>
                                    <input type="text" class="form-control" id="fee_name_en" name="name_en" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="amount" class="form-label">Amount (Rp)</label>
                            <input type="number" class="form-control" id="amount" name="amount" min="0" step="1000" required>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="fee_description" class="form-label">Description (Indonesian)</label>
                                    <textarea class="form-control" id="fee_description" name="description" rows="3"></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="fee_description_en" class="form-label">Description (English)</label>
                                    <textarea class="form-control" id="fee_description_en" name="description_en" rows="3"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="fee_order" class="form-label">Order</label>
                            <input type="number" class="form-control" id="fee_order" name="order" min="1">
                            <small class="text-muted">Leave empty for automatic ordering.</small>
                        </div>

                        <input type="hidden" name="is_active" value="1">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Fee Modal -->
    <div class="modal fade" id="editFeeModal" tabindex="-1" aria-labelledby="editFeeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editFeeModalLabel">Edit Registration Fee</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="editFeeForm" action="{{ url('/admin/registrations/fees') }}" method="POST">
                    @csrf
                    @method('PUT')
                    <!-- This form action will be updated by JavaScript when the modal is opened -->
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_fee_name" class="form-label">Name (Indonesian)</label>
                                    <input type="text" class="form-control" id="edit_fee_name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_fee_name_en" class="form-label">Name (English)</label>
                                    <input type="text" class="form-control" id="edit_fee_name_en" name="name_en" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_amount" class="form-label">Amount (Rp)</label>
                            <input type="number" class="form-control" id="edit_amount" name="amount" min="0" step="1000" required>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_fee_description" class="form-label">Description (Indonesian)</label>
                                    <textarea class="form-control" id="edit_fee_description" name="description" rows="3"></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_fee_description_en" class="form-label">Description (English)</label>
                                    <textarea class="form-control" id="edit_fee_description_en" name="description_en" rows="3"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_fee_order" class="form-label">Order</label>
                            <input type="number" class="form-control" id="edit_fee_order" name="order" min="1">
                        </div>

                        <!-- Always set as active automatically -->
                        <input type="hidden" name="is_active" value="1">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add form submission debugging
        const allForms = document.querySelectorAll('form');
        allForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                console.log('Form submitting to:', this.action);
                console.log('Form method:', this.method);
                // Don't prevent default - let the form submit normally
            });
        });

        // Edit Schedule
        const editScheduleBtns = document.querySelectorAll('.edit-schedule-btn');
        editScheduleBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // Debug data attributes
                console.log('Schedule Data Attributes:', this.dataset);

                const id = this.dataset.id;
                const title = this.dataset.title;
                const titleEn = this.dataset.titleen; // Camel case in HTML becomes lowercase in dataset
                const startDate = this.dataset.startdate;
                const endDate = this.dataset.enddate;
                const description = this.dataset.description;
                const descriptionEn = this.dataset.descriptionen;
                const isActive = this.dataset.isactive === '1';

                // Set form values, handling null/undefined values
                document.getElementById('edit_title').value = title || '';
                document.getElementById('edit_title_en').value = titleEn || '';
                document.getElementById('edit_start_date').value = startDate || '';
                document.getElementById('edit_end_date').value = endDate || '';
                document.getElementById('edit_description').value = description || '';
                document.getElementById('edit_description_en').value = descriptionEn || '';

                const form = document.getElementById('editScheduleForm');
                // Use the correct route with the schedule parameter and full origin
                form.action = window.location.origin + '/admin/registrations/schedules/' + id;
            });
        });

        // Edit Requirement
        const editRequirementBtns = document.querySelectorAll('.edit-requirement-btn');
        editRequirementBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // Debug data attributes
                console.log('Requirement Data Attributes:', this.dataset);

                const id = this.dataset.id;
                const name = this.dataset.name;
                const nameEn = this.dataset.nameen; // Camel case in HTML becomes lowercase in dataset
                const description = this.dataset.description;
                const descriptionEn = this.dataset.descriptionen;
                const order = this.dataset.order;
                const isActive = this.dataset.isactive === '1';

                // Set form values, handling null/undefined values
                document.getElementById('edit_req_name').value = name || '';
                document.getElementById('edit_req_name_en').value = nameEn || '';
                document.getElementById('edit_req_description').value = description || '';
                document.getElementById('edit_req_description_en').value = descriptionEn || '';
                document.getElementById('edit_req_order').value = order || '';

                const form = document.getElementById('editRequirementForm');
                // Use the correct route with the requirement parameter and full origin
                form.action = window.location.origin + '/admin/registrations/requirements/' + id;
            });
        });

        // Edit Fee
        const editFeeBtns = document.querySelectorAll('.edit-fee-btn');
        editFeeBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // Debug data attributes
                console.log('Fee Data Attributes:', this.dataset);

                const id = this.dataset.id;
                const name = this.dataset.name;
                const nameEn = this.dataset.nameen; // Camel case in HTML becomes lowercase in dataset
                const amount = this.dataset.amount;
                const description = this.dataset.description;
                const descriptionEn = this.dataset.descriptionen;
                const order = this.dataset.order;
                const isActive = this.dataset.isactive === '1';

                // Set form values, handling null/undefined values
                document.getElementById('edit_fee_name').value = name || '';
                document.getElementById('edit_fee_name_en').value = nameEn || '';
                document.getElementById('edit_amount').value = amount || '0';
                document.getElementById('edit_fee_description').value = description || '';
                document.getElementById('edit_fee_description_en').value = descriptionEn || '';
                document.getElementById('edit_fee_order').value = order || '';
                // No need to set is_active as it's now a hidden field with value=1

                const form = document.getElementById('editFeeForm');
                // Use the correct route with the fee parameter and full origin
                // This should match the route for AdminRegistrationController@updateFee
                const feeUrl = window.location.origin + '/admin/registrations/fees/' + id;
                form.action = feeUrl;
                console.log('Setting fee form action to:', feeUrl);
            });
        });
    });
</script>
@endpush
