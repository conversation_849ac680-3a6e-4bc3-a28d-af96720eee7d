<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\WebhookController;
use App\Http\Controllers\Api\NewsController;
use App\Http\Controllers\Api\NewsCategoryController;
use App\Http\Controllers\Api\GalleryController;
use App\Http\Controllers\Api\FacilityController;
use App\Http\Controllers\Api\ProgramController;
use App\Http\Controllers\Api\RegistrationController;
use App\Http\Controllers\Api\ContactController;
use App\Http\Controllers\Api\WebsiteIdentityController;
use App\Http\Controllers\Api\SwiperController;
use App\Http\Controllers\Api\MenuController;
use App\Http\Controllers\Api\LeaderController;
use App\Http\Controllers\Api\EducationUnitController;
use App\Http\Controllers\Api\PartnershipController;
use App\Http\Controllers\Api\CurriculumController;
use App\Http\Controllers\Api\AnnouncementController;
use App\Http\Controllers\Api\AgendaController;
use App\Http\Controllers\Api\AcademicCalendarController;
use App\Http\Controllers\Api\ActivityScheduleController;
use App\Http\Controllers\Api\AchievementController;
use App\Http\Controllers\Api\DirectorInsightController;
use App\Http\Controllers\Api\PageController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\VideoController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public API routes
Route::prefix('v1')->group(function () {
    // Authentication
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/register', [AuthController::class, 'register']);

    // Public data endpoints
    Route::get('/news', [NewsController::class, 'index']);
    Route::get('/news/{id}', [NewsController::class, 'show']);
    Route::get('/news/categories', [NewsCategoryController::class, 'index']);
    Route::get('/galleries', [GalleryController::class, 'index']);
    Route::get('/galleries/{id}', [GalleryController::class, 'show']);
    Route::get('/facilities', [FacilityController::class, 'index']);
    Route::get('/facilities/{id}', [FacilityController::class, 'show']);
    Route::get('/programs', [ProgramController::class, 'index']);
    Route::get('/programs/{id}', [ProgramController::class, 'show']);
    Route::get('/announcements', [AnnouncementController::class, 'index']);
    Route::get('/announcements/{id}', [AnnouncementController::class, 'show']);
    Route::get('/agendas', [AgendaController::class, 'index']);
    Route::get('/agendas/{id}', [AgendaController::class, 'show']);
    Route::get('/academic-calendars', [AcademicCalendarController::class, 'index']);
    Route::get('/academic-calendars/{id}', [AcademicCalendarController::class, 'show']);
    Route::get('/activity-schedules', [ActivityScheduleController::class, 'index']);
    Route::get('/activity-schedules/{id}', [ActivityScheduleController::class, 'show']);
    Route::get('/achievements', [AchievementController::class, 'index']);
    Route::get('/achievements/{id}', [AchievementController::class, 'show']);
    Route::get('/leaders', [LeaderController::class, 'index']);
    Route::get('/leaders/{id}', [LeaderController::class, 'show']);
    Route::get('/education-units', [EducationUnitController::class, 'index']);
    Route::get('/education-units/{id}', [EducationUnitController::class, 'show']);
    Route::get('/partnerships', [PartnershipController::class, 'index']);
    Route::get('/partnerships/{id}', [PartnershipController::class, 'show']);
    Route::get('/website-identity', [WebsiteIdentityController::class, 'show']);
    Route::put('/website-identity', [WebsiteIdentityController::class, 'update']);
    Route::get('/swipers', [SwiperController::class, 'index']);
    Route::get('/menus', [MenuController::class, 'index']);
    Route::get('/pages', [PageController::class, 'index']);
    Route::get('/pages/{slug}', [PageController::class, 'show']);
    Route::get('/videos', [VideoController::class, 'index']);
    Route::get('/videos/{id}', [VideoController::class, 'show']);

    // Contact form submission
    Route::post('/contacts', [ContactController::class, 'store']);

    // Registration form submission
    Route::post('/registrations', [RegistrationController::class, 'store']);

    // Webhook management (moved from protected routes)
    Route::get('/webhooks', [WebhookController::class, 'index']);
    Route::post('/webhooks', [WebhookController::class, 'store']);
    Route::get('/webhooks/{id}', [WebhookController::class, 'show']);
    Route::put('/webhooks/{id}', [WebhookController::class, 'update']);
    Route::delete('/webhooks/{id}', [WebhookController::class, 'destroy']);
    Route::post('/webhooks/{id}/test', [WebhookController::class, 'test']);
    Route::post('/webhooks/{id}/regenerate-api-key', [WebhookController::class, 'regenerateApiKey']);

    // Webhook receiver endpoint (protected by API key middleware)
    Route::middleware('webhook.api')->group(function () {
        Route::post('/webhook-receiver', function (Request $request) {
            // The webhook is already validated by the middleware
            $webhook = $request->attributes->get('webhook');

            // Log the webhook call
            \Illuminate\Support\Facades\Log::info('Webhook received', [
                'webhook_id' => $webhook->id,
                'webhook_name' => $webhook->name,
                'event' => $webhook->event,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Webhook received successfully',
                'event' => $webhook->event,
            ]);
        });
    });
});

// Protected API routes (require authentication)
Route::prefix('v1')->middleware('auth:sanctum')->group(function () {
    // User profile
    Route::get('/user', [AuthController::class, 'user']);
    Route::post('/logout', [AuthController::class, 'logout']);

    // Admin routes (require admin role)
    Route::middleware('admin')->group(function () {
        // Users management
        Route::apiResource('/users', UserController::class);

        // Director's Insight
        Route::apiResource('/directors-insights', DirectorInsightController::class);
    });

    // Operator routes (require operator role)
    Route::middleware('operator')->group(function () {
        // News management
        Route::apiResource('/news', NewsController::class)->except(['index', 'show']);
        Route::apiResource('/news-categories', NewsCategoryController::class)->except(['index']);

        // Media management
        Route::apiResource('/galleries', GalleryController::class)->except(['index', 'show']);
        Route::apiResource('/videos', VideoController::class)->except(['index', 'show']);

        // Institution management
        Route::apiResource('/facilities', FacilityController::class)->except(['index', 'show']);
        Route::apiResource('/programs', ProgramController::class)->except(['index', 'show']);
        Route::apiResource('/leaders', LeaderController::class)->except(['index', 'show']);
        Route::apiResource('/education-units', EducationUnitController::class)->except(['index', 'show']);
        Route::apiResource('/partnerships', PartnershipController::class)->except(['index', 'show']);
        Route::apiResource('/curricula', CurriculumController::class);

        // Website management
        Route::apiResource('/swipers', SwiperController::class)->except(['index']);
        Route::apiResource('/menus', MenuController::class)->except(['index']);
        Route::apiResource('/pages', PageController::class)->except(['index', 'show']);

        // Registration management
        Route::get('/registrations', [RegistrationController::class, 'index']);
        Route::get('/registrations/{id}', [RegistrationController::class, 'show']);
        Route::put('/registrations/{id}', [RegistrationController::class, 'update']);
        Route::delete('/registrations/{id}', [RegistrationController::class, 'destroy']);
        Route::post('/registrations/{id}/approve', [RegistrationController::class, 'approve']);
        Route::post('/registrations/{id}/reject', [RegistrationController::class, 'reject']);
        Route::get('/registrations/export', [RegistrationController::class, 'export']);

        // Contact management
        Route::get('/contacts', [ContactController::class, 'index']);
        Route::get('/contacts/{id}', [ContactController::class, 'show']);
        Route::put('/contacts/{id}', [ContactController::class, 'update']);
        Route::delete('/contacts/{id}', [ContactController::class, 'destroy']);
        Route::post('/contacts/{id}/mark-as-read', [ContactController::class, 'markAsRead']);
        Route::post('/contacts/{id}/reply', [ContactController::class, 'reply']);
    });

    // Editor routes (require editor role)
    Route::middleware('editor')->group(function () {
        // Content management
        Route::apiResource('/announcements', AnnouncementController::class)->except(['index', 'show']);
        Route::apiResource('/agendas', AgendaController::class)->except(['index', 'show']);
        Route::apiResource('/academic-calendars', AcademicCalendarController::class)->except(['index', 'show']);
        Route::apiResource('/activity-schedules', ActivityScheduleController::class)->except(['index', 'show']);
        Route::apiResource('/achievements', AchievementController::class)->except(['index', 'show']);
    });
});
