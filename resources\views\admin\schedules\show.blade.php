@extends('admin.layouts.app')

@section('title', 'Activity Schedule Details')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Activity Schedule Details</h1>
        <div>
            <a href="{{ route('admin.schedules.edit', $schedule) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="{{ route('admin.schedules.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Schedule Information</h5>
                    <span class="badge {{ $schedule->is_active ? 'bg-success' : 'bg-secondary' }}">
                        {{ $schedule->is_active ? 'Active' : 'Inactive' }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">ID</div>
                        <div class="col-md-9">{{ $schedule->id }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Title (Indonesian)</div>
                        <div class="col-md-9">{{ $schedule->title }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Title (English)</div>
                        <div class="col-md-9">{{ $schedule->title_en }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Description (Indonesian)</div>
                        <div class="col-md-9">{{ $schedule->description ?? 'N/A' }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Description (English)</div>
                        <div class="col-md-9">{{ $schedule->description_en ?? 'N/A' }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Activity Type</div>
                        <div class="col-md-9">
                            <span class="badge bg-primary">{{ ucfirst($schedule->activity_type) }}</span>
                        </div>
                    </div>

                    @if($schedule->activity_type == 'weekly')
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Day of Week</div>
                        <div class="col-md-9">{{ $schedule->day_of_week }}</div>
                    </div>
                    @endif

                    @if($schedule->activity_type == 'monthly')
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Week</div>
                        <div class="col-md-9">Week {{ $schedule->week_number }}</div>
                    </div>
                    @endif

                    @if($schedule->activity_type == 'yearly')
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Month</div>
                        <div class="col-md-9">{{ $schedule->month_name }}</div>
                    </div>
                    @endif

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Time</div>
                        <div class="col-md-9">{{ $schedule->start_time->format('H:i') }} - {{ $schedule->end_time->format('H:i') }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Duration</div>
                        <div class="col-md-9">
                            @php
                                $duration = $schedule->start_time->diffInMinutes($schedule->end_time);
                                $hours = floor($duration / 60);
                                $minutes = $duration % 60;
                                $durationText = '';
                                if ($hours > 0) {
                                    $durationText .= $hours . ' hour' . ($hours > 1 ? 's' : '');
                                }
                                if ($minutes > 0) {
                                    if ($hours > 0) {
                                        $durationText .= ' and ';
                                    }
                                    $durationText .= $minutes . ' minute' . ($minutes > 1 ? 's' : '');
                                }
                            @endphp
                            {{ $durationText }}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Location (Indonesian)</div>
                        <div class="col-md-9">{{ $schedule->location ?? 'N/A' }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Location (English)</div>
                        <div class="col-md-9">{{ $schedule->location_en ?? 'N/A' }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Instructor (Indonesian)</div>
                        <div class="col-md-9">{{ $schedule->instructor ?? 'N/A' }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Instructor (English)</div>
                        <div class="col-md-9">{{ $schedule->instructor_en ?? 'N/A' }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Category</div>
                        <div class="col-md-9">
                            @if($schedule->category)
                                <span class="badge bg-info">{{ ucfirst($schedule->category) }}</span>
                            @else
                                <span class="text-muted">N/A</span>
                            @endif
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Display Order</div>
                        <div class="col-md-9">{{ $schedule->order }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Created At</div>
                        <div class="col-md-9">{{ $schedule->created_at->format('d F Y, H:i:s') }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Last Updated</div>
                        <div class="col-md-9">{{ $schedule->updated_at->format('d F Y, H:i:s') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
