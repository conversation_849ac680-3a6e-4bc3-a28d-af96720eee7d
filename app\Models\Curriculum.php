<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class Curriculum extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'title',
        'title_en',
        'order',
        'is_active',
        'edu_type',
        'education_unit_id',
        // Formal curriculum fields
        'national_curriculum',
        'national_curriculum_en',
        'general_subjects',
        'general_subjects_en',
        'religious_subjects',
        'religious_subjects_en',
        'local_content',
        'local_content_en',
        'supporting_activities',
        'supporting_activities_en',
        'extracurricular',
        'extracurricular_en',
        'special_program',
        'special_program_en',
        'learning_approach',
        'learning_approach_en',
        'assessment_system',
        'assessment_system_en',
        'assessment_evaluation',
        'assessment_evaluation_en',
        'graduation_certificates',
        'graduation_certificates_en',
        // Non-formal curriculum fields
        'educational_goals',
        'educational_goals_en',
        'core_textbooks_studied',
        'core_textbooks_studied_en',
        'fields_of_study',
        'fields_of_study_en',
        'class_levels',
        'class_levels_en',
        'study_schedule',
        'study_schedule_en',
        'teaching_methods',
        'teaching_methods_en',
        'language_of_instruction',
        'language_of_instruction_en',
        'graduation_certificate',
        'graduation_certificate_en',
    ];

    /**
     * Get the education unit that owns the curriculum.
     */
    public function educationUnit()
    {
        return $this->belongsTo(EducationUnit::class);
    }

    /**
     * Scope a query to only include active curricula.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include formal curricula.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFormal($query)
    {
        return $query->where('edu_type', 'formal');
    }

    /**
     * Scope a query to only include non-formal curricula.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNonFormal($query)
    {
        return $query->where('edu_type', 'non-formal');
    }
}
