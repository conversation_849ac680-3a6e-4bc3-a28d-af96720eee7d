<?php

namespace App\Models;

class News extends BaseModel
{
    protected $fillable = [
        'title',
        'title_en',
        'slug',
        'slug_en',
        'content',
        'content_en',
        'image',
        'thumbnail',
        'user_id',
        'category_id',
        'is_published',
        'published_at',
    ];

    protected $casts = [
        'is_published' => 'boolean',
        'published_at' => 'datetime',
    ];

    /**
     * Get the image fields for this model.
     *
     * @return array
     */
    public function getImageFields(): array
    {
        return ['image', 'thumbnail'];
    }

    /**
     * Get the image upload paths for this model.
     *
     * @return array
     */
    public function getImagePaths(): array
    {
        return [
            'image' => 'news/images',
            'thumbnail' => 'news/thumbnails',
        ];
    }

    /**
     * Get the image dimensions for this model.
     *
     * @return array
     */
    public function getImageDimensions(): array
    {
        return [
            'image' => [
                'width' => config('image.max_width'),
                'height' => config('image.max_height'),
                'quality' => config('image.quality'),
            ],
            'thumbnail' => [
                'width' => 800,
                'height' => 600,
                'quality' => config('image.quality'),
            ],
        ];
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the category that owns the news.
     */
    public function category()
    {
        return $this->belongsTo(NewsCategory::class);
    }

    public function scopePublished($query)
    {
        // Check if the is_published column exists in the news table
        try {
            // Try to use the is_published column
            return $query->where('is_published', true)
                ->whereNotNull('published_at')
                ->where('published_at', '<=', now())
                ->orderBy('published_at', 'desc');
        } catch (\Exception $e) {
            // If the column doesn't exist, just use published_at
            \Illuminate\Support\Facades\Log::warning('is_published column not found, using only published_at');
            return $query->whereNotNull('published_at')
                ->where('published_at', '<=', now())
                ->orderBy('published_at', 'desc');
        }
    }
}
