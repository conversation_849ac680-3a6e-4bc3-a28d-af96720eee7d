<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\NewsRequest;
use App\Models\News;
use App\Models\NewsCategory;
use Illuminate\Http\Request;

class NewsController extends Controller
{

    /**
     * Display a listing of the news.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $news = News::with('category')->latest()->paginate(10);
        return view('admin.news.index', compact('news'));
    }

    /**
     * Show the form for creating a new news.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $categories = NewsCategory::where('is_active', true)->orderBy('id')->get();
        return view('admin.news.create', compact('categories'));
    }

    /**
     * Store a newly created news in storage.
     *
     * @param  \App\Http\Requests\NewsRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(NewsRequest $request)
    {
        $data = $request->validated();

        // Handle image uploads
        if ($request->hasFile('thumbnail')) {
            $data['thumbnail'] = News::make()->handleImageUpload($request->file('thumbnail'), 'thumbnail');
        }

        if ($request->hasFile('image')) {
            $data['image'] = News::make()->handleImageUpload($request->file('image'), 'image');
        }

        // Set user_id to current authenticated user
        $data['user_id'] = auth()->id();

        // Set is_published to true by default
        $data['is_published'] = true;



        News::create($data);

        return redirect()->route('admin.news.index')
            ->with('success', 'News created successfully.');
    }

    /**
     * Display the specified news.
     *
     * @param  \App\Models\News  $news
     * @return \Illuminate\View\View
     */
    public function show(News $news)
    {
        return view('admin.news.show', compact('news'));
    }

    /**
     * Show the form for editing the specified news.
     *
     * @param  \App\Models\News  $news
     * @return \Illuminate\View\View
     */
    public function edit(News $news)
    {
        $categories = NewsCategory::where('is_active', true)->orderBy('id')->get();
        return view('admin.news.edit', compact('news', 'categories'));
    }

    /**
     * Update the specified news in storage.
     *
     * @param  \App\Http\Requests\NewsRequest  $request
     * @param  \App\Models\News  $news
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(NewsRequest $request, News $news)
    {
        $data = $request->validated();

        // Handle image uploads
        if ($request->hasFile('thumbnail')) {
            // Delete old thumbnail first
            $news->deleteImage('thumbnail');

            // Upload new thumbnail
            $data['thumbnail'] = $news->handleImageUpload($request->file('thumbnail'), 'thumbnail');
        }

        if ($request->hasFile('image')) {
            // Delete old image first
            $news->deleteImage('image');

            // Upload new image
            $data['image'] = $news->handleImageUpload($request->file('image'), 'image');
        }

        // Ensure is_published remains true
        $data['is_published'] = true;



        $news->update($data);

        return redirect()->route('admin.news.index')
            ->with('success', 'News updated successfully.');
    }

    /**
     * Remove the specified news from storage.
     *
     * @param  \App\Models\News  $news
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(News $news)
    {
        // The ImageUploadable trait will automatically delete associated images
        $news->delete();

        return redirect()->route('admin.news.index')
            ->with('success', 'News deleted successfully.');
    }
}
