@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? 'Prestasi' : 'Achievements')

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? 'Prestasi' : 'Achievements' }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? 'Prestasi' : 'Achievements' }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Achievements -->
    @if($featuredAchievements->isNotEmpty())
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-4">
                    <h2 class="section-title">{{ app()->getLocale() == 'id' ? 'Prestasi Unggulan' : 'Featured Achievements' }}</h2>
                    <div class="section-divider"></div>
                </div>
            </div>

            <div class="row">
                @foreach($featuredAchievements as $achievement)
                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm border-0 achievement-card fade-in">
                        <div class="achievement-image-container">
                            <img src="{{ \App\Helpers\ImageHelper::getImageUrl($achievement->image, 'achievement') }}" class="card-img-top achievement-image" alt="{{ app()->getLocale() == 'id' ? $achievement->title : $achievement->title_en }}">
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="achievement-date">
                                    <i class="far fa-calendar-alt me-1"></i> {{ $achievement->achievement_date ? $achievement->achievement_date->format('d M Y') : '-' }}
                                </div>
                                <div class="achievement-badge">
                                    <span class="badge bg-success">{{ app()->getLocale() == 'id' ? 'Unggulan' : 'Featured' }}</span>
                                </div>
                            </div>
                            <h5 class="card-title">{{ app()->getLocale() == 'id' ? $achievement->title : $achievement->title_en }}</h5>
                            <p class="card-text text-muted">{{ app()->getLocale() == 'id' ? $achievement->award_by : $achievement->award_by_en }}</p>
                            <a href="{{ route('achievements.show', $achievement->id) }}" class="btn btn-sm btn-outline-success">{{ app()->getLocale() == 'id' ? 'Lihat Detail' : 'View Details' }}</a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- All Achievements -->
    <section class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <div class="row">
                        <div class="col-12 mb-4">
                            <h2 class="section-title">{{ app()->getLocale() == 'id' ? 'Semua Prestasi' : 'All Achievements' }}</h2>
                            <div class="section-divider"></div>
                        </div>
                    </div>

                    @if($achievements->isEmpty())
                        <div class="alert alert-info text-center">
                            {{ app()->getLocale() == 'id' ? 'Belum ada prestasi yang tersedia.' : 'No achievements available yet.' }}
                        </div>
                    @else
                        <!-- Achievements Timeline -->
                        <div class="achievements-timeline">
                            @foreach($achievements as $year => $yearAchievements)
                                <div class="year-section mb-5 fade-in">
                                    <h3 class="year-title">{{ $year }}</h3>
                                    <div class="timeline">
                                        @foreach($yearAchievements as $achievement)
                                            <div class="timeline-item fade-in">
                                                <div class="timeline-dot"></div>
                                                <div class="timeline-content card shadow-sm">
                                                    <div class="card-body">
                                                        <img src="{{ \App\Helpers\ImageHelper::getImageUrl($achievement->image, 'achievement') }}" class="timeline-image mb-3" alt="{{ app()->getLocale() == 'id' ? $achievement->title : $achievement->title_en }}">
                                                        <h5 class="timeline-title">{{ app()->getLocale() == 'id' ? $achievement->title : $achievement->title_en }}</h5>
                                                        <p class="timeline-date">
                                                            <i class="far fa-calendar-alt me-1"></i> {{ $achievement->achievement_date ? $achievement->achievement_date->format('d M Y') : '-' }}
                                                        </p>
                                                        <p class="timeline-award">
                                                            <i class="fas fa-award me-1"></i> {{ app()->getLocale() == 'id' ? $achievement->award_by : $achievement->award_by_en }}
                                                        </p>
                                                        <p class="timeline-description">{{ app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit(strip_tags($achievement->description), 150) : \Illuminate\Support\Str::limit(strip_tags($achievement->description_en), 150) }}</p>
                                                        <a href="{{ route('achievements.show', $achievement->id) }}" class="btn btn-sm btn-outline-success">{{ app()->getLocale() == 'id' ? 'Lihat Detail' : 'View Details' }}</a>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>

                <div class="col-lg-4">
                    <div class="achievements-sidebar">
                        <!-- Latest News -->
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">{{ app()->getLocale() == 'id' ? 'Berita Terbaru' : 'Latest News' }}</h5>
                            </div>
                            <div class="card-body">
                                @if($latestNews->isEmpty())
                                    <p class="text-muted">{{ app()->getLocale() == 'id' ? 'Belum ada berita yang tersedia.' : 'No news available yet.' }}</p>
                                @else
                                    <ul class="list-group list-group-flush">
                                        @foreach($latestNews as $news)
                                            <li class="list-group-item px-0">
                                                <div class="d-flex">
                                                    <div class="flex-shrink-0 me-3">
                                                        <img src="{{ \App\Helpers\ImageHelper::getImageUrl($news->thumbnail, 'news') }}" alt="{{ app()->getLocale() == 'id' ? $news->title : $news->title_en }}" class="rounded" width="60">
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1"><a href="{{ route('news.show', app()->getLocale() == 'id' ? $news->slug : $news->slug_en) }}" class="text-decoration-none">{{ app()->getLocale() == 'id' ? $news->title : $news->title_en }}</a></h6>
                                                        <small class="text-muted"><i class="far fa-calendar-alt me-1"></i> {{ $news->published_at->format('d M Y') }}</small>
                                                    </div>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>
                                    <div class="text-center mt-3">
                                        <a href="{{ route('news') }}" class="btn btn-sm btn-outline-success">{{ app()->getLocale() == 'id' ? 'Lihat Semua Berita' : 'View All News' }}</a>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Latest Announcements -->
                        <div class="card shadow-sm">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">{{ app()->getLocale() == 'id' ? 'Pengumuman Terbaru' : 'Latest Announcements' }}</h5>
                            </div>
                            <div class="card-body">
                                @if($latestAnnouncements->isEmpty())
                                    <p class="text-muted">{{ app()->getLocale() == 'id' ? 'Belum ada pengumuman yang tersedia.' : 'No announcements available yet.' }}</p>
                                @else
                                    <ul class="list-group list-group-flush">
                                        @foreach($latestAnnouncements as $announcement)
                                            <li class="list-group-item px-0">
                                                <h6 class="mb-1"><a href="{{ route('announcements.show', $announcement->id) }}" class="text-decoration-none">{{ app()->getLocale() == 'id' ? $announcement->title : $announcement->title_en }}</a></h6>
                                                <small class="text-muted"><i class="far fa-calendar-alt me-1"></i> {{ $announcement->start_date->format('d M Y') }} - {{ $announcement->end_date->format('d M Y') }}</small>
                                            </li>
                                        @endforeach
                                    </ul>
                                    <div class="text-center mt-3">
                                        <a href="{{ route('announcements') }}" class="btn btn-sm btn-outline-success">{{ app()->getLocale() == 'id' ? 'Lihat Semua Pengumuman' : 'View All Announcements' }}</a>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h2 class="mb-4">{{ app()->getLocale() == 'id' ? 'Bergabunglah dengan Kami' : 'Join Us' }}</h2>
                    <p class="lead mb-4">{{ app()->getLocale() == 'id' ? 'Jadilah bagian dari keluarga besar Pondok Pesantren Nurul Hayah 4 dan raih prestasi bersama kami.' : 'Be a part of the Nurul Hayah 4 Islamic Boarding School family and achieve excellence with us.' }}</p>
                    <a href="{{ route('registration') }}" class="btn btn-light btn-lg">{{ app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now' }}</a>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
    /* Achievement Cards */
    .achievement-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        overflow: hidden;
        border-radius: 8px;
    }

    .achievement-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
    }

    .achievement-image-container {
        height: 200px;
        overflow: hidden;
    }

    .achievement-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }

    .achievement-card:hover .achievement-image {
        transform: scale(1.05);
    }

    /* Timeline Styling */
    .year-title {
        position: relative;
        display: inline-block;
        padding: 0.5rem 1rem;
        background-color: #198754;
        color: white;
        border-radius: 4px;
        margin-bottom: 1.5rem;
    }

    .timeline {
        position: relative;
        padding-left: 2rem;
        margin-left: 1rem;
        border-left: 2px solid #e9ecef;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 2rem;
    }

    .timeline-dot {
        position: absolute;
        left: -2.7rem;
        top: 0.5rem;
        width: 1rem;
        height: 1rem;
        background-color: #198754;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 0 0 2px #198754;
    }

    .timeline-content {
        border-radius: 8px;
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .timeline-content:hover {
        transform: translateX(5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
    }

    .timeline-image {
        width: 100%;
        height: 180px;
        object-fit: cover;
        border-radius: 4px;
    }

    .timeline-title {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .timeline-date, .timeline-award {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 0.5rem;
    }

    .timeline-description {
        margin-bottom: 1rem;
    }

    /* Section Styling */
    .section-title {
        position: relative;
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .section-divider {
        width: 50px;
        height: 3px;
        background-color: #198754;
        margin-bottom: 2rem;
    }

    /* Sidebar Styling */
    .achievements-sidebar {
        position: sticky;
        top: 2rem;
        z-index: 10;
    }

    .achievements-sidebar .card {
        margin-bottom: 1.5rem;
    }

    .achievements-sidebar .card-header {
        padding: 0.75rem 1.25rem;
    }

    .achievements-sidebar .list-group-item {
        border-left: none;
        border-right: none;
        padding: 0.75rem 0;
    }

    .achievements-sidebar .list-group-item:first-child {
        border-top: none;
    }

    .achievements-sidebar .list-group-item:last-child {
        border-bottom: none;
    }

    /* Section Padding */
    .section-padding {
        padding: 80px 0;
    }

    /* Responsive adjustments */
    @media (max-width: 991.98px) {
        .achievements-sidebar {
            position: relative;
            top: 0;
        }

        .section-padding {
            padding: 60px 0;
        }
    }

    @media (max-width: 767.98px) {
        .timeline {
            padding-left: 1.5rem;
        }

        .timeline-dot {
            left: -2.2rem;
        }

        .section-padding {
            padding: 40px 0;
        }
    }
</style>
@endpush
