@extends('admin.layouts.app')

@section('title', 'Add New Swiper Image')

@section('content')
<div class="container-fluid px-4">
    <h1 class="mt-4">Add New Swiper Image</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="{{ route('admin.swipers.index') }}">Swiper Images</a></li>
        <li class="breadcrumb-item active">Add New</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-plus me-1"></i> Add New Swiper Image
        </div>
        <div class="card-body">
            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form action="{{ route('admin.swipers.store') }}" method="POST" enctype="multipart/form-data">
                @csrf

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="title" class="form-label">Title (Indonesian) <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" id="title" name="title" value="{{ old('title') }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="title_en" class="form-label">Title (English)</label>
                            <input type="text" class="form-control @error('title_en') is-invalid @enderror" id="title_en" name="title_en" value="{{ old('title_en') }}">
                            @error('title_en')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="description" class="form-label">Description (Indonesian)</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="5">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="description_en" class="form-label">Description (English)</label>
                            <textarea class="form-control @error('description_en') is-invalid @enderror" id="description_en" name="description_en" rows="5">{{ old('description_en') }}</textarea>
                            @error('description_en')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="button_text" class="form-label">Button Text (Indonesian)</label>
                            <input type="text" class="form-control @error('button_text') is-invalid @enderror" id="button_text" name="button_text" value="{{ old('button_text') }}">
                            @error('button_text')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="button_text_en" class="form-label">Button Text (English)</label>
                            <input type="text" class="form-control @error('button_text_en') is-invalid @enderror" id="button_text_en" name="button_text_en" value="{{ old('button_text_en') }}">
                            @error('button_text_en')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="button_url" class="form-label">Button URL</label>
                    <input type="text" class="form-control @error('button_url') is-invalid @enderror" id="button_url" name="button_url" value="{{ old('button_url') }}">
                    <small class="text-muted">Example: /contact, /programs, https://example.com</small>
                    @error('button_url')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="image" class="form-label">Image <span class="text-danger">*</span></label>
                            <input type="file" class="form-control @error('image') is-invalid @enderror" id="image" name="image" required>
                            <small class="text-muted">Recommended size: 1920x800 pixels. Max file size: 2MB.</small>
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="order" class="form-label">Display Order</label>
                            <input type="number" class="form-control @error('order') is-invalid @enderror" id="order" name="order" value="{{ old('order') }}">
                            <small class="text-muted">Lower numbers will be displayed first. Leave empty for automatic ordering.</small>
                            @error('order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Save Swiper Image
                    </button>
                    <a href="{{ route('admin.swipers.index') }}" class="btn btn-secondary ms-2">
                        <i class="fas fa-times me-1"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Preview image before upload
        const imageInput = document.getElementById('image');
        imageInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // You can add image preview here if needed
                };
                reader.readAsDataURL(file);
            }
        });

        // Prevent Summernote initialization on description fields
        // This needs to run before summernote-init.js tries to initialize these fields
        const descriptionField = document.getElementById('description');
        const descriptionEnField = document.getElementById('description_en');

        if (descriptionField) {
            // Add a custom attribute to mark this field as non-summernote
            descriptionField.setAttribute('data-no-summernote', 'true');
            // Remove any summernote class that might have been added
            descriptionField.classList.remove('summernote');
            // Add a custom ID to avoid targeting by the summernote initializer
            descriptionField.id = 'swiper-description';
        }

        if (descriptionEnField) {
            // Add a custom attribute to mark this field as non-summernote
            descriptionEnField.setAttribute('data-no-summernote', 'true');
            // Remove any summernote class that might have been added
            descriptionEnField.classList.remove('summernote');
            // Add a custom ID to avoid targeting by the summernote initializer
            descriptionEnField.id = 'swiper-description-en';
        }

        // Override any existing Summernote instances on these fields
        setTimeout(function() {
            // Check if Summernote was initialized and destroy it
            if ($(descriptionField).next('.note-editor').length) {
                $(descriptionField).summernote('destroy');
            }
            if ($(descriptionEnField).next('.note-editor').length) {
                $(descriptionEnField).summernote('destroy');
            }
        }, 100);
    });
</script>
@endsection
