<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class MailConfigServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register email types in the container
        $this->registerEmailTypes();
    }

    /**
     * Register email types in the container using configuration from config/mail.php
     *
     * @return void
     */
    private function registerEmailTypes(): void
    {
        // Define email types
        $emailTypes = [
            'default',
            'password_reset',
            'notifications',
            'confirmations',
            'transactional'
        ];

        // Register each email type configuration
        foreach ($emailTypes as $type) {
            $this->app->bind("mail.config.{$type}", function () use ($type) {
                // Get mailer configuration from config/mail.php
                $mailerConfig = config("mail.mailers.{$type}", config("mail.mailers.default"));
                $fromConfig = config("mail.from_addresses.{$type}", config('mail.from'));

                // Build configuration array
                $config = [
                    'mailer' => $type,
                    'host' => $mailerConfig['host'],
                    'port' => $mailerConfig['port'],
                    'username' => $mailerConfig['username'],
                    'password' => $mailerConfig['password'],
                    'encryption' => $mailerConfig['encryption'] ?? null,
                    'scheme' => $mailerConfig['scheme'] ?? 'smtps',
                    'transport' => $mailerConfig['transport'] ?? 'smtp',
                    'from_address' => $fromConfig['address'],
                    'from_name' => $fromConfig['name'],
                    'use_custom_settings' => true,
                ];

                return $config;
            });
        }
    }
}
