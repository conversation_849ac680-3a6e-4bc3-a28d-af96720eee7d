<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Leader;

class LeaderController extends Controller
{
    /**
     * Display a listing of the leaders.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get all active leaders ordered by their order field
        $leaders = Leader::active()
            ->orderBy('order', 'asc')
            ->get();

        return view('public.leaders.index', compact('leaders'));
    }

    /**
     * Display the specified leader.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        // Find the leader by ID
        $leader = Leader::findOrFail($id);

        // Check if the leader is active
        if (!$leader->is_active) {
            abort(404);
        }

        return view('public.leaders.show', compact('leader'));
    }
}
