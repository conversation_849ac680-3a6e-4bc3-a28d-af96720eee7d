<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Insight;
use App\Services\ImageService;
use Illuminate\Http\Request;

class InsightController extends Controller
{
    /**
     * The image service instance.
     */
    protected $imageService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\ImageService  $imageService
     * @return void
     */
    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $insights = Insight::orderBy('order', 'asc')->get();
        return view('admin.insights.index', compact('insights'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.insights.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'nullable|string|max:255',
            'content' => 'required|string',
            'content_en' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // Ubah ke 5MB
            'published_at' => 'nullable|date',
            'order' => 'nullable|integer',
            'is_featured' => 'boolean', // Ubah dari nullable|boolean menjadi boolean
        ]);

        $data = $request->all();

        // Set default order if not provided
        if (!isset($data['order'])) {
            $maxOrder = Insight::max('order');
            $data['order'] = $maxOrder ? $maxOrder + 1 : 1;
        }

        // Set is_active to true by default and is_featured based on checkbox
        $data['is_active'] = true;
        $data['is_featured'] = (bool) $request->input('is_featured', false);

        // If this insight is being featured, unfeature all other insights
        if ($data['is_featured']) {
            Insight::where('is_featured', true)->update(['is_featured' => false]);
        }

        // Set published_at to today if not provided
        if (!isset($data['published_at'])) {
            $data['published_at'] = now()->toDateString();
        }

        // Handle image upload with compression
        if ($request->hasFile('image')) {
            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'insights',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }

        Insight::create($data);

        return redirect()->route('admin.directors-insight.index', ['tab' => 'insight'])
            ->with('success', 'Insight created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show(string $id)
    {
        $insight = Insight::findOrFail($id);
        return view('admin.insights.show', compact('insight'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit(string $id)
    {
        $insight = Insight::findOrFail($id);
        return view('admin.insights.edit', compact('insight'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, string $id)
    {
        $insight = Insight::findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'nullable|string|max:255',
            'content' => 'required|string',
            'content_en' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB
            'published_at' => 'nullable|date',
            'order' => 'nullable|integer',
            'is_featured' => 'boolean',
        ]);

        $data = $request->all();

        // Set is_active to true by default and is_featured based on checkbox
        $data['is_active'] = true;
        $data['is_featured'] = (bool) $request->input('is_featured', false);

        // If this insight is being featured and it wasn't featured before, unfeature all other insights
        if ($data['is_featured'] && !$insight->is_featured) {
            Insight::where('id', '!=', $insight->id)
                  ->where('is_featured', true)
                  ->update(['is_featured' => false]);
        }

        // Handle image upload with compression
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($insight->image) {
                $this->imageService->deleteImage($insight->image);
            }

            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'insights',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }

        $insight->update($data);

        return redirect()->route('admin.directors-insight.index', ['tab' => 'insight'])
            ->with('success', 'Insight updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(string $id)
    {
        $insight = Insight::findOrFail($id);

        // Delete image if exists
        if ($insight->image) {
            $this->imageService->deleteImage($insight->image);
        }

        $insight->delete();

        return redirect()->route('admin.directors-insight.index', ['tab' => 'insight'])
            ->with('success', 'Insight deleted successfully.');
    }
}


