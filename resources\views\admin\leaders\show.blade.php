@extends('admin.layouts.app')

@section('title', 'Leader Details')

@push('styles')
<style>
    /* Text formatting styles */
    .formatted-content p {
        margin-bottom: 0.5rem;
    }

    .formatted-content ul, .formatted-content ol {
        padding-left: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .formatted-content table {
        width: 100%;
        margin-bottom: 1rem;
        border-collapse: collapse;
    }

    .formatted-content table th, .formatted-content table td {
        padding: 0.5rem;
        border: 1px solid #dee2e6;
    }

    .formatted-content table th {
        background-color: #f8f9fa;
    }

    .formatted-content img {
        max-width: 100%;
        height: auto;
        margin-bottom: 0.5rem;
    }
</style>
@endpush

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Leader Details</h1>
        <div>
            <a href="{{ route('admin.leaders.edit', $leader) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="{{ route('admin.leaders.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-body text-center">
                    @if($leader->image)
                        <img src="{{ asset('storage/' . $leader->image) }}" alt="{{ $leader->name }}" class="img-fluid rounded mb-3" style="max-height: 300px;">
                    @else
                        <div class="bg-light p-5 mb-3 text-center">
                            <i class="fas fa-user fa-5x text-secondary"></i>
                        </div>
                    @endif

                    <h4>{{ $leader->name }}</h4>
                    <p class="text-muted">{{ $leader->position }}</p>

                    <div class="d-flex justify-content-center mt-3">
                        @if($leader->social_facebook)
                            <a href="{{ $leader->social_facebook }}" target="_blank" class="btn btn-outline-primary mx-1">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                        @endif

                        @if($leader->social_twitter)
                            <a href="{{ $leader->social_twitter }}" target="_blank" class="btn btn-outline-dark mx-1">
                                <i class="fab fa-x-twitter"></i>
                            </a>
                        @endif

                        @if($leader->social_instagram)
                            <a href="{{ $leader->social_instagram }}" target="_blank" class="btn btn-outline-danger mx-1">
                                <i class="fab fa-instagram"></i>
                            </a>
                        @endif

                        @if($leader->social_linkedin)
                            <a href="{{ $leader->social_linkedin }}" target="_blank" class="btn btn-outline-info mx-1">
                                <i class="fab fa-telegram"></i>
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Leader Information</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Status</div>
                        <div class="col-md-9">
                            @if($leader->is_active)
                                <span class="badge bg-success">Active</span>
                            @else
                                <span class="badge bg-secondary">Inactive</span>
                            @endif
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Display Order</div>
                        <div class="col-md-9">{{ $leader->order }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Email</div>
                        <div class="col-md-9">
                            @if($leader->email)
                                <a href="mailto:{{ $leader->email }}">{{ $leader->email }}</a>
                            @else
                                <span class="text-muted">Not provided</span>
                            @endif
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Phone</div>
                        <div class="col-md-9">
                            @if($leader->phone)
                                <a href="tel:{{ $leader->phone }}">{{ $leader->phone }}</a>
                            @else
                                <span class="text-muted">Not provided</span>
                            @endif
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Created At</div>
                        <div class="col-md-9">{{ $leader->created_at->format('d M Y, H:i') }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Last Updated</div>
                        <div class="col-md-9">{{ $leader->updated_at->format('d M Y, H:i') }}</div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="bioTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="bio-id-tab" data-bs-toggle="tab" data-bs-target="#bio-id" type="button" role="tab" aria-controls="bio-id" aria-selected="true">Bio (Indonesian)</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="bio-en-tab" data-bs-toggle="tab" data-bs-target="#bio-en" type="button" role="tab" aria-controls="bio-en" aria-selected="false">Bio (English)</button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="bioTabsContent">
                        <div class="tab-pane fade show active" id="bio-id" role="tabpanel" aria-labelledby="bio-id-tab">
                            @if($leader->bio)
                                <div class="formatted-content">{!! $leader->bio !!}</div>
                            @else
                                <p class="text-muted">No biography provided in Indonesian.</p>
                            @endif
                        </div>
                        <div class="tab-pane fade" id="bio-en" role="tabpanel" aria-labelledby="bio-en-tab">
                            @if($leader->bio_en)
                                <div class="formatted-content">{!! $leader->bio_en !!}</div>
                            @else
                                <p class="text-muted">No biography provided in English.</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Education History -->
            @if(isset($leader->education_history) && $leader->education_history)
                <div class="card mb-4">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="educationTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="education-id-tab" data-bs-toggle="tab" data-bs-target="#education-id" type="button" role="tab" aria-controls="education-id" aria-selected="true">Education History (Indonesian)</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="education-en-tab" data-bs-toggle="tab" data-bs-target="#education-en" type="button" role="tab" aria-controls="education-en" aria-selected="false">Education History (English)</button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="educationTabsContent">
                            <div class="tab-pane fade show active" id="education-id" role="tabpanel" aria-labelledby="education-id-tab">
                                @php
                                    $educationHistory = json_decode($leader->education_history, true) ?? [];
                                @endphp

                                @if(count($educationHistory) > 0)
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Institution</th>
                                                    <th>Degree</th>
                                                    <th>Year</th>
                                                    <th>Location</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($educationHistory as $education)
                                                    <tr>
                                                        <td>{{ $education['institution'] ?? '-' }}</td>
                                                        <td>{{ $education['degree'] ?? '-' }}</td>
                                                        <td>{{ $education['year'] ?? '-' }}</td>
                                                        <td>{{ $education['location'] ?? '-' }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @else
                                    <p class="text-muted">No education history provided in Indonesian.</p>
                                @endif
                            </div>
                            <div class="tab-pane fade" id="education-en" role="tabpanel" aria-labelledby="education-en-tab">
                                @php
                                    $educationHistoryEn = json_decode($leader->education_history_en, true) ?? [];
                                    $educationHistory = json_decode($leader->education_history, true) ?? [];

                                    // If English version is empty but Indonesian version exists, use Indonesian data
                                    if (count($educationHistoryEn) == 0 && count($educationHistory) > 0) {
                                        $educationHistoryEn = $educationHistory;
                                    } else {
                                        // For each item in English version, fill in missing fields from Indonesian version
                                        foreach ($educationHistoryEn as $key => $item) {
                                            if (isset($educationHistory[$key])) {
                                                // For fields that should be the same in both languages
                                                if (!isset($item['year']) || empty($item['year'])) {
                                                    $educationHistoryEn[$key]['year'] = $educationHistory[$key]['year'] ?? '-';
                                                }
                                                if (!isset($item['location']) || empty($item['location'])) {
                                                    $educationHistoryEn[$key]['location'] = $educationHistory[$key]['location'] ?? '-';
                                                }
                                            }
                                        }
                                    }
                                @endphp

                                @if(count($educationHistoryEn) > 0)
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Institution</th>
                                                    <th>Degree</th>
                                                    <th>Year</th>
                                                    <th>Location</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($educationHistoryEn as $education)
                                                    <tr>
                                                        <td>{{ $education['institution'] ?? '-' }}</td>
                                                        <td>{{ $education['degree'] ?? '-' }}</td>
                                                        <td>{{ $education['year'] ?? '-' }}</td>
                                                        <td>{{ $education['location'] ?? '-' }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @else
                                    <p class="text-muted">No education history provided in English.</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Achievements -->
            @if(isset($leader->achievements) && $leader->achievements)
                <div class="card mb-4">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="achievementsTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="achievements-id-tab" data-bs-toggle="tab" data-bs-target="#achievements-id" type="button" role="tab" aria-controls="achievements-id" aria-selected="true">Achievements (Indonesian)</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="achievements-en-tab" data-bs-toggle="tab" data-bs-target="#achievements-en" type="button" role="tab" aria-controls="achievements-en" aria-selected="false">Achievements (English)</button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="achievementsTabsContent">
                            <div class="tab-pane fade show active" id="achievements-id" role="tabpanel" aria-labelledby="achievements-id-tab">
                                @php
                                    $achievements = json_decode($leader->achievements, true) ?? [];
                                @endphp

                                @if(count($achievements) > 0)
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Title</th>
                                                    <th>Description</th>
                                                    <th>Year</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($achievements as $achievement)
                                                    <tr>
                                                        <td>{{ $achievement['title'] ?? '-' }}</td>
                                                        <td>{!! $achievement['description'] ?? '-' !!}</td>
                                                        <td>{{ $achievement['year'] ?? '-' }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @else
                                    <p class="text-muted">No achievements provided in Indonesian.</p>
                                @endif
                            </div>
                            <div class="tab-pane fade" id="achievements-en" role="tabpanel" aria-labelledby="achievements-en-tab">
                                @php
                                    $achievementsEn = json_decode($leader->achievements_en, true) ?? [];
                                    $achievements = json_decode($leader->achievements, true) ?? [];

                                    // If English version is empty but Indonesian version exists, use Indonesian data
                                    if (count($achievementsEn) == 0 && count($achievements) > 0) {
                                        $achievementsEn = $achievements;
                                    } else {
                                        // For each item in English version, fill in missing fields from Indonesian version
                                        foreach ($achievementsEn as $key => $item) {
                                            if (isset($achievements[$key])) {
                                                // For fields that should be the same in both languages
                                                if (!isset($item['year']) || empty($item['year'])) {
                                                    $achievementsEn[$key]['year'] = $achievements[$key]['year'] ?? '-';
                                                }
                                            }
                                        }
                                    }
                                @endphp

                                @if(count($achievementsEn) > 0)
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Title</th>
                                                    <th>Description</th>
                                                    <th>Year</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($achievementsEn as $achievement)
                                                    <tr>
                                                        <td>{{ $achievement['title'] ?? '-' }}</td>
                                                        <td>{!! $achievement['description'] ?? '-' !!}</td>
                                                        <td>{{ $achievement['year'] ?? '-' }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @else
                                    <p class="text-muted">No achievements provided in English.</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Work Experience -->
            @if(isset($leader->work_experience) && $leader->work_experience)
                <div class="card mb-4">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="workExperienceTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="work-experience-id-tab" data-bs-toggle="tab" data-bs-target="#work-experience-id" type="button" role="tab" aria-controls="work-experience-id" aria-selected="true">Work Experience (Indonesian)</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="work-experience-en-tab" data-bs-toggle="tab" data-bs-target="#work-experience-en" type="button" role="tab" aria-controls="work-experience-en" aria-selected="false">Work Experience (English)</button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="workExperienceTabsContent">
                            <div class="tab-pane fade show active" id="work-experience-id" role="tabpanel" aria-labelledby="work-experience-id-tab">
                                @php
                                    $workExperience = json_decode($leader->work_experience, true) ?? [];
                                @endphp

                                @if(count($workExperience) > 0)
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Company/Organization</th>
                                                    <th>Position</th>
                                                    <th>Period</th>
                                                    <th>Location</th>
                                                    <th>Description</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($workExperience as $experience)
                                                    <tr>
                                                        <td>{{ $experience['company'] ?? '-' }}</td>
                                                        <td>{{ $experience['position'] ?? '-' }}</td>
                                                        <td>{{ $experience['period'] ?? '-' }}</td>
                                                        <td>{{ $experience['location'] ?? '-' }}</td>
                                                        <td>{!! $experience['description'] ?? '-' !!}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @else
                                    <p class="text-muted">No work experience provided in Indonesian.</p>
                                @endif
                            </div>
                            <div class="tab-pane fade" id="work-experience-en" role="tabpanel" aria-labelledby="work-experience-en-tab">
                                @php
                                    $workExperienceEn = json_decode($leader->work_experience_en, true) ?? [];
                                    $workExperience = json_decode($leader->work_experience, true) ?? [];

                                    // If English version is empty but Indonesian version exists, use Indonesian data
                                    if (count($workExperienceEn) == 0 && count($workExperience) > 0) {
                                        $workExperienceEn = $workExperience;
                                    } else {
                                        // For each item in English version, fill in missing fields from Indonesian version
                                        foreach ($workExperienceEn as $key => $item) {
                                            if (isset($workExperience[$key])) {
                                                // For fields that should be the same in both languages
                                                if (!isset($item['period']) || empty($item['period'])) {
                                                    $workExperienceEn[$key]['period'] = $workExperience[$key]['period'] ?? '-';
                                                }
                                                if (!isset($item['location']) || empty($item['location'])) {
                                                    $workExperienceEn[$key]['location'] = $workExperience[$key]['location'] ?? '-';
                                                }
                                            }
                                        }
                                    }
                                @endphp

                                @if(count($workExperienceEn) > 0)
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Company/Organization</th>
                                                    <th>Position</th>
                                                    <th>Period</th>
                                                    <th>Location</th>
                                                    <th>Description</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($workExperienceEn as $experience)
                                                    <tr>
                                                        <td>{{ $experience['company'] ?? '-' }}</td>
                                                        <td>{{ $experience['position'] ?? '-' }}</td>
                                                        <td>{{ $experience['period'] ?? '-' }}</td>
                                                        <td>{{ $experience['location'] ?? '-' }}</td>
                                                        <td>{!! $experience['description'] ?? '-' !!}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @else
                                    <p class="text-muted">No work experience provided in English.</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
@endsection
