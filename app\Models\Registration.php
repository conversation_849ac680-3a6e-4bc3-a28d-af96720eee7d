<?php

namespace App\Models;

use App\Models\EducationUnit;

class Registration extends BaseModel
{
    protected $fillable = [
        'nik',
        'full_name',
        'gender',
        'place_of_birth',
        'date_of_birth',
        'parent_name',
        'parent_occupation',
        'father_name',
        'father_occupation',
        'mother_name',
        'mother_occupation',
        'address',
        'phone',
        'email',
        'nisn',
        'last_education',
        'previous_school',
        'graduation_year',
        'notes',
        'education_unit_id',
        'reason',
        'hobby',
        'ambition',
        'status',
        'registration_number',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
    ];



    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    public function approve()
    {
        $this->update([
            'status' => 'approved',
            'approved_at' => now(),
            'rejected_at' => null,
        ]);
    }

    public function reject()
    {
        $this->update([
            'status' => 'rejected',
            'rejected_at' => now(),
            'approved_at' => null,
        ]);
    }

    /**
     * Get the education unit that the student is applying for.
     */
    public function educationUnit()
    {
        return $this->belongsTo(EducationUnit::class);
    }
}

