@extends('admin.layouts.app')

@section('title', 'View Insight')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View Insight</h1>
        <div>
            <a href="{{ route('admin.insights.edit', $insight) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="{{ route('admin.directors-insight.index', ['tab' => 'insight']) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Insights
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-12 mb-4">
                    @if($insight->image)
                        <img src="{{ asset('storage/' . $insight->image) }}" alt="{{ $insight->title }}" class="img-fluid rounded w-100" style="max-height: 400px; object-fit: cover;">
                    @endif
                </div>
                <div class="col-md-12">
                    <h2 class="mb-3">{{ $insight->title }}</h2>
                    <div class="d-flex mb-4">
                        <span class="me-3"><i class="far fa-calendar-alt me-1"></i> {{ $insight->published_at ? $insight->published_at->format('d M Y') : 'Not published' }}</span>
                        @if($insight->is_featured)
                            <span class="badge bg-warning">Featured</span>
                        @endif
                    </div>

                    <div class="mb-4">
                        <h5>Content</h5>
                        <div class="p-3 bg-light rounded">
                            {!! $insight->content !!}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <h6>Title (English)</h6>
                                <p>{{ $insight->title_en ?: 'Not provided' }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h5>Content (English)</h5>
                        <div class="p-3 bg-light rounded">
                            {!! $insight->content_en ?: 'Not provided' !!}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <h6>Display Order</h6>
                                <p>{{ $insight->order }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <h6>Status</h6>
                                <p>
                                    @if($insight->is_active)
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <span class="badge bg-danger">Inactive</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <h6>Featured</h6>
                                <p>
                                    @if($insight->is_featured)
                                        <span class="badge bg-warning">Featured</span>
                                    @else
                                        <span class="badge bg-secondary">No</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
