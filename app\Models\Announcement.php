<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Announcement extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'title_en',
        'content',
        'content_en',
        'image',
        'file',
        'start_date',
        'end_date',
        'is_featured',
        'order',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'order' => 'integer',
    ];

    /**
     * Get the image fields for this model.
     *
     * @return array
     */
    public function getImageFields(): array
    {
        return ['image'];
    }

    /**
     * Get the image upload paths for this model.
     *
     * @return array
     */
    public function getImagePaths(): array
    {
        return [
            'image' => 'announcements',
        ];
    }

    /**
     * Get the image dimensions for this model.
     *
     * @return array
     */
    public function getImageDimensions(): array
    {
        return [
            'image' => [
                'width' => config('image.max_width'),
                'height' => config('image.max_height'),
                'quality' => config('image.quality'),
            ],
        ];
    }

    /**
     * Scope a query to only include current announcements.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCurrent($query)
    {
        $today = now()->format('Y-m-d');
        return $query->where('is_active', true)
            ->where('start_date', '<=', $today)
            ->where('end_date', '>=', $today)
            ->orderBy('order', 'asc');
    }
}
