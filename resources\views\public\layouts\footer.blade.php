<footer class="bg-dark text-white pt-5 pb-3">
    <div class="container">
        <div class="row">
            <!-- About -->
            <div class="col-lg-4 mb-4">
                <h5 class="text-uppercase mb-4">{{ app()->getLocale() == 'id' ? 'Tentang Kami' : 'About Us' }}</h5>
                <p>{{ app()->getLocale() == 'id' ? \App\Helpers\SettingHelper::getInstitutionName() . ' adalah lembaga pendidikan Islam modern yang berkomitmen menghasilkan generasi Muslim berakhlak mulia, be<PERSON><PERSON><PERSON><PERSON> luas, dan berdaya saing global.' : \App\Helpers\SettingHelper::getInstitutionNameEn() . ' is a modern Islamic educational institution committed to producing a generation of Muslims with noble character, broad insight, and global competitiveness.' }}</p>
                <div class="social-links mt-3">
                    <a href="{{ \App\Models\Setting::getValue('facebook', '#') }}" class="text-white me-3" target="_blank"><i class="fab fa-facebook-f fa-lg"></i></a>
                    <a href="{{ \App\Models\Setting::getValue('instagram', '#') }}" class="text-white me-3" target="_blank"><i class="fab fa-instagram fa-lg"></i></a>
                    <a href="{{ \App\Models\Setting::getValue('twitter', '#') }}" class="text-white me-3" target="_blank"><i class="fab fa-spotify fa-lg"></i></a>
                    <a href="{{ \App\Models\Setting::getValue('youtube', '#') }}" class="text-white" target="_blank"><i class="fab fa-youtube fa-lg"></i></a>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="col-lg-4 mb-4">
                <h5 class="text-uppercase mb-4">{{ app()->getLocale() == 'id' ? 'Tautan Cepat' : 'Quick Links' }}</h5>
                <ul class="list-unstyled">
                    <li class="mb-2"><a href="{{ route('home') }}" class="text-white text-decoration-none"><i class="fas fa-angle-right me-2"></i>{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                    <li class="mb-2"><a href="{{ route('profile.vision-mission') }}" class="text-white text-decoration-none"><i class="fas fa-angle-right me-2"></i>{{ app()->getLocale() == 'id' ? 'Visi & Misi' : 'Vision & Mission' }}</a></li>
                    <li class="mb-2"><a href="{{ route('profile.history') }}" class="text-white text-decoration-none"><i class="fas fa-angle-right me-2"></i>{{ app()->getLocale() == 'id' ? 'Sejarah' : 'History' }}</a></li>
                    <li class="mb-2"><a href="{{ route('facilities') }}" class="text-white text-decoration-none"><i class="fas fa-angle-right me-2"></i>{{ app()->getLocale() == 'id' ? 'Fasilitas' : 'Facilities' }}</a></li>
                    <li class="mb-2"><a href="{{ route('programs') }}" class="text-white text-decoration-none"><i class="fas fa-angle-right me-2"></i>{{ app()->getLocale() == 'id' ? 'Program' : 'Programs' }}</a></li>
                    <li class="mb-2"><a href="{{ route('news') }}" class="text-white text-decoration-none"><i class="fas fa-angle-right me-2"></i>{{ app()->getLocale() == 'id' ? 'Berita' : 'News' }}</a></li>
                    <li class="mb-2"><a href="{{ route('gallery') }}" class="text-white text-decoration-none"><i class="fas fa-angle-right me-2"></i>{{ app()->getLocale() == 'id' ? 'Galeri' : 'Gallery' }}</a></li>
                    <li class="mb-2"><a href="{{ route('contact') }}" class="text-white text-decoration-none"><i class="fas fa-angle-right me-2"></i>{{ app()->getLocale() == 'id' ? 'Kontak' : 'Contact' }}</a></li>
                </ul>
            </div>

            <!-- Contact Info -->
            <div class="col-lg-4 mb-4">
                <h5 class="text-uppercase mb-4">{{ app()->getLocale() == 'id' ? 'Informasi Kontak' : 'Contact Information' }}</h5>
                <p class="mb-3"><i class="fas fa-map-marker-alt me-2"></i> {{ \App\Models\Setting::getValue('address', 'Jl. Pesantren No. 123, Kota Jakarta, Indonesia') }}</p>
                <p class="mb-3"><i class="fas fa-phone-alt me-2"></i> {{ \App\Models\Setting::getValue('phone', '+62 123 4567 890') }}</p>
                <p class="mb-3"><i class="fas fa-envelope me-2"></i> {{ \App\Models\Setting::getValue('email', '<EMAIL>') }}</p>
                <a href="{{ route('registration') }}" class="btn btn-success mt-3">{{ app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now' }}</a>
            </div>
        </div>

        <hr class="my-4">

        <!-- Copyright -->
        <div class="row">
            <div class="col-md-6 text-center text-md-start">
                <p class="mb-0">&copy; {{ date('Y') }} {{ app()->getLocale() == 'id' ? \App\Helpers\SettingHelper::getInstitutionName() . '. Hak Cipta Dilindungi.' : \App\Helpers\SettingHelper::getInstitutionNameEn() . '. All Rights Reserved.' }}</p>
            </div>
            <div class="col-md-6 text-center text-md-end">
                <p class="mb-0">{{ app()->getLocale() == 'id' ? 'Dibuat dengan' : 'Made with' }} <i class="fas fa-heart text-danger"></i> {{ app()->getLocale() == 'id' ? 'oleh' : 'by' }} <a href="#" class="text-white text-decoration-none">Santri IT</a></p>
            </div>
        </div>
    </div>
</footer>
