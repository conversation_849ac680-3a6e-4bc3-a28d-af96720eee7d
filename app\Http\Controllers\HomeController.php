<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Director;
use App\Models\Insight;
use App\Models\Announcement;
use App\Models\Agenda;
use App\Models\Partnership;
use App\Models\Swiper;

class HomeController extends Controller
{
    /**
     * Display the home page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get featured programs
        $featuredPrograms = \App\Models\Program::featured()->take(5)->get();

        // Get the "berita" category
        $beritaCategory = \App\Models\NewsCategory::where('name', 'Berita')
            ->orWhere('name', 'berita')
            ->orWhere('name', 'News')
            ->orWhere('name', 'news')
            ->first();

        // Get latest news from the "berita" category only
        if ($beritaCategory) {
            $latestNews = \App\Models\News::published()
                ->where('category_id', $beritaCategory->id)
                ->take(3)
                ->get();

            // Log the news count for debugging
            \Illuminate\Support\Facades\Log::info('Latest berita news count: ' . $latestNews->count());
        } else {
            // Fallback if category not found
            $latestNews = \App\Models\News::published()->take(3)->get();
            \Illuminate\Support\Facades\Log::info('No berita category found, showing all news');
        }

        // Get all news for debugging
        $allNews = \App\Models\News::all();
        \Illuminate\Support\Facades\Log::info('All news count: ' . $allNews->count());

        // Get facilities
        $facilities = \App\Models\Facility::active()->take(6)->get();

        // Get gallery images (sorted by latest)
        $galleryImages = \App\Models\Gallery::where('is_active', true)
            ->latest()  // Order by created_at DESC
            ->take(6)
            ->get();

        // Get director and insight for Director's Insight section
        $director = Director::active()->first();
        $featuredInsight = Insight::where('is_active', true)
            ->where('is_featured', true)
            ->latest('published_at')
            ->first();

        // Get all active announcements
        $latestAnnouncements = Announcement::where('is_active', true)
            ->orderBy('order', 'asc')
            ->take(6) // Showing 6 announcements on the home page
            ->get();

        // Get upcoming agenda items - using the upcoming scope
        $upcomingAgenda = Agenda::upcoming()
            ->take(3)
            ->get();

        // If no upcoming agenda items, get any active agenda items
        if ($upcomingAgenda->isEmpty()) {
            $upcomingAgenda = Agenda::active()
                ->take(3)
                ->get();
        }

        // Get active partnerships
        $partnerships = Partnership::active()->get();

        // Get active swiper images
        $swiperImages = Swiper::active()->get();

        // Get website name from settings
        $websiteName = \App\Helpers\SettingHelper::getWebsiteName();

        return view('public.home', compact(
            'featuredPrograms',
            'latestNews',
            'facilities',
            'galleryImages',
            'websiteName',
            'director',
            'featuredInsight',
            'latestAnnouncements',
            'upcomingAgenda',
            'partnerships',
            'swiperImages'
        ));
    }

    /**
     * Update all news to be published (temporary fix)
     */
    public function updateNewsPublishStatus()
    {
        $news = \App\Models\News::all();
        $count = 0;

        foreach ($news as $article) {
            $article->is_published = true;
            $article->save();
            $count++;
        }

        return "Updated $count news articles to be published.";
    }

    /**
     * Create the "Berita" category if it doesn't exist
     */
    public function createBeritaCategory()
    {
        $beritaCategory = \App\Models\NewsCategory::where('name', 'Berita')
            ->orWhere('name', 'berita')
            ->orWhere('name', 'News')
            ->orWhere('name', 'news')
            ->first();

        if (!$beritaCategory) {
            $beritaCategory = \App\Models\NewsCategory::create([
                'name' => 'Berita',
                'name_en' => 'News',
                'slug' => 'berita',
                'slug_en' => 'news',
                'description' => 'Kategori untuk berita terbaru',
                'description_en' => 'Category for latest news',
                'is_active' => true
            ]);

            return "Berita category created successfully with ID: " . $beritaCategory->id;
        }

        return "Berita category already exists with ID: " . $beritaCategory->id;
    }

    /**
     * Update all news to use the "Berita" category
     */
    public function updateNewsCategory()
    {
        // First, make sure the Berita category exists
        $beritaCategory = \App\Models\NewsCategory::where('name', 'Berita')
            ->orWhere('name', 'berita')
            ->orWhere('name', 'News')
            ->orWhere('name', 'news')
            ->first();

        if (!$beritaCategory) {
            // Create the category if it doesn't exist
            $beritaCategory = \App\Models\NewsCategory::create([
                'name' => 'Berita',
                'name_en' => 'News',
                'slug' => 'berita',
                'slug_en' => 'news',
                'description' => 'Kategori untuk berita terbaru',
                'description_en' => 'Category for latest news',
                'is_active' => true
            ]);
        }

        // Update all news without a category to use the Berita category
        $news = \App\Models\News::whereNull('category_id')->get();
        $count = 0;

        foreach ($news as $article) {
            $article->category_id = $beritaCategory->id;
            $article->save();
            $count++;
        }

        return "Updated $count news articles to use the Berita category (ID: " . $beritaCategory->id . ").";
    }
}
