<?php

namespace App\Console\Commands;

use App\Services\TelegramBotService;
use Illuminate\Console\Command;

class TelegramDeleteWebhook extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:delete-webhook';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete the webhook for the Telegram bot';

    /**
     * The Telegram Bot service instance.
     *
     * @var \App\Services\TelegramBotService
     */
    protected $telegramService;

    /**
     * Create a new command instance.
     *
     * @param  \App\Services\TelegramBotService  $telegramService
     * @return void
     */
    public function __construct(TelegramBotService $telegramService)
    {
        parent::__construct();
        $this->telegramService = $telegramService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Deleting webhook...');

        // Delete the webhook
        $result = $this->telegramService->deleteWebhook();

        if ($result && isset($result['ok']) && $result['ok']) {
            $this->info('Webhook deleted successfully!');
            return 0;
        } else {
            $this->error('Failed to delete webhook.');
            if ($result && isset($result['description'])) {
                $this->error('Error: ' . $result['description']);
            }
            return 1;
        }
    }
}
