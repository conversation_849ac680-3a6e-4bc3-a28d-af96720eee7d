@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? $calendar->title : $calendar->title_en)

@push('styles')
<style>
    /* Calendar Detail Styles */
    .calendar-detail-card {
        border: none;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .calendar-header {
        position: relative;
        padding: 30px;
        background-color: #198754;
        color: white;
    }

    .calendar-header.exam {
        background-color: #dc3545;
    }

    .calendar-header.holiday {
        background-color: #0d6efd;
    }

    .calendar-header.semester {
        background-color: #ffc107;
        color: #212529;
    }

    .calendar-header.event {
        background-color: #6f42c1;
    }

    .calendar-header h1 {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 15px;
    }

    .calendar-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-top: 20px;
    }

    .calendar-meta-item {
        display: flex;
        align-items: center;
    }

    .calendar-meta-item i {
        margin-right: 8px;
        font-size: 18px;
    }

    .calendar-content {
        padding: 30px;
    }

    .calendar-description {
        margin-bottom: 30px;
        line-height: 1.8;
    }

    .calendar-type-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 15px;
        background-color: rgba(25, 135, 84, 0.1);
        color: #198754;
    }

    .calendar-type-badge.exam {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .calendar-type-badge.holiday {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }

    .calendar-type-badge.semester {
        background-color: rgba(255, 193, 7, 0.1);
        color: #212529;
    }

    .calendar-type-badge.event {
        background-color: rgba(111, 66, 193, 0.1);
        color: #6f42c1;
    }

    /* Related Calendar Styles */
    .related-calendar {
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #dee2e6;
    }

    .related-calendar:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }

    .related-calendar-date {
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 5px;
    }

    .related-calendar h5 {
        margin-bottom: 5px;
        font-weight: 600;
    }

    .related-calendar-type {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 20px;
        font-size: 12px;
        background-color: #e9ecef;
        margin-bottom: 5px;
    }

    .related-calendar-type.exam {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .related-calendar-type.holiday {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }

    .related-calendar-type.semester {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }

    .related-calendar-type.event {
        background-color: rgba(111, 66, 193, 0.1);
        color: #6f42c1;
    }

    /* Animation classes */
    .fade-in-up {
        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.6s ease-out, transform 0.6s ease-out;
    }

    .fade-in-up.active {
        opacity: 1;
        transform: translateY(0);
    }

    /* Note: Sticky sidebar is now defined in custom.css */
</style>
@endpush

@section('content')
    <!-- Page Header -->
    <section class="bg-light py-4">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('home') }}">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('calendars.index') }}">{{ app()->getLocale() == 'id' ? 'Kalender Akademik' : 'Academic Calendar' }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ app()->getLocale() == 'id' ? $calendar->title : $calendar->title_en }}</li>
                </ol>
            </nav>
        </div>
    </section>

    <!-- Calendar Detail -->
    <section class="section-padding">
        <div class="container">
            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8 mb-4 mb-lg-0">
                    <div class="calendar-detail-card fade-in-up">
                        <div class="calendar-header {{ strtolower($calendar->type ?? 'other') }}">
                            @if($calendar->type)
                                <div class="calendar-type-badge {{ strtolower($calendar->type) }}">
                                    {{ $calendar->type }}
                                </div>
                            @endif
                            <h1>{{ app()->getLocale() == 'id' ? $calendar->title : $calendar->title_en }}</h1>
                            <div class="calendar-meta">
                                <div class="calendar-meta-item">
                                    <i class="far fa-calendar-alt"></i>
                                    <span>
                                        {{ $calendar->start_date->format('d M Y') }}
                                        @if($calendar->start_date->format('Y-m-d') != $calendar->end_date->format('Y-m-d'))
                                            - {{ $calendar->end_date->format('d M Y') }}
                                        @endif
                                    </span>
                                </div>
                                @if($calendar->location || $calendar->location_en)
                                    <div class="calendar-meta-item">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <span>{{ app()->getLocale() == 'id' ? $calendar->location : $calendar->location_en }}</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="calendar-content">
                            <div class="calendar-description">
                                {!! app()->getLocale() == 'id' ? $calendar->description : $calendar->description_en !!}
                            </div>
                            <a href="{{ route('calendars.index') }}" class="btn btn-outline-success">
                                <i class="fas fa-arrow-left me-2"></i>{{ app()->getLocale() == 'id' ? 'Kembali ke Daftar Kalender' : 'Back to Calendar List' }}
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <div class="sticky-sidebar">
                        <!-- Related Calendars -->
                        <div class="card border-0 shadow-sm mb-4 fade-in-up">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">{{ app()->getLocale() == 'id' ? 'Kalender Terkait' : 'Related Calendars' }}</h5>
                            </div>
                            <div class="card-body">
                                @forelse($relatedCalendars as $relatedCalendar)
                                    <div class="related-calendar">
                                        <div class="related-calendar-date">
                                            <i class="far fa-calendar-alt me-1"></i>
                                            {{ $relatedCalendar->start_date->format('d M Y') }}
                                            @if($relatedCalendar->start_date->format('Y-m-d') != $relatedCalendar->end_date->format('Y-m-d'))
                                                - {{ $relatedCalendar->end_date->format('d M Y') }}
                                            @endif
                                        </div>
                                        @if($relatedCalendar->type)
                                            <div class="related-calendar-type {{ strtolower($relatedCalendar->type) }}">
                                                {{ $relatedCalendar->type }}
                                            </div>
                                        @endif
                                        <h5>
                                            <a href="{{ route('calendars.show', $relatedCalendar->id) }}" class="text-decoration-none">
                                                {{ app()->getLocale() == 'id' ? $relatedCalendar->title : $relatedCalendar->title_en }}
                                            </a>
                                        </h5>
                                    </div>
                                @empty
                                    <p class="text-center py-3">{{ app()->getLocale() == 'id' ? 'Tidak ada kalender terkait.' : 'No related calendars.' }}</p>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize animations
        const animElements = document.querySelectorAll('.fade-in-up');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('active');
                }
            });
        }, {
            threshold: 0.1
        });

        animElements.forEach(element => {
            observer.observe(element);
        });
    });
</script>
<script src="{{ asset('js/sticky-sidebar.js') }}"></script>
@endpush
