<?php

namespace App\Console\Commands;

use App\Models\Setting;
use App\Services\TelegramBotService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TelegramCheckWebhook extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:check-webhook';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check the Telegram webhook configuration and connectivity';

    /**
     * The Telegram Bot service instance.
     *
     * @var \App\Services\TelegramBotService
     */
    protected $telegramService;

    /**
     * Create a new command instance.
     *
     * @param \App\Services\TelegramBotService $telegramService
     * @return void
     */
    public function __construct(TelegramBotService $telegramService)
    {
        parent::__construct();
        $this->telegramService = $telegramService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking Telegram webhook configuration...');

        // Check if Telegram is configured
        $botToken = Setting::getValue('telegram_bot_token');
        if (empty($botToken)) {
            $this->error('Telegram bot token is not configured.');
            return 1;
        }

        // Get webhook info
        $webhookInfo = $this->telegramService->getWebhookInfo();
        if (!$webhookInfo || !isset($webhookInfo['ok']) || !$webhookInfo['ok']) {
            $this->error('Failed to get webhook info.');
            if ($webhookInfo && isset($webhookInfo['description'])) {
                $this->error('Error: ' . $webhookInfo['description']);
            }
            return 1;
        }

        // Display webhook info
        $this->info('Current webhook configuration:');
        $this->table(
            ['Property', 'Value'],
            [
                ['URL', $webhookInfo['result']['url'] ?? 'Not set'],
                ['Has Custom Certificate', $webhookInfo['result']['has_custom_certificate'] ?? 'No'],
                ['Pending Updates', $webhookInfo['result']['pending_update_count'] ?? '0'],
                ['Max Connections', $webhookInfo['result']['max_connections'] ?? 'Default'],
                ['Last Error Date', isset($webhookInfo['result']['last_error_date']) ? date('Y-m-d H:i:s', $webhookInfo['result']['last_error_date']) : 'None'],
                ['Last Error Message', $webhookInfo['result']['last_error_message'] ?? 'None'],
            ]
        );

        // Check if webhook URL is set
        $webhookUrl = $webhookInfo['result']['url'] ?? null;
        if (empty($webhookUrl)) {
            $this->warn('No webhook URL is set. Use telegram:set-webhook to set one.');
            return 1;
        }

        // Check if the webhook URL is accessible
        $this->info('Checking if webhook URL is accessible...');
        try {
            $response = Http::timeout(10)->get($webhookUrl);
            $this->info('Webhook URL is accessible. Status code: ' . $response->status());
            
            if ($response->status() >= 400) {
                $this->warn('Webhook URL returned an error status code.');
            }
        } catch (\Exception $e) {
            $this->error('Failed to access webhook URL: ' . $e->getMessage());
            $this->warn('This might be expected if the URL is only accessible from Telegram servers.');
        }

        // Check for pending updates
        $pendingUpdates = $webhookInfo['result']['pending_update_count'] ?? 0;
        if ($pendingUpdates > 0) {
            $this->warn("There are {$pendingUpdates} pending updates. This might indicate issues with webhook processing.");
        } else {
            $this->info('No pending updates. Webhook is processing updates correctly.');
        }

        // Check for last error
        if (isset($webhookInfo['result']['last_error_date'])) {
            $lastErrorDate = date('Y-m-d H:i:s', $webhookInfo['result']['last_error_date']);
            $lastErrorMessage = $webhookInfo['result']['last_error_message'] ?? 'Unknown error';
            $this->warn("Last webhook error: {$lastErrorMessage} at {$lastErrorDate}");
        } else {
            $this->info('No webhook errors reported.');
        }

        // Test sending a message to admin
        $this->info('Testing sending a message to admin...');
        $result = $this->telegramService->sendAdminMessage('🧪 <b>WEBHOOK CHECK</b> - This is a test message from the webhook check command.');

        if ($result && isset($result['ok']) && $result['ok']) {
            $this->info('Test message sent successfully!');
        } else {
            $this->error('Failed to send test message.');
            if ($result && isset($result['description'])) {
                $this->error('Error: ' . $result['description']);
            }
        }

        // Provide recommendations
        $this->info("\nRecommendations:");
        
        if (isset($webhookInfo['result']['last_error_date'])) {
            $this->warn('- There have been webhook errors. Consider checking server logs and fixing any issues.');
        }
        
        if ($pendingUpdates > 0) {
            $this->warn('- There are pending updates. Consider deleting and re-setting the webhook using:');
            $this->warn('  php artisan telegram:delete-webhook');
            $this->warn('  php artisan telegram:set-webhook');
        }
        
        $this->info('- Ensure your server is accessible from the internet.');
        $this->info('- Ensure your webhook URL is using HTTPS (required by Telegram).');
        $this->info('- Check Laravel logs for any errors related to webhook processing.');
        
        return 0;
    }
}
