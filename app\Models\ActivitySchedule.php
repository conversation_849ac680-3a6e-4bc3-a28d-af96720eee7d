<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ActivitySchedule extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'title_en',
        'description',
        'description_en',
        'day_of_week',
        'start_time',
        'end_time',
        'location',
        'location_en',
        'instructor',
        'instructor_en',
        'category',
        'activity_type',
        'week_number',
        'month_number',
        'order',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'is_active' => 'boolean',
        'order' => 'integer',
        'week_number' => 'integer',
        'month_number' => 'integer',
    ];

    /**
     * Scope a query to only include active schedules.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->orderBy('day_of_week', 'asc')
            ->orderBy('start_time', 'asc');
    }

    /**
     * Get the day of week as an integer (1 = Monday, 7 = Sunday).
     *
     * @return int
     */
    public function getDayOfWeekNumberAttribute()
    {
        $days = [
            'monday' => 1,
            'tuesday' => 2,
            'wednesday' => 3,
            'thursday' => 4,
            'friday' => 5,
            'saturday' => 6,
            'sunday' => 7,
        ];

        return $days[strtolower($this->day_of_week)] ?? 0;
    }

    /**
     * Get formatted start time.
     *
     * @return string
     */
    public function getFormattedStartTimeAttribute()
    {
        return $this->start_time->format('H:i');
    }

    /**
     * Get formatted end time.
     *
     * @return string
     */
    public function getFormattedEndTimeAttribute()
    {
        return $this->end_time->format('H:i');
    }

    /**
     * Get the formatted week number (e.g., "Week 1").
     *
     * @return string|null
     */
    public function getFormattedWeekNumberAttribute()
    {
        if ($this->week_number) {
            return 'Week ' . $this->week_number;
        }

        return null;
    }

    /**
     * Get the month name based on month number.
     *
     * @return string|null
     */
    public function getMonthNameAttribute()
    {
        if ($this->month_number) {
            $months = [
                1 => 'January',
                2 => 'February',
                3 => 'March',
                4 => 'April',
                5 => 'May',
                6 => 'June',
                7 => 'July',
                8 => 'August',
                9 => 'September',
                10 => 'October',
                11 => 'November',
                12 => 'December',
            ];

            return $months[$this->month_number] ?? null;
        }

        return null;
    }
}
