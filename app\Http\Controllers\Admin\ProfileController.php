<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Profile;
use App\Models\Timeline;
use App\Services\ImageService;
use Illuminate\Http\Request;

class ProfileController extends Controller
{
    /**
     * The image service instance.
     *
     * @var \App\Services\ImageService
     */
    protected $imageService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\ImageService  $imageService
     * @return void
     */
    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Display a listing of the profiles.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $profiles = \App\Models\Profile::all();
        return view('admin.profiles.index', compact('profiles'));
    }

    /**
     * Show the form for creating a new profile.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $timelines = [];
        return view('admin.profiles.create', compact('timelines'));
    }

    /**
     * Store a newly created profile in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'content' => 'required|string',
            'content_en' => 'required|string',
            'type' => 'required|string|in:motto,vision,mission,history',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->all();

        // Handle image upload for profile
        if ($request->hasFile('image')) {
            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'profiles',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }

        $profile = Profile::create($data);

        // Handle timeline entries if profile type is history
        if ($profile->type === 'history' && $request->has('timelines')) {
            foreach ($request->timelines as $index => $timelineData) {
                if (!empty($timelineData['year']) && !empty($timelineData['title'])) {
                    $timelineData['order'] = $index;

                    // Handle image upload for timeline entry
                    if (isset($timelineData['image']) && $timelineData['image'] instanceof \Illuminate\Http\UploadedFile) {
                        $imagePath = $this->imageService->compressAndSave(
                            $timelineData['image'],
                            'timelines',
                            config('image.quality'),
                            config('image.max_width'),
                            config('image.max_height')
                        );
                        $timelineData['image'] = $imagePath;
                    }

                    $profile->timelines()->create($timelineData);
                }
            }
        }

        return redirect()->route('admin.profiles.index')
            ->with('success', 'Profile created successfully.');
    }

    /**
     * Display the specified profile.
     *
     * @param  \App\Models\Profile  $profile
     * @return \Illuminate\View\View
     */
    public function show(Profile $profile)
    {
        $timelines = [];
        if ($profile->type === 'history') {
            $timelines = $profile->timelines()->orderBy('order')->get();
        }
        return view('admin.profiles.show', compact('profile', 'timelines'));
    }

    /**
     * Show the form for editing the specified profile.
     *
     * @param  \App\Models\Profile  $profile
     * @return \Illuminate\View\View
     */
    public function edit(Profile $profile)
    {
        $timelines = [];
        if ($profile->type === 'history') {
            $timelines = $profile->timelines()->orderBy('order')->get();
        }
        return view('admin.profiles.edit', compact('profile', 'timelines'));
    }

    /**
     * Update the specified profile in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Profile  $profile
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Profile $profile)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'content' => 'required|string',
            'content_en' => 'required|string',
            'type' => 'required|string|in:motto,vision,mission,history',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->all();

        // Handle image upload for profile
        if ($request->hasFile('image')) {
            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'profiles',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        } else {
            // If no new image is uploaded, keep the existing one
            unset($data['image']);
        }

        $profile->update($data);

        // Handle timeline entries if profile type is history
        if ($profile->type === 'history' && $request->has('timelines')) {
            // Delete existing timeline entries that are not in the request
            $timelineIds = collect($request->timelines)->pluck('id')->filter()->toArray();
            $profile->timelines()->whereNotIn('id', $timelineIds)->delete();

            // Update or create timeline entries
            foreach ($request->timelines as $index => $timelineData) {
                if (!empty($timelineData['year']) && !empty($timelineData['title'])) {
                    $timelineData['order'] = $index;

                    // Handle image upload for timeline entry
                    if (isset($timelineData['image']) && $timelineData['image'] instanceof \Illuminate\Http\UploadedFile) {
                        $imagePath = $this->imageService->compressAndSave(
                            $timelineData['image'],
                            'timelines',
                            config('image.quality'),
                            config('image.max_width'),
                            config('image.max_height')
                        );
                        $timelineData['image'] = $imagePath;
                    }

                    if (!empty($timelineData['id'])) {
                        $timeline = Timeline::find($timelineData['id']);
                        if ($timeline) {
                            // If no new image is uploaded, keep the existing one
                            if (!isset($timelineData['image']) || !$timelineData['image']) {
                                unset($timelineData['image']);
                            }
                            $timeline->update($timelineData);
                        }
                    } else {
                        $profile->timelines()->create($timelineData);
                    }
                }
            }
        }

        return redirect()->route('admin.profiles.index')
            ->with('success', 'Profile updated successfully.');
    }

    /**
     * Remove the specified profile from storage.
     *
     * @param  \App\Models\Profile  $profile
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(\App\Models\Profile $profile)
    {
        $profile->delete();

        return redirect()->route('admin.profiles.index')
            ->with('success', 'Profile deleted successfully.');
    }
}
