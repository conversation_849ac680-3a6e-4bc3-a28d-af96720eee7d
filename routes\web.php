<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\GalleryController;
use App\Http\Controllers\FacilityController;
use App\Http\Controllers\ProgramController;
use App\Http\Controllers\RegistrationController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\VideoController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\ProfileController as AdminProfileController;
use App\Http\Controllers\Admin\NewsController as AdminNewsController;
use App\Http\Controllers\Admin\GalleryController as AdminGalleryController;
use App\Http\Controllers\Admin\FacilityController as AdminFacilityController;
use App\Http\Controllers\Admin\ProgramController as AdminProgramController;
use App\Http\Controllers\Admin\RegistrationController as AdminRegistrationController;
use App\Http\Controllers\Admin\ContactController as AdminContactController;
use App\Http\Controllers\Admin\SettingController as AdminSettingController;
use App\Http\Controllers\Admin\WebsiteIdentityController;
use App\Http\Controllers\Admin\LeaderController;
use App\Http\Controllers\Admin\NewsCategoryController;
use App\Http\Controllers\Admin\VideoController as AdminVideoController;
use App\Http\Controllers\Admin\EducationUnitController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\AcademicCalendarController;
use App\Http\Controllers\Admin\ActivityScheduleController;
use App\Http\Controllers\Admin\AnnouncementController;
use App\Http\Controllers\Admin\AgendaController;
use App\Http\Controllers\Admin\CurriculumController;
use App\Http\Controllers\Admin\DirectorInsightController;
use App\Http\Controllers\Admin\DirectorController;
use App\Http\Controllers\Admin\InsightController;
use App\Http\Controllers\Admin\MenuController;
use App\Http\Controllers\Admin\SwiperController;
use App\Http\Controllers\Admin\ConfigurationController;
use App\Http\Controllers\Admin\PageController as AdminPageController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\ArtworkController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Language Switcher
Route::get('/language/{locale}', [LanguageController::class, 'change'])->name('language.change');

// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');

// Test route
Route::get('/test', function() {
    return 'The application is working!';
});

// Generate literature placeholder data
Route::get('/generate-literature', [\App\Http\Controllers\Admin\LiteratureGeneratorController::class, 'generate']);

// Update news publish status
Route::get('/update-news-publish-status', [HomeController::class, 'updateNewsPublishStatus']);

// Create Berita category
Route::get('/create-berita-category', [HomeController::class, 'createBeritaCategory']);

// Update news category
Route::get('/update-news-category', [HomeController::class, 'updateNewsCategory']);
// Profile Routes
Route::get('/profile', [ProfileController::class, 'index'])->name('profile');
Route::get('/profile/vision-mission', [ProfileController::class, 'visionMission'])->name('profile.vision-mission');
Route::get('/profile/history', [ProfileController::class, 'history'])->name('profile.history');

// News Routes
Route::get('/news', [NewsController::class, 'index'])->name('news');
Route::get('/news/category/{slug}', [NewsController::class, 'category'])->name('news.category');
Route::get('/news/{slug}', [NewsController::class, 'show'])->name('news.show');

// Announcements Routes
Route::get('/announcements', [App\Http\Controllers\AnnouncementController::class, 'index'])->name('announcements');
Route::get('/announcements/{id}', [App\Http\Controllers\AnnouncementController::class, 'show'])->name('announcements.show');

// Agenda Routes
Route::get('/agenda', [App\Http\Controllers\AgendaController::class, 'index'])->name('agenda');
Route::get('/agenda/{id}', [App\Http\Controllers\AgendaController::class, 'show'])->name('agenda.show');

// Gallery Routes
Route::get('/gallery', [GalleryController::class, 'index'])->name('gallery');
Route::get('/gallery/category/{category}', [GalleryController::class, 'category'])->name('gallery.category');

// Facility Routes
Route::get('/facilities', [FacilityController::class, 'index'])->name('facilities');

// Program Routes
Route::get('/programs', [ProgramController::class, 'index'])->name('programs');

// Registration Routes
Route::get('/registration', [RegistrationController::class, 'index'])->name('registration');
Route::post('/registration', [RegistrationController::class, 'store'])->name('registration.store');
Route::get('/registration/success/{registration}', [RegistrationController::class, 'success'])->name('registration.success');
Route::get('/registration/print/{registration}', [RegistrationController::class, 'printReceipt'])->name('registration.print');
Route::post('/registration/check-status', [RegistrationController::class, 'checkStatus'])->name('registration.check-status');

// Contact Routes
Route::get('/contact', [ContactController::class, 'index'])->name('contact');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

// Leaders Routes
Route::get('/leaders', [App\Http\Controllers\LeaderController::class, 'index'])->name('leaders');
Route::get('/leaders/{id}', [App\Http\Controllers\LeaderController::class, 'show'])->name('leaders.show');

// Education Units Routes
Route::get('/education-units', [App\Http\Controllers\EducationUnitController::class, 'index'])->name('education-units');
Route::get('/education-units/{id}', [App\Http\Controllers\EducationUnitController::class, 'show'])->name('education-units.show');

// Achievements Routes
Route::get('/achievements', [App\Http\Controllers\AchievementController::class, 'index'])->name('achievements');
Route::get('/achievements/{id}', [App\Http\Controllers\AchievementController::class, 'show'])->name('achievements.show');

// Videos Routes
Route::get('/videos', [VideoController::class, 'index'])->name('videos');
Route::get('/videos/{id}', [VideoController::class, 'show'])->name('videos.show');

// Insights Routes
Route::get('/insights/{id}', [App\Http\Controllers\InsightController::class, 'show'])->name('insights.show');

// Pages Routes
Route::get('/pages', [PageController::class, 'index'])->name('pages');
Route::get('/pages/{slug}', [PageController::class, 'show'])->name('pages.show');

// Academic Calendar Routes
Route::get('/calendars', [App\Http\Controllers\AcademicCalendarController::class, 'index'])->name('calendars.index');
Route::get('/calendars/{id}', [App\Http\Controllers\AcademicCalendarController::class, 'show'])->name('calendars.show');

// Activity Schedule Routes
Route::get('/schedules', [App\Http\Controllers\ActivityScheduleController::class, 'index'])->name('schedules.index');

// Artwork Routes
Route::get('/artwork', [ArtworkController::class, 'index'])->name('artwork');
// Paint Routes
Route::get('/artwork/paints', [ArtworkController::class, 'paints'])->name('artwork.paints');
Route::get('/artwork/paints/type/{typeSlug}', [ArtworkController::class, 'paintsByType'])->name('artwork.paints.type');
Route::get('/artwork/paints/{id}', [ArtworkController::class, 'showPaint'])->name('artwork.paints.show');
// Literature Routes
Route::get('/artwork/literature', [ArtworkController::class, 'literature'])->name('artwork.literature');
Route::get('/artwork/literature/type/{typeSlug}', [ArtworkController::class, 'literatureByType'])->name('artwork.literature.type');
Route::get('/artwork/literature/{id}', [ArtworkController::class, 'showLiterature'])->name('artwork.literature.show');

// Authentication Routes
Route::prefix('auth')->group(function () {
    Route::middleware('guest')->group(function () {
        Route::get('login', [App\Http\Controllers\Auth\LoginController::class, 'showLoginForm'])->name('login');
        Route::post('login', [App\Http\Controllers\Auth\LoginController::class, 'login']);
        Route::get('password/reset', [App\Http\Controllers\Auth\ForgotPasswordController::class, 'showLinkRequestForm'])->name('password.request');
        Route::post('password/email', [App\Http\Controllers\Auth\ForgotPasswordController::class, 'sendResetLinkEmail'])->name('password.email');
        Route::get('password/reset/{token}', [App\Http\Controllers\Auth\ResetPasswordController::class, 'showResetForm'])->name('password.reset');
        Route::post('password/reset', [App\Http\Controllers\Auth\ResetPasswordController::class, 'reset'])->name('password.update');
        Route::get('password/confirm', [App\Http\Controllers\Auth\ConfirmPasswordController::class, 'showConfirmForm'])->name('password.confirm');
        Route::post('password/confirm', [App\Http\Controllers\Auth\ConfirmPasswordController::class, 'confirm']);
    });

    Route::middleware('auth')->group(function () {
        Route::post('logout', [App\Http\Controllers\Auth\LoginController::class, 'logout'])->name('logout');

        // Email Verification Routes
        Route::get('email/verify', [App\Http\Controllers\Auth\VerificationController::class, 'show'])->name('verification.notice');
        Route::get('email/verify/{id}/{hash}', [App\Http\Controllers\Auth\VerificationController::class, 'verify'])->name('verification.verify')->middleware(['signed']);
        Route::post('email/resend', [App\Http\Controllers\Auth\VerificationController::class, 'resend'])->name('verification.resend')->middleware(['throttle:6,1']);
    });
});

// Admin Routes
Route::prefix('admin')->middleware(['auth'])->group(function () {
    // Dashboard - accessible by all roles
    Route::get('/', [DashboardController::class, 'index'])->name('admin.dashboard');

    // Translation API - accessible by all roles
    Route::post('/translate', [\App\Http\Controllers\Admin\TranslationController::class, 'translate'])->name('admin.translate');

    // Summernote image upload - accessible by all roles
    Route::post('/upload-summernote-image', [\App\Http\Controllers\Admin\UploadController::class, 'uploadSummernoteImage'])->name('admin.upload.summernote-image');

    // Artwork Management - accessible by all roles
    Route::prefix('artwork')->name('admin.artwork.')->group(function () {
        // Paints
        Route::resource('paints', \App\Http\Controllers\Admin\PaintController::class);

        // Literature
        Route::resource('literature', \App\Http\Controllers\Admin\LiteratureController::class);
    });

    // User Profile - accessible by all roles
    Route::get('/profile', [\App\Http\Controllers\Admin\UserProfileController::class, 'edit'])->name('admin.profile.edit');
    Route::put('/profile', [\App\Http\Controllers\Admin\UserProfileController::class, 'update'])->name('admin.profile.update');
    Route::get('/profile/change-password', [\App\Http\Controllers\Admin\UserProfileController::class, 'showChangePasswordForm'])->name('admin.profile.change-password');
    Route::put('/profile/change-password', [\App\Http\Controllers\Admin\UserProfileController::class, 'changePassword'])->name('admin.profile.change-password');

    // Routes for Admin only
    Route::middleware(['admin'])->group(function () {
        // Settings Management
        Route::prefix('settings')->name('admin.settings.')->group(function () {
            // Configuration Management
            Route::get('/configuration', [ConfigurationController::class, 'index'])->name('configuration.index');
            Route::post('/configuration', [ConfigurationController::class, 'update'])->name('configuration.update');


            // Users Management
            Route::get('/users', [UserController::class, 'index'])->name('users.index');
            Route::post('/users', [UserController::class, 'store'])->name('users.store');
            Route::put('/users/{user}', [UserController::class, 'update'])->name('users.update');
            Route::put('/users/{user}/change-password', [UserController::class, 'changePassword'])->name('users.change-password');
            Route::delete('/users/{user}', [UserController::class, 'destroy'])->name('users.destroy');
        });

        // Director's Insight Management
        Route::get('/directors-insight', [DirectorInsightController::class, 'index'])->name('admin.directors-insight.index');
        Route::resource('directors', DirectorController::class, ['as' => 'admin']);
        Route::resource('insights', InsightController::class, ['as' => 'admin']);
    });

    // Routes for Operator - can access everything except Settings and Director's Insight
    Route::middleware(['operator'])->group(function () {
        // Configuration for Operator
        Route::get('/settings/configuration', [ConfigurationController::class, 'index'])
            ->name('admin.settings.configuration.operator.index');
        Route::post('/settings/configuration', [ConfigurationController::class, 'update'])
            ->name('admin.settings.configuration.operator.update');

        // Literature Types Management
        Route::resource('artwork/types', \App\Http\Controllers\Admin\LitTypeController::class, ['as' => 'admin.artwork']);

        // Profile Management
        Route::resource('profiles', AdminProfileController::class, ['as' => 'admin']);

        // News Category Management
        Route::resource('categories', NewsCategoryController::class, ['as' => 'admin']);

        // Facility Management
        Route::resource('facilities', AdminFacilityController::class, ['as' => 'admin']);

        // Program Management
        Route::resource('programs', AdminProgramController::class, ['as' => 'admin']);

        // Registration Management
        Route::get('/registrations/export', [AdminRegistrationController::class, 'export'])->name('admin.registrations.export');
        Route::resource('registrations', AdminRegistrationController::class, ['as' => 'admin']);
        Route::post('/registrations/{registration}/approve', [AdminRegistrationController::class, 'approve'])->name('admin.registrations.approve');
        Route::post('/registrations/{registration}/reject', [AdminRegistrationController::class, 'reject'])->name('admin.registrations.reject');

        // Registration Management - Schedules
        Route::post('/registrations/schedules', [AdminRegistrationController::class, 'storeSchedule'])->name('admin.registrations.schedules.store');
        Route::put('/registrations/schedules/{schedule}', [AdminRegistrationController::class, 'updateSchedule'])->name('admin.registrations.schedules.update');
        Route::delete('/registrations/schedules/{schedule}', [AdminRegistrationController::class, 'destroySchedule'])->name('admin.registrations.schedules.destroy');

        // Registration Management - Requirements
        Route::post('/registrations/requirements', [AdminRegistrationController::class, 'storeRequirement'])->name('admin.registrations.requirements.store');
        Route::put('/registrations/requirements/{requirement}', [AdminRegistrationController::class, 'updateRequirement'])->name('admin.registrations.requirements.update');
        Route::delete('/registrations/requirements/{requirement}', [AdminRegistrationController::class, 'destroyRequirement'])->name('admin.registrations.requirements.destroy');

        // Registration Management - Fees
        Route::post('/registrations/fees', [AdminRegistrationController::class, 'storeFee'])->name('admin.registrations.fees.store');
        Route::put('/registrations/fees/{fee}', [AdminRegistrationController::class, 'updateFee'])->name('admin.registrations.fees.update');
        Route::delete('/registrations/fees/{fee}', [AdminRegistrationController::class, 'destroyFee'])->name('admin.registrations.fees.destroy');

        // Registration Management - Info
        Route::post('/registrations/info', [AdminRegistrationController::class, 'storeInfo'])->name('admin.registrations.info.store');

        // Contact Management
        Route::resource('contacts', AdminContactController::class, ['as' => 'admin']);
        Route::post('/contacts/{contact}/mark-as-read', [AdminContactController::class, 'markAsRead'])->name('admin.contacts.mark-as-read');
        Route::post('/contacts/{contact}/reply', [AdminContactController::class, 'reply'])->name('admin.contacts.reply');

        // Website Management
        Route::get('/website/identity', [WebsiteIdentityController::class, 'index'])->name('admin.website.identity');
        Route::post('/website/identity', [WebsiteIdentityController::class, 'update'])->name('admin.website.identity.update');

        // Swiper Management
        Route::resource('swipers', SwiperController::class, ['as' => 'admin']);

        // Menu Management
        Route::get('/website/menu', [MenuController::class, 'vueIndex'])->name('admin.website.menu');
        Route::get('/website/menu/classic', [MenuController::class, 'index'])->name('admin.website.menu.classic');
        Route::get('/website/menu/create', [MenuController::class, 'create'])->name('admin.website.menu.create');
        Route::post('/website/menu', [MenuController::class, 'store'])->name('admin.website.menu.store');
        Route::get('/website/menu/{id}/edit', [MenuController::class, 'edit'])->name('admin.website.menu.edit');
        Route::put('/website/menu/{id}', [MenuController::class, 'update'])->name('admin.website.menu.update');
        Route::delete('/website/menu/{id}', [MenuController::class, 'destroy'])->name('admin.website.menu.destroy');
        Route::post('/website/menu/update-order', [MenuController::class, 'updateOrder'])->name('admin.website.menu.update-order');

        // Pages Management
        Route::resource('pages', AdminPageController::class, ['as' => 'admin']);

        // Menu API Endpoints
        Route::prefix('api/menu')->name('api.menu.')->group(function () {
            Route::get('/menus', [App\Http\Controllers\Admin\MenuApiController::class, 'getMenus'])->name('menus');
            Route::get('/items', [App\Http\Controllers\Admin\MenuApiController::class, 'getMenuItems'])->name('items');
            Route::post('/items', [App\Http\Controllers\Admin\MenuApiController::class, 'createMenuItem'])->name('items.create');
            Route::put('/items/{id}', [App\Http\Controllers\Admin\MenuApiController::class, 'updateMenuItem'])->name('items.update');
            Route::delete('/items/{id}', [App\Http\Controllers\Admin\MenuApiController::class, 'deleteMenuItem'])->name('items.delete');
            Route::post('/items/update-order', [App\Http\Controllers\Admin\MenuApiController::class, 'updateMenuItemsOrder'])->name('items.update-order');
        });

        // Leaders Management
        Route::resource('leaders', LeaderController::class, ['as' => 'admin']);

        // Education Units Management
        Route::resource('education-units', EducationUnitController::class, ['as' => 'admin']);

        // Partnerships Management
        Route::resource('partnerships', \App\Http\Controllers\Admin\PartnershipController::class, ['as' => 'admin']);

        // Curriculum Management
        Route::resource('curricula', CurriculumController::class, ['as' => 'admin']);
    });

    // Routes for Editor - can only access News, Media, Agenda, Announcements, Achievements
    Route::middleware(['editor'])->group(function () {
        // News Management
        Route::resource('news', AdminNewsController::class, ['as' => 'admin']);

        // Gallery Management
        Route::resource('galleries', AdminGalleryController::class, ['as' => 'admin']);

        // Videos Management
        Route::resource('videos', AdminVideoController::class, ['as' => 'admin']);

        // Achievements Management
        Route::resource('achievements', \App\Http\Controllers\Admin\AchievementController::class, ['as' => 'admin']);

        // Announcement Management
        Route::resource('announcements', AnnouncementController::class, ['as' => 'admin']);

        // Agenda Management
        Route::resource('agendas', AgendaController::class, ['as' => 'admin']);

        // Academic Calendar Management
        Route::resource('calendars', AcademicCalendarController::class, ['as' => 'admin']);

        // Activity Schedule Management
        Route::resource('schedules', ActivityScheduleController::class, ['as' => 'admin']);


    });
});

// Redirect /home to admin dashboard for authenticated users
Route::get('/home', function() {
    return redirect()->route('admin.dashboard');
});


