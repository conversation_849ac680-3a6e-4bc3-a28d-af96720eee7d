<!-- Latest Announcements -->
<div class="card border-0 shadow-sm mb-4 fade-in">
    <div class="card-header bg-warning text-white">
        <h5 class="mb-0">{{ app()->getLocale() == 'id' ? 'Pengumuman Terbaru' : 'Latest Announcements' }}</h5>
    </div>
    <div class="card-body">
        @forelse($latestAnnouncements as $announcement)
            <div class="related-item mb-3 pb-3 {{ !$loop->last ? 'border-bottom' : '' }}">
                <div class="small text-danger mb-1">
                    <i class="far fa-calendar-alt me-1"></i>
                    {{ $announcement->start_date->format('d M Y') }} - {{ $announcement->end_date->format('d M Y') }}
                </div>
                <h6 class="mb-0">
                    <a href="{{ route('announcements.show', $announcement->id) }}" class="text-decoration-none">
                        {{ app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit($announcement->title, 70) : \Illuminate\Support\Str::limit($announcement->title_en, 70) }}
                    </a>
                </h6>
            </div>
        @empty
            <p class="mb-0">{{ app()->getLocale() == 'id' ? 'Tidak ada pengumuman terbaru.' : 'No latest announcements.' }}</p>
        @endforelse
    </div>
</div>
