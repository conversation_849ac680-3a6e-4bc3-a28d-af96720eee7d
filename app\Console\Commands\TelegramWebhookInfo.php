<?php

namespace App\Console\Commands;

use App\Models\Setting;
use App\Services\TelegramBotService;
use Illuminate\Console\Command;

class TelegramWebhookInfo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:webhook:info';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get information about the current Telegram webhook';

    /**
     * The Telegram Bot service instance.
     *
     * @var \App\Services\TelegramBotService
     */
    protected $telegramService;

    /**
     * Create a new command instance.
     *
     * @param  \App\Services\TelegramBotService  $telegramService
     * @return void
     */
    public function __construct(TelegramBotService $telegramService)
    {
        parent::__construct();
        $this->telegramService = $telegramService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Getting Telegram webhook information...');

        $result = $this->telegramService->getWebhookInfo();

        if (!$result || !isset($result['ok']) || !$result['ok']) {
            $this->error('Failed to get webhook information.');
            if ($result && isset($result['description'])) {
                $this->error('Error: ' . $result['description']);
            }
            return 1;
        }

        $this->info('Webhook Information:');
        $this->info('URL: ' . ($result['result']['url'] ?? 'Not set'));
        $this->info('Has custom certificate: ' . (($result['result']['has_custom_certificate'] ?? false) ? 'Yes' : 'No'));
        $this->info('Pending update count: ' . ($result['result']['pending_update_count'] ?? 0));
        $this->info('Max connections: ' . ($result['result']['max_connections'] ?? 'Default'));
        
        if (isset($result['result']['last_error_date'])) {
            $this->error('Last error date: ' . date('Y-m-d H:i:s', $result['result']['last_error_date']));
            $this->error('Last error message: ' . ($result['result']['last_error_message'] ?? 'Unknown error'));
        } else {
            $this->info('No errors reported.');
        }

        return 0;
    }
}
