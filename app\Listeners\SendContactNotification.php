<?php

namespace App\Listeners;

use App\Models\Contact;
use App\Models\Setting;
use Illuminate\Support\Facades\Log;

class SendContactNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        // Constructor
    }

    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        Log::info('SendContactNotification listener triggered');

        // Check if the event has a contact
        if (!isset($event->contact) || !($event->contact instanceof Contact)) {
            Log::warning('Event does not have a valid contact object');
            return;
        }

        $contact = $event->contact;
        Log::info('Processing contact notification', ['contact_id' => $contact->id, 'name' => $contact->name]);

        // Here you could implement other notification methods like email
        // For now, we'll just log that we received the contact
        Log::info('Contact message received', [
            'contact_id' => $contact->id,
            'name' => $contact->name,
            'email' => $contact->email,
            'subject' => $contact->subject
        ]);
    }
}
