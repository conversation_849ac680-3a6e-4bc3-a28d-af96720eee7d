<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\Setting;
use App\Services\ImageService;

class WebsiteIdentityController extends Controller
{
    /**
     * The image service instance.
     *
     * @var \App\Services\ImageService
     */
    protected $imageService;

    /**
     * Create a new controller instance.
     *
     * @param \App\Services\ImageService $imageService
     * @return void
     */
    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Display the website identity form.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // Get all settings
        $settings = Setting::pluck('value', 'key')->toArray();

        return view('admin.website.identity.index', compact('settings'));
    }

    /**
     * Update the website identity.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        // Validate the request
        $request->validate([
            'site_name' => 'required|string|max:255',
            'site_name_en' => 'nullable|string|max:255',
            'institution_name' => 'nullable|string|max:255',
            'institution_name_en' => 'nullable|string|max:255',
            'site_description' => 'nullable|string',
            'site_description_en' => 'nullable|string',
            'email' => 'required|email|max:255',
            'domain' => 'required|string|max:255',
            'phone' => 'required|string|max:255',
            'logo_file' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'favicon_file' => 'nullable|file|mimes:ico,png|max:100', // limit to ICO and PNG only
            'address' => 'nullable|string',
            'address_en' => 'nullable|string',
            'map_url' => 'nullable|string',
            'facebook' => 'nullable|string|max:255',
            'twitter' => 'nullable|string|max:255',
            'instagram' => 'nullable|string|max:255',
            'youtube' => 'nullable|string|max:255',
            'whatsapp' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'meta_description_en' => 'nullable|string',
            'meta_keywords' => 'nullable|string',
            'meta_keywords_en' => 'nullable|string',
            'google_analytics' => 'nullable|string|max:255',
        ]);

        // Get all form data except CSRF token and file uploads
        $data = $request->except(['_token', 'logo_file', 'favicon_file']);

        // Handle logo upload
        if ($request->hasFile('logo_file')) {
            // Delete old logo if exists
            $oldLogo = Setting::where('key', 'logo')->first();
            if ($oldLogo && $oldLogo->value) {
                Log::info("Attempting to delete old logo: " . $oldLogo->value);
                $deleteResult = $this->imageService->deleteImage($oldLogo->value);
                Log::info("Delete old logo result: " . ($deleteResult ? 'success' : 'failed'));
            } else {
                Log::info("No old logo found to delete");
            }

            // Upload new logo
            $logoPath = $this->imageService->compressAndSave(
                $request->file('logo_file'),
                'website',
                config('image.quality'),
                200, // Specific width for logo
                60   // Specific height for logo
            );

            // Add to data array
            $data['logo'] = $logoPath;

            // Add debug message
            Log::info("New logo uploaded to: " . $logoPath);
            session()->flash('debug', 'Logo uploaded to: ' . $logoPath);
        }

        // Handle favicon upload
        if ($request->hasFile('favicon_file')) {
            // Delete old favicon if exists
            $oldFavicon = Setting::where('key', 'favicon')->first();
            if ($oldFavicon && $oldFavicon->value) {
                Log::info("Attempting to delete old favicon: " . $oldFavicon->value);
                $deleteResult = $this->imageService->deleteImage($oldFavicon->value);
                Log::info("Delete old favicon result: " . ($deleteResult ? 'success' : 'failed'));
            } else {
                Log::info("No old favicon found to delete");
            }

            // Upload new favicon using dedicated method
            $faviconPath = $this->imageService->saveFavicon(
                $request->file('favicon_file'),
                'website'
            );

            // Add to data array
            $data['favicon'] = $faviconPath;

            // Add debug message
            Log::info("New favicon uploaded to: " . $faviconPath);
        }

        // Update or create settings for all fields
        foreach ($data as $key => $value) {
            Setting::updateOrCreate(
                ['key' => $key],
                ['value' => $value]
            );
        }

        // Clear settings cache
        \Illuminate\Support\Facades\Cache::forget('settings_all');

        // Clear individual setting caches
        foreach ($data as $key => $value) {
            \Illuminate\Support\Facades\Cache::forget('setting_' . $key);
        }

        // Clear logo and favicon caches if they were updated
        if ($request->hasFile('logo_file')) {
            \Illuminate\Support\Facades\Cache::forget('setting_logo');
        }
        if ($request->hasFile('favicon_file')) {
            \Illuminate\Support\Facades\Cache::forget('setting_favicon');
        }

        return redirect()->route('admin.website.identity')->with('success', 'Website settings updated successfully.');
    }
}


