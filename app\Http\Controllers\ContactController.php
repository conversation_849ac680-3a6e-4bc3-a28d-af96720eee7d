<?php

namespace App\Http\Controllers;

use App\Events\ContactCreated;
use App\Models\Contact;
use App\Models\Setting;
use App\Services\EmailService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ContactController extends Controller
{
    /**
     * Display the contact page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get contact information from settings
        $address = Setting::getValue('address');
        $phone = Setting::getValue('phone');
        $email = Setting::getValue('email');
        $mapUrl = Setting::getValue('map_url');

        return view('public.contact.index', compact('address', 'phone', 'email', 'mapUrl'));
    }

    /**
     * Store a newly created contact message in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        // Create contact message
        $contact = Contact::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'subject' => $request->subject,
            'message' => $request->message,
            'is_read' => false,
        ]);

        // Send auto-reply email to the sender
        $this->sendAutoReplyEmail($contact);

        // Dispatch event for Telegram notification
        event(new ContactCreated($contact));

        return redirect()->back()
            ->with('success', app()->getLocale() == 'id' ?
                'Pesan Anda telah berhasil dikirim!' :
                'Your message has been sent successfully!');
    }

    /**
     * Send auto-reply email to the contact.
     *
     * @param  \App\Models\Contact  $contact
     * @return void
     */
    protected function sendAutoReplyEmail(Contact $contact)
    {
        // Only send email if an email address is provided
        if (!$contact->email) {
            Log::warning("Cannot send auto-reply email: No email address provided for contact ID {$contact->id}");
            return;
        }

        try {
            Log::info("Attempting to send auto-reply email to: {$contact->email}");

            // Use EmailService to send contact auto-reply email
            $emailService = new EmailService();

            // Get the current locale
            $locale = app()->getLocale();

            // Get contact information from settings
            $address = Setting::getValue('address');
            $phone = Setting::getValue('phone');
            $email = Setting::getValue('email');

            // Prepare data for the email template
            $data = [
                'name' => $contact->name,
                'subject' => $contact->subject,
                'date' => $contact->created_at->format('d M Y H:i'),
                'address' => $address,
                'phone' => $phone,
                'email' => $email,
            ];

            // Try to send email using the 'notifications' email type with the template
            $result = $emailService->sendEmailWithView(
                $contact->email,
                __('emails.contact_subject'),
                'emails.contact-auto-reply',
                $data,
                'notifications',
                $locale
            );

            if ($result) {
                // Log that we sent an email
                Log::info("Contact auto-reply email sent to: {$contact->email} in {$locale} language");
            } else {
                // Try fallback method using default mailer
                Log::warning("Primary email method failed, trying fallback...");

                try {
                    // Use Laravel's Mail facade directly with the default mailer
                    Mail::send('emails.contact-auto-reply', $data, function ($message) use ($contact) {
                        $message->to($contact->email)
                            ->subject(__('emails.contact_subject'));
                    });

                    Log::info("Fallback email sent successfully to: {$contact->email}");
                } catch (\Exception $fallbackEx) {
                    Log::error("Fallback email also failed: " . $fallbackEx->getMessage());

                    // Log the contact information for manual follow-up
                    Log::warning("Contact requires manual email follow-up: ID={$contact->id}, Email={$contact->email}, Name={$contact->name}");
                }
            }
        } catch (\Exception $e) {
            // Log the error but don't fail the contact process
            Log::error("Failed to send contact auto-reply email: " . $e->getMessage());
            Log::error("Stack trace: " . $e->getTraceAsString());
        }
    }
}
