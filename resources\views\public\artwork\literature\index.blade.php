@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? '<PERSON><PERSON>' : 'Literature')
@section('meta_description', app()->getLocale() == 'id' ? 'Koleksi karya sastra dari para penulis kami' : 'Collection of literature from our writers')

@section('content')
    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="page-title" data-aos="fade-up">{{ app()->getLocale() == 'id' ? 'Karya Sastra' : 'Literature' }}</h1>
                    <nav aria-label="breadcrumb" data-aos="fade-up" data-aos-delay="100">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('artwork') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Karya Seni' : 'Artwork' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? 'Karya Sastra' : 'Literature' }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Literature Grid -->
    <section class="section-padding">
        <div class="container">
            @if(isset($type))
                <div class="row mb-4">
                    <div class="col-12 text-center">
                        <h2 data-aos="fade-up">{{ app()->getLocale() == 'id' ? $type->name : $type->name_en }}</h2>
                        @if($type->description || $type->description_en)
                            <p class="lead" data-aos="fade-up" data-aos-delay="100">
                                {{ app()->getLocale() == 'id' ? $type->description : $type->description_en }}
                            </p>
                        @endif
                    </div>
                </div>
            @endif

            <div class="row">
                <!-- Literature Content - 9 columns -->
                <div class="col-lg-9">
                    <div class="row justify-content-center">
                        @forelse($literature as $lit)
                            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="{{ $loop->iteration % 3 * 100 }}">
                                <div class="literature-card">
                                    <div class="literature-image">
                                        @if($lit->image)
                                            <a href="{{ route('artwork.literature.show', $lit->id) }}">
                                                <img src="{{ asset('storage/' . $lit->image) }}" alt="{{ app()->getLocale() == 'id' ? $lit->title : $lit->title_en }}">
                                            </a>
                                        @else
                                            <a href="{{ route('artwork.literature.show', $lit->id) }}">
                                                <div class="literature-placeholder">
                                                    <i class="fas fa-book fa-3x"></i>
                                                </div>
                                            </a>
                                        @endif
                                    </div>
                                    <div class="literature-info bg-white p-3">
                                        <h5><a href="{{ route('artwork.literature.show', $lit->id) }}" class="text-decoration-none">{{ app()->getLocale() == 'id' ? $lit->title : $lit->title_en }}</a></h5>
                                        <p class="author mb-1"><i class="fas fa-user-alt me-2"></i>{{ app()->getLocale() == 'id' ? $lit->author : $lit->author_en }}</p>
                                        @if($lit->year)
                                            <p class="year mb-3"><i class="fas fa-calendar-alt me-2"></i>{{ $lit->year }}</p>
                                        @endif
                                        <div class="literature-excerpt mb-3">
                                            {{ \Illuminate\Support\Str::limit(strip_tags(app()->getLocale() == 'id' ? $lit->content : $lit->content_en), 100) }}
                                        </div>
                                        @if($lit->file)
                                            <a href="{{ asset('storage/' . $lit->file) }}" class="btn btn-sm btn-outline-success ms-2" target="_blank">
                                                <i class="fas fa-download me-1"></i> {{ app()->getLocale() == 'id' ? 'Unduh' : 'Download' }}
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="col-12 text-center">
                                <div class="alert alert-info">
                                    {{ app()->getLocale() == 'id' ? 'Belum ada karya sastra yang tersedia.' : 'No literature available yet.' }}
                                </div>
                            </div>
                        @endforelse
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $literature->links() }}
                    </div>
                </div>

                <!-- Submission Info Card - 3 columns -->
                <div class="col-lg-3" data-aos="fade-up" data-aos-delay="200">
                    <div class="sticky-top" style="top: 20px; z-index: 10;">
                        <div class="literature-card submission-card">
                            <div class="literature-image submission-image">
                                <div class="submission-icon">
                                    <i class="fas fa-paper-plane fa-3x"></i>
                                </div>
                            </div>
                            <div class="literature-info bg-white p-3">
                                <h5 class="text-center">{{ app()->getLocale() == 'id' ? 'Kirimkan Karyamu' : 'Submit Your Work' }}</h5>
                                <div class="submission-info mb-3">
                                    <p>{{ app()->getLocale() == 'id' ? 'Anda dapat mengirimkan karya sastra Anda ke redaksi kami melalui email:' : 'You can submit your literary work to our editorial team via email:' }}</p>
                                    <p class="text-center"><strong><EMAIL></strong></p>
                                    <p>{{ app()->getLocale() == 'id' ? 'Dengan subjek sesuai tipe karya (Puisi, Cerpen, Esai, dll).' : 'With subject according to the type of work (Poetry, Short Story, Essay, etc).' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
    .page-header {
        background: linear-gradient(rgba(0, 0, 0, 0.65), rgba(0, 0, 0, 0.65)), url('{{ asset('images/header-bg.jpg') }}') center/cover no-repeat;
        padding: 80px 0;
        margin-bottom: 0;
    }

    .page-title {
        color: white;
        font-weight: 700;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .breadcrumb-item, .breadcrumb-item a {
        color: rgba(255, 255, 255, 0.8);
    }

    .breadcrumb-item.active {
        color: white;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        color: rgba(255, 255, 255, 0.6);
    }

    .section-padding {
        padding: 70px 0;
    }

    .literature-card {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        height: 100%;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .literature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .literature-image {
        position: relative;
        height: 200px;
        overflow: hidden;
    }

    .literature-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }

    .literature-card:hover .literature-image img {
        transform: scale(1.1);
    }

    .literature-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        color: #6c757d;
    }

    .author, .year, .type {
        font-size: 0.9rem;
        color: #6c757d;
    }

    .literature-excerpt {
        font-size: 0.9rem;
        color: #6c757d;
        line-height: 1.5;
    }

    .submission-card {
        background-color: #f8f9fa;
        border: 2px dashed #28a745;
    }

    .submission-image {
        background-color: rgba(40, 167, 69, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        height: 150px;
    }

    .submission-icon {
        color: #28a745;
    }

    .submission-info {
        font-size: 0.9rem;
        line-height: 1.6;
    }

    @media (max-width: 991.98px) {
        .sticky-top {
            position: relative !important;
            top: 0 !important;
            margin-top: 2rem;
        }
    }
</style>
@endpush
