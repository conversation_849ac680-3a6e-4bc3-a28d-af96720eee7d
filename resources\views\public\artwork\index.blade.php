@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? 'Karya <PERSON>' : 'Artwork')
@section('meta_description', app()->getLocale() == 'id' ? 'Koleksi karya seni dari para seniman kami' : 'Collection of artwork from our artists')

@section('content')
    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="page-title" data-aos="fade-up">{{ app()->getLocale() == 'id' ? 'Karya Seni' : 'Artwork' }}</h1>
                    <nav aria-label="breadcrumb" data-aos="fade-up" data-aos-delay="100">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? 'Karya Seni' : 'Artwork' }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Paintings -->
    <section class="section-padding">
        <div class="container">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="section-title text-center" data-aos="fade-up">
                        <h2>{{ app()->getLocale() == 'id' ? 'Lukisan Unggulan' : 'Featured Paintings' }}</h2>
                        <div class="divider mx-auto"></div>
                    </div>
                </div>
            </div>

            <div class="row justify-content-center">
                @forelse($paints as $paint)
                    <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="{{ $loop->iteration % 3 * 100 }}">
                        <div class="artwork-card">
                            <div class="artwork-image">
                                @if($paint->image)
                                    <img src="{{ asset('storage/' . $paint->image) }}" alt="{{ app()->getLocale() == 'id' ? $paint->title : $paint->title_en }}">
                                @else
                                    <img src="{{ asset('images/artwork-placeholder.jpg') }}" alt="{{ app()->getLocale() == 'id' ? $paint->title : $paint->title_en }}">
                                @endif
                                <div class="artwork-overlay">
                                    <a href="{{ route('artwork.paints.show', $paint->id) }}" class="artwork-link">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="artwork-info bg-white p-3">
                                <h5><a href="{{ route('artwork.paints.show', $paint->id) }}" class="text-decoration-none">{{ app()->getLocale() == 'id' ? $paint->title : $paint->title_en }}</a></h5>
                                <p class="artist mb-1"><i class="fas fa-user-alt me-2"></i>{{ app()->getLocale() == 'id' ? $paint->artist : $paint->artist_en }}</p>
                                @if($paint->year)
                                    <p class="year mb-0"><i class="fas fa-calendar-alt me-2"></i>{{ $paint->year }}</p>
                                @endif
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12 text-center">
                        <div class="alert alert-info">
                            {{ app()->getLocale() == 'id' ? 'Belum ada lukisan yang tersedia.' : 'No paintings available yet.' }}
                        </div>
                    </div>
                @endforelse
            </div>

            <div class="text-center mt-4" data-aos="fade-up">
                <a href="{{ route('artwork.paints') }}" class="btn btn-outline-success">
                    {{ app()->getLocale() == 'id' ? 'Lihat Semua Lukisan' : 'View All Paintings' }}
                    <i class="fas fa-arrow-right ms-2"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Featured Literature -->
    <section class="section-padding bg-light">
        <div class="container">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="section-title text-center" data-aos="fade-up">
                        <h2>{{ app()->getLocale() == 'id' ? 'Karya Sastra Unggulan' : 'Featured Literature' }}</h2>
                        <div class="divider mx-auto"></div>
                    </div>
                </div>
            </div>

            <div class="row justify-content-center">
                @forelse($literature as $lit)
                    <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="{{ $loop->iteration % 3 * 100 }}">
                        <div class="literature-card">
                            <div class="literature-image">
                                @if($lit->image)
                                    <a href="{{ route('artwork.literature.show', $lit->id) }}">
                                        <img src="{{ asset('storage/' . $lit->image) }}" alt="{{ app()->getLocale() == 'id' ? $lit->title : $lit->title_en }}">
                                    </a>
                                @else
                                    <a href="{{ route('artwork.literature.show', $lit->id) }}">
                                        <div class="literature-placeholder">
                                            <i class="fas fa-book fa-3x"></i>
                                        </div>
                                    </a>
                                @endif
                            </div>
                            <div class="literature-info bg-white p-3">
                                <h5><a href="{{ route('artwork.literature.show', $lit->id) }}" class="text-decoration-none">{{ app()->getLocale() == 'id' ? $lit->title : $lit->title_en }}</a></h5>
                                <p class="author mb-1"><i class="fas fa-user-alt me-2"></i>{{ app()->getLocale() == 'id' ? $lit->author : $lit->author_en }}</p>
                                @if($lit->year)
                                    <p class="year mb-0"><i class="fas fa-calendar-alt me-2"></i>{{ $lit->year }}</p>
                                @endif
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12 text-center">
                        <div class="alert alert-info">
                            {{ app()->getLocale() == 'id' ? 'Belum ada karya sastra yang tersedia.' : 'No literature available yet.' }}
                        </div>
                    </div>
                @endforelse
            </div>

            <div class="text-center mt-4" data-aos="fade-up">
                <a href="{{ route('artwork.literature') }}" class="btn btn-outline-success">
                    {{ app()->getLocale() == 'id' ? 'Lihat Semua Karya Sastra' : 'View All Literature' }}
                    <i class="fas fa-arrow-right ms-2"></i>
                </a>
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
    .page-header {
        background: linear-gradient(rgba(0, 0, 0, 0.65), rgba(0, 0, 0, 0.65)), url('{{ asset('images/header-bg.jpg') }}') center/cover no-repeat;
        padding: 80px 0;
        margin-bottom: 0;
    }

    .page-title {
        color: white;
        font-weight: 700;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .breadcrumb-item, .breadcrumb-item a {
        color: rgba(255, 255, 255, 0.8);
    }

    .breadcrumb-item.active {
        color: white;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        color: rgba(255, 255, 255, 0.6);
    }

    .section-title h2 {
        font-weight: 600;
        color: #333;
        position: relative;
        margin-bottom: 15px;
    }

    .divider {
        width: 60px;
        height: 3px;
        background-color: #198754;
        margin-bottom: 20px;
    }

    .section-padding {
        padding: 70px 0;
    }

    .artwork-card, .literature-card {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        height: 100%;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .artwork-card:hover, .literature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .artwork-image, .literature-image {
        position: relative;
        height: 250px;
        overflow: hidden;
    }

    .artwork-image img, .literature-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }

    .artwork-card:hover .artwork-image img {
        transform: scale(1.1);
    }

    .artwork-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(25, 135, 84, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .artwork-card:hover .artwork-overlay {
        opacity: 1;
    }

    .artwork-link {
        color: white;
        font-size: 24px;
    }

    .literature-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        color: #6c757d;
    }

    .artist, .author, .year {
        font-size: 0.9rem;
        color: #6c757d;
    }
</style>
@endpush
