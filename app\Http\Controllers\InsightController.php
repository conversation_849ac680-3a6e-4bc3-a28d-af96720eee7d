<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Insight;
use App\Models\Director;
use App\Models\News;

class InsightController extends Controller
{
    /**
     * Display the specified insight.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $insight = Insight::where('is_active', true)
            ->findOrFail($id);

        // Get the director
        $director = Director::active()->first();

        // Get other insights
        $otherInsights = Insight::where('is_active', true)
            ->where('id', '!=', $insight->id)
            ->orderBy('published_at', 'desc')
            ->take(3)
            ->get();

        // Get latest news
        $latestNews = News::published()
            ->orderBy('published_at', 'desc')
            ->take(3)
            ->get();

        return view('public.insights.show', compact(
            'insight',
            'director',
            'otherInsights',
            'latestNews'
        ));
    }
}
