<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\View;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register middleware
        $router = $this->app['router'];
        $router->pushMiddlewareToGroup('web', \App\Http\Middleware\SetLocale::class);

        // Register image helper directive
        Blade::directive('image', function ($expression) {
            return "<?php echo \App\Helpers\ImageHelper::getImageUrl($expression); ?>";
        });

        // Register mail component namespace
        View::addNamespace('mail', resource_path('views/components/mail'));
    }
}
