<?php if (isset($component)) { $__componentOriginal8e6152ef5dc48984ffbbc7127d1a1a49 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8e6152ef5dc48984ffbbc7127d1a1a49 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => $__env->getContainer()->make(Illuminate\View\Factory::class)->make('mail::html-layout'),'data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('mail::html-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo e($header ?? ''); ?>


<?php echo e($slot); ?>


<?php if(isset($subcopy)): ?>
<?php echo e($subcopy); ?>

<?php endif; ?>

<?php echo e($footer ?? ''); ?>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8e6152ef5dc48984ffbbc7127d1a1a49)): ?>
<?php $attributes = $__attributesOriginal8e6152ef5dc48984ffbbc7127d1a1a49; ?>
<?php unset($__attributesOriginal8e6152ef5dc48984ffbbc7127d1a1a49); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8e6152ef5dc48984ffbbc7127d1a1a49)): ?>
<?php $component = $__componentOriginal8e6152ef5dc48984ffbbc7127d1a1a49; ?>
<?php unset($__componentOriginal8e6152ef5dc48984ffbbc7127d1a1a49); ?>
<?php endif; ?>
<?php /**PATH D:\IT\nurul-hayah-4\resources\views/components/mail/layout.blade.php ENDPATH**/ ?>