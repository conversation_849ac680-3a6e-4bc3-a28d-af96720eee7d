<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use App\Models\Program;

class FixProgramsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all existing programs
        $programs = Program::all();
        
        // Update each program with default values for the new columns
        foreach ($programs as $index => $program) {
            $program->update([
                'is_featured' => $index < 3, // Make the first 3 programs featured
                'order' => $index + 1, // Set order based on index
                'icon' => 'fas fa-book', // Default icon
            ]);
        }
        
        // If there are no programs, create some sample ones
        if ($programs->isEmpty()) {
            $samplePrograms = [
                [
                    'name' => 'Program Reguler',
                    'name_en' => 'Regular Program',
                    'description' => 'Program reguler untuk siswa.',
                    'description_en' => 'Regular program for students.',
                    'image' => null,
                    'icon' => 'fas fa-book',
                    'is_featured' => true,
                    'order' => 1,
                    'is_active' => true,
                ],
                [
                    'name' => 'Program Unggulan',
                    'name_en' => 'Featured Program',
                    'description' => 'Program unggulan untuk siswa berprestasi.',
                    'description_en' => 'Featured program for high-achieving students.',
                    'image' => null,
                    'icon' => 'fas fa-star',
                    'is_featured' => true,
                    'order' => 2,
                    'is_active' => true,
                ],
                [
                    'name' => 'Program Khusus',
                    'name_en' => 'Special Program',
                    'description' => 'Program khusus untuk siswa dengan kebutuhan tertentu.',
                    'description_en' => 'Special program for students with specific needs.',
                    'image' => null,
                    'icon' => 'fas fa-certificate',
                    'is_featured' => true,
                    'order' => 3,
                    'is_active' => true,
                ],
                [
                    'name' => 'Program Tambahan',
                    'name_en' => 'Additional Program',
                    'description' => 'Program tambahan untuk pengembangan diri.',
                    'description_en' => 'Additional program for self-development.',
                    'image' => null,
                    'icon' => 'fas fa-plus-circle',
                    'is_featured' => false,
                    'order' => 4,
                    'is_active' => true,
                ],
                [
                    'name' => 'Program Ekstrakurikuler',
                    'name_en' => 'Extracurricular Program',
                    'description' => 'Program ekstrakurikuler untuk pengembangan bakat.',
                    'description_en' => 'Extracurricular program for talent development.',
                    'image' => null,
                    'icon' => 'fas fa-futbol',
                    'is_featured' => false,
                    'order' => 5,
                    'is_active' => true,
                ],
            ];
            
            foreach ($samplePrograms as $program) {
                Program::create($program);
            }
            
            $this->command->info('Sample programs created successfully.');
        } else {
            $this->command->info('Existing programs updated successfully.');
        }
    }
}
