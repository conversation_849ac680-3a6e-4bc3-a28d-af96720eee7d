@extends('admin.layouts.app')

@section('title', 'View Swiper Image')

@section('content')
<div class="container-fluid px-4">
    <h1 class="mt-4">View Swiper Image</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="{{ route('admin.swipers.index') }}">Swiper Images</a></li>
        <li class="breadcrumb-item active">View</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-eye me-1"></i> Swiper Image Details
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-12 text-center">
                    <img src="{{ asset('storage/' . $swiper->image) }}" alt="{{ $swiper->title }}" class="img-fluid rounded" style="max-height: 400px;">
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <h5>Indonesian Content</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 150px;">Title</th>
                            <td>{{ $swiper->title }}</td>
                        </tr>
                        <tr>
                            <th>Description</th>
                            <td>{{ $swiper->description }}</td>
                        </tr>
                        <tr>
                            <th>Button Text</th>
                            <td>{{ $swiper->button_text }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h5>English Content</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 150px;">Title</th>
                            <td>{{ $swiper->title_en }}</td>
                        </tr>
                        <tr>
                            <th>Description</th>
                            <td>{{ $swiper->description_en }}</td>
                        </tr>
                        <tr>
                            <th>Button Text</th>
                            <td>{{ $swiper->button_text_en }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <h5>General Information</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 150px;">Button URL</th>
                            <td>{{ $swiper->button_url }}</td>
                        </tr>
                        <tr>
                            <th>Display Order</th>
                            <td>{{ $swiper->order }}</td>
                        </tr>
                        <!-- Status is always active -->
                        <tr>
                            <th>Created At</th>
                            <td>{{ $swiper->created_at->format('F d, Y H:i:s') }}</td>
                        </tr>
                        <tr>
                            <th>Last Updated</th>
                            <td>{{ $swiper->updated_at->format('F d, Y H:i:s') }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="mt-4">
                <a href="{{ route('admin.swipers.edit', $swiper->id) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-1"></i> Edit
                </a>
                <a href="{{ route('admin.swipers.index') }}" class="btn btn-secondary ms-2">
                    <i class="fas fa-arrow-left me-1"></i> Back to List
                </a>
                <form action="{{ route('admin.swipers.destroy', $swiper->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this swiper image?');">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger ms-2">
                        <i class="fas fa-trash me-1"></i> Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
