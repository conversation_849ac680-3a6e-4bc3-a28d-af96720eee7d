<?php

namespace App\Console\Commands;

use App\Models\Setting;
use App\Services\TelegramBotService;
use Illuminate\Console\Command;

class TelegramSetWebhook extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:set-webhook {url?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set the webhook URL for the Telegram bot';

    /**
     * The Telegram Bot service instance.
     *
     * @var \App\Services\TelegramBotService
     */
    protected $telegramService;

    /**
     * Create a new command instance.
     *
     * @param  \App\Services\TelegramBotService  $telegramService
     * @return void
     */
    public function __construct(TelegramBotService $telegramService)
    {
        parent::__construct();
        $this->telegramService = $telegramService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Get the webhook URL from the argument or use nurulhayah.com
        $url = $this->argument('url') ?? 'https://nurulhayah.com/webhook/telegram';

        // If URL is empty for some reason, use nurulhayah.com
        if (empty($url)) {
            $url = 'https://nurulhayah.com/webhook/telegram';
        }

        $this->info("Setting webhook URL to: {$url}");

        // Set the webhook
        $result = $this->telegramService->setWebhook($url);

        if ($result && isset($result['ok']) && $result['ok']) {
            $this->info('Webhook set successfully!');

            // Update the webhook URL in the settings if it's different
            if ($url !== Setting::getValue('telegram_webhook_url')) {
                Setting::setValue('telegram_webhook_url', $url, 'telegram');
                $this->info('Webhook URL updated in settings.');
            }

            return 0;
        } else {
            $this->error('Failed to set webhook.');
            if ($result && isset($result['description'])) {
                $this->error('Error: ' . $result['description']);
            }
            return 1;
        }
    }
}
