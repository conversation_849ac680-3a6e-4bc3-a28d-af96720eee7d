<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Paint;
use App\Models\LitType;
use Illuminate\Http\Request;

class PaintController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $paints = Paint::latest()->paginate(10);
        return view('admin.artwork.paints.index', compact('paints'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.artwork.paints.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'artist' => 'nullable|string|max:255',
            'artist_en' => 'nullable|string|max:255',
            'year' => 'nullable|integer',
        ]);

        $data = $request->all();

        // Set is_active to true by default
        $data['is_active'] = true;

        // Handle image upload
        if ($request->hasFile('image')) {
            // Create a temporary instance for handling the upload
            $tempPaint = new Paint();
            $data['image'] = $tempPaint->handleImageUpload($request->file('image'));
        }

        Paint::create($data);

        return redirect()->route('admin.artwork.paints.index')
            ->with('success', 'Paint created successfully.');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $paint = Paint::findOrFail($id);
        return view('admin.artwork.paints.edit', compact('paint'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $paint = Paint::findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'artist' => 'nullable|string|max:255',
            'artist_en' => 'nullable|string|max:255',
            'year' => 'nullable|integer',
            'medium' => 'nullable|string|max:255',
            'medium_en' => 'nullable|string|max:255',
            'dimensions' => 'nullable|string|max:255',
        ]);

        $data = $request->all();

        // Keep is_active status unchanged
        $data['is_active'] = $paint->is_active;

        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image'] = $paint->handleImageUpload($request->file('image'));
        }

        $paint->update($data);

        return redirect()->route('admin.artwork.paints.index')
            ->with('success', 'Paint updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $paint = Paint::findOrFail($id);

        // Delete the image file
        $paint->deleteImage();

        // Delete the record
        $paint->delete();

        return redirect()->route('admin.artwork.paints.index')
            ->with('success', 'Paint deleted successfully.');
    }
}
