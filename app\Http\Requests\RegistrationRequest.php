<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\RegistrationSchedule;

class RegistrationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Check if registration is open
        return RegistrationSchedule::isRegistrationOpen();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Personal Information
            'nik' => 'required|string|size:16|regex:/^[0-9]+$/',
            'full_name' => 'required|string|max:255',
            'gender' => 'required|in:male,female',
            'place_of_birth' => 'required|string|max:255',
            'date_of_birth' => 'required|date|before_or_equal:today',

            // Parent Information
            'father_name' => 'required|string|max:255',
            'father_occupation' => 'required|string|max:255',
            'mother_name' => 'required|string|max:255',
            'mother_occupation' => 'required|string|max:255',
            'parent_name' => 'nullable|string|max:255',
            'parent_occupation' => 'nullable|string|max:255',

            // Contact Information
            'address' => 'required|string',
            'phone' => 'required|string',
            'phone_full' => 'required|string',
            'email' => 'nullable|email|max:255',

            // Educational Background
            'nisn' => 'nullable|string|max:20|regex:/^[0-9]+$/',
            'last_education' => 'required|string|max:255',
            'previous_school' => 'required|string|max:255',
            'graduation_year' => 'required|numeric|digits:4',

            // Interests and Goals
            'education_unit_id' => 'required|exists:education_units,id',
            'reason' => 'required|string',
            'hobby' => 'required|string|max:255',
            'ambition' => 'required|string|max:255',

            // Additional Information
            'notes' => 'nullable|string',

            // Terms and Conditions
            'terms_accepted' => 'required|accepted',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        $isIndonesian = app()->getLocale() == 'id';

        return [
            'nik.required' => $isIndonesian ? 'NIK wajib diisi.' : 'National ID Number is required.',
            'nik.size' => $isIndonesian ? 'NIK harus terdiri dari 16 digit.' : 'National ID Number must be 16 digits.',
            'nik.regex' => $isIndonesian ? 'NIK hanya boleh berisi angka.' : 'National ID Number must contain only numbers.',

            'full_name.required' => $isIndonesian ? 'Nama lengkap wajib diisi.' : 'Full name is required.',

            'father_name.required' => $isIndonesian ? 'Nama ayah wajib diisi.' : 'Father\'s name is required.',
            'father_occupation.required' => $isIndonesian ? 'Pekerjaan ayah wajib diisi.' : 'Father\'s occupation is required.',
            'mother_name.required' => $isIndonesian ? 'Nama ibu wajib diisi.' : 'Mother\'s name is required.',
            'mother_occupation.required' => $isIndonesian ? 'Pekerjaan ibu wajib diisi.' : 'Mother\'s occupation is required.',

            'phone.required' => $isIndonesian ? 'Nomor telepon wajib diisi.' : 'Phone number is required.',
            'phone_full.required' => $isIndonesian ? 'Nomor telepon lengkap wajib diisi.' : 'Complete phone number is required.',

            'education_unit_id.required' => $isIndonesian ? 'Jenjang pendidikan wajib dipilih.' : 'Education level must be selected.',
            'education_unit_id.exists' => $isIndonesian ? 'Jenjang pendidikan tidak valid.' : 'Invalid education level.',

            'terms_accepted.required' => $isIndonesian ? 'Anda harus menyetujui syarat dan ketentuan.' : 'You must accept the terms and conditions.',
            'terms_accepted.accepted' => $isIndonesian ? 'Anda harus menyetujui syarat dan ketentuan.' : 'You must accept the terms and conditions.',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Additional custom validation if needed
        });
    }

    /**
     * Prepare the data for validation.
     * This ensures that the case formatting from the frontend is preserved.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // No modifications needed - we want to preserve the original case formatting
        // from the frontend JavaScript transformations
    }

    /**
     * Handle a failed authorization attempt.
     *
     * @return \Illuminate\Http\Response
     */
    protected function failedAuthorization()
    {
        $message = app()->getLocale() == 'id'
            ? 'Pendaftaran saat ini ditutup. Silakan cek jadwal pendaftaran.'
            : 'Registration is currently closed. Please check the registration schedule.';

        abort(redirect()->route('registration')->with('error', $message));
    }
}
