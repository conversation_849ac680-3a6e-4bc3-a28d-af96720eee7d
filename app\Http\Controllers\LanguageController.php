<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class LanguageController extends Controller
{
    /**
     * Change the application language.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $locale
     * @return \Illuminate\Http\RedirectResponse
     */
    public function change(Request $request, $locale)
    {
        // Check if the locale is valid
        if (!in_array($locale, ['id', 'en'])) {
            $locale = 'id'; // Default to Indonesian
        }

        // Store the locale in the session
        session(['locale' => $locale]);

        // Redirect back to the previous page
        return redirect()->back();
    }
}
