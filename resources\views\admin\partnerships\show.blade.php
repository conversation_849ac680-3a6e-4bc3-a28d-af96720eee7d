@extends('admin.layouts.app')

@section('title', 'Partnership Details')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Partnership Details</h1>
        <div>
            <a href="{{ route('admin.partnerships.edit', $partnership) }}" class="btn btn-primary me-2">
                <i class="fas fa-edit me-1"></i> Edit
            </a>
            <a href="{{ route('admin.partnerships.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Partnership Information</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Partner Name (Indonesian)</h6>
                            <p>{{ $partnership->name }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Partner Name (English)</h6>
                            <p>{{ $partnership->name_en }}</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Website</h6>
                            <p>
                                @if($partnership->website)
                                    <a href="{{ $partnership->website }}" target="_blank">{{ $partnership->website }}</a>
                                @else
                                    -
                                @endif
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Partnership Since</h6>
                            <p>{{ $partnership->partnership_since ? $partnership->partnership_since->format('d F Y') : '-' }}</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Country</h6>
                            <p>
                                @if($partnership->country_code)
                                    <img src="https://flagcdn.com/32x24/{{ strtolower($partnership->country_code) }}.png" class="me-2" alt="{{ $partnership->country_name }}" width="32" height="24" style="vertical-align: middle;">
                                    {{ $partnership->country_name }}
                                @else
                                    -
                                @endif
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Status</h6>
                            <p>
                                @if($partnership->is_active)
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-danger">Inactive</span>
                                @endif
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Description (Indonesian)</h6>
                            <div class="formatted-content">
                                {!! $partnership->description ?: '-' !!}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Description (English)</h6>
                            <div class="formatted-content">
                                {!! $partnership->description_en ?: '-' !!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Partner Logo</h5>
                </div>
                <div class="card-body text-center">
                    @if($partnership->logo)
                        <img src="{{ asset('storage/' . $partnership->logo) }}" alt="{{ $partnership->name }}" class="img-fluid rounded">
                    @else
                        <div class="bg-light p-5 rounded">
                            <i class="fas fa-image fa-3x text-muted"></i>
                            <p class="mt-3 text-muted">No logo available</p>
                        </div>
                    @endif
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Metadata</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="fw-bold">ID</h6>
                        <p>{{ $partnership->id }}</p>
                    </div>
                    <div class="mb-3">
                        <h6 class="fw-bold">Created At</h6>
                        <p>{{ $partnership->created_at->format('d F Y, H:i') }}</p>
                    </div>
                    <div>
                        <h6 class="fw-bold">Last Updated</h6>
                        <p>{{ $partnership->updated_at->format('d F Y, H:i') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    /* Formatted content styling */
    .formatted-content {
        line-height: 1.6;
    }

    .formatted-content img {
        max-width: 100%;
        height: auto;
        margin: 0.5rem 0;
    }

    .formatted-content ul,
    .formatted-content ol {
        margin-bottom: 1rem;
        padding-left: 1.5rem;
    }

    .formatted-content table {
        width: 100%;
        margin-bottom: 1rem;
        border-collapse: collapse;
    }

    .formatted-content table td,
    .formatted-content table th {
        padding: 0.5rem;
        border: 1px solid #dee2e6;
    }

    .formatted-content blockquote {
        padding: 0.5rem 1rem;
        margin: 1rem 0;
        border-left: 4px solid #6c757d;
        background-color: #f8f9fa;
    }
</style>
@endpush
