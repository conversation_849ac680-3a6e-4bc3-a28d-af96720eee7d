<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RecordLastLoginTime
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::check()) {
            // Only update the timestamp if the user is logging in
            if ($request->route()->getName() === 'login' && $request->isMethod('post')) {
                // We'll update the timestamp after the request is handled
                $request->session()->put('update_login_timestamp', true);
            }
        }

        $response = $next($request);

        // Check if we need to update the login timestamp
        if ($request->session()->has('update_login_timestamp')) {
            $user = Auth::user();
            $user->last_login_at = now();
            $user->save();
            
            $request->session()->forget('update_login_timestamp');
        }

        return $response;
    }
}
