<?php

namespace App\Http\Controllers;

use App\Models\Paint;
use App\Models\Literature;
use App\Models\LitType;
use Illuminate\Http\Request;

class ArtworkController extends Controller
{
    /**
     * Display a listing of all artwork (paints and literature).
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get active paints sorted by view count (most viewed first)
        $paints = Paint::active()->orderBy('view_count', 'desc')->take(6)->get();

        // Get active literature sorted by view count (most viewed first)
        $literature = Literature::active()->orderBy('view_count', 'desc')->take(6)->get();

        // Get all types
        $types = LitType::where('is_active', true)->get();

        return view('public.artwork.index', compact('paints', 'literature', 'types'));
    }

    /**
     * Display a listing of paints.
     *
     * @return \Illuminate\View\View
     */
    public function paints()
    {
        // Get active paints with pagination
        $paints = Paint::active()->latest()->paginate(12);

        // Get all types
        $types = LitType::where('is_active', true)->get();

        return view('public.artwork.paints.index', compact('paints', 'types'));
    }

    /**
     * Display a listing of paints by type.
     *
     * @param  string  $typeSlug
     * @return \Illuminate\View\View
     */
    public function paintsByType($typeSlug)
    {
        // Find the type
        $type = LitType::where('slug', $typeSlug)->where('is_active', true)->firstOrFail();

        // Get active paints with pagination (no longer filtering by category_id)
        $paints = Paint::active()
            ->latest()
            ->paginate(12);

        // Get all types
        $types = LitType::where('is_active', true)->get();

        return view('public.artwork.paints.index', compact('paints', 'types', 'type'));
    }

    /**
     * Display the specified paint.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function showPaint($id)
    {
        // Find the paint
        $paint = Paint::active()->findOrFail($id);

        // Increment view count
        $paint->increment('view_count');

        // Get related paints (just get some recent ones since we no longer have category)
        $relatedPaints = Paint::active()
            ->where('id', '!=', $paint->id)
            ->latest()
            ->take(3)
            ->get();

        return view('public.artwork.paints.show', compact('paint', 'relatedPaints'));
    }

    /**
     * Display a listing of literature.
     *
     * @return \Illuminate\View\View
     */
    public function literature()
    {
        // Get active literature with pagination
        $literature = Literature::active()->latest()->paginate(12);

        // Get all types
        $types = LitType::where('is_active', true)->get();

        return view('public.artwork.literature.index', compact('literature', 'types'));
    }

    /**
     * Display a listing of literature by type.
     *
     * @param  string  $typeSlug
     * @return \Illuminate\View\View
     */
    public function literatureByType($typeSlug)
    {
        // Find the type
        $type = LitType::where('slug', $typeSlug)->where('is_active', true)->firstOrFail();

        // Get active literature with pagination
        $literature = Literature::active()
            ->where('type', $type->slug)
            ->latest()
            ->paginate(12);

        // Get all types
        $types = LitType::where('is_active', true)->get();

        return view('public.artwork.literature.index', compact('literature', 'types', 'type'));
    }

    /**
     * Display the specified literature.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function showLiterature($id)
    {
        // Find the literature
        $literature = Literature::active()->findOrFail($id);

        // Increment view count
        $literature->increment('view_count');

        // Get related literature (same type)
        $relatedLiterature = Literature::active()
            ->where('id', '!=', $literature->id)
            ->where('type', $literature->type)
            ->latest()
            ->take(3)
            ->get();

        return view('public.artwork.literature.show', compact('literature', 'relatedLiterature'));
    }
}
