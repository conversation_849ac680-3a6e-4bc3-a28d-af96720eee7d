@extends('admin.layouts.app')

@section('title', 'Curriculum')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Curriculum</h1>
        <a href="{{ route('admin.curricula.create') }}" class="btn btn-success">
            <i class="fas fa-plus"></i> Add New
        </a>
    </div>

    <div class="card">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {{ $activeTab == 'formal' ? 'active' : '' }}" href="{{ route('admin.curricula.index', ['tab' => 'formal']) }}">
                        Formal
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ $activeTab == 'non-formal' ? 'active' : '' }}" href="{{ route('admin.curricula.index', ['tab' => 'non-formal']) }}">
                        Non-Formal
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Title</th>
                            @if($activeTab == 'formal')
                                <th>Education Unit</th>
                            @endif
                            <th>Order</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($curricula as $curriculum)
                            <tr>
                                <td>{{ $curriculum->title }}</td>
                                @if($activeTab == 'formal')
                                    <td>{{ $curriculum->educationUnit->name ?? 'N/A' }}</td>
                                @endif
                                <td>{{ $curriculum->order }}</td>
                                <td>
                                    @if($curriculum->is_active)
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <span class="badge bg-secondary">Inactive</span>
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('admin.curricula.show', $curriculum) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.curricula.edit', $curriculum) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.curricula.destroy', $curriculum) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger btn-delete" onclick="return confirm('Are you sure you want to delete this item?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="{{ $activeTab == 'formal' ? 6 : 5 }}" class="text-center">No curriculum items found.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection
