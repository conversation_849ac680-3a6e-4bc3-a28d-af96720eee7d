<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('registrations', function (Blueprint $table) {
            // Check if columns don't exist before adding them
            if (!Schema::hasColumn('registrations', 'parent_name')) {
                $table->string('parent_name')->nullable()->after('mother_occupation');
            }
            if (!Schema::hasColumn('registrations', 'parent_occupation')) {
                $table->string('parent_occupation')->nullable()->after('parent_name');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('registrations', function (Blueprint $table) {
            // Drop the columns if they exist
            if (Schema::hasColumn('registrations', 'parent_occupation')) {
                $table->dropColumn('parent_occupation');
            }
            if (Schema::hasColumn('registrations', 'parent_name')) {
                $table->dropColumn('parent_name');
            }
        });
    }
};
