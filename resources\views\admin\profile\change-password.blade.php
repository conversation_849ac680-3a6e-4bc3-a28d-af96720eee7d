@extends('admin.layouts.app')

@section('title', 'Ganti Password')

@section('content')
    <div class="container-fluid px-4">
        <h1 class="mt-4">Ganti Password</h1>
        <ol class="breadcrumb mb-4">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('admin.profile.edit') }}">Profil</a></li>
            <li class="breadcrumb-item active">Ganti Password</li>
        </ol>

        <div class="row">
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-key me-1"></i>
                        Ganti Password
                    </div>
                    <div class="card-body">
                        <form action="{{ route('admin.profile.change-password') }}" method="POST">
                            @csrf
                            @method('PUT')

                            <div class="mb-3">
                                <label for="current_password" class="form-label">Password Saat Ini</label>
                                <input type="password" class="form-control @error('current_password') is-invalid @enderror" id="current_password" name="current_password" required>
                                @error('current_password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">Password Baru</label>
                                <input type="password" class="form-control @error('password') is-invalid @enderror" id="password" name="password" required>
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="password_confirmation" class="form-label">Konfirmasi Password Baru</label>
                                <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                            </div>

                            <button type="submit" class="btn btn-primary">Ganti Password</button>
                            <a href="{{ route('admin.profile.edit') }}" class="btn btn-outline-secondary ms-2">Kembali ke Profil</a>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-shield-alt me-1"></i>
                        Panduan Password
                    </div>
                    <div class="card-body">
                        <p>Untuk password yang kuat, pertimbangkan:</p>
                        <ul>
                            <li>Minimal 8 karakter</li>
                            <li>Gunakan huruf besar dan kecil</li>
                            <li>Sertakan angka</li>
                            <li>Sertakan karakter khusus</li>
                        </ul>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Jangan pernah membagikan password Anda kepada orang lain.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
