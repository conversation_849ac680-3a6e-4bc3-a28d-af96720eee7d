@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? 'Program' : 'Programs')

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? 'Program Unggulan' : 'Featured Programs' }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? 'Program' : 'Programs' }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Programs Introduction -->
    <section class="section-padding bg-light">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 order-lg-2 mb-4 mb-lg-0" data-aos="fade-left">
                    <img src="{{ asset('images/programs.jpg') }}" alt="Programs" class="img-fluid rounded shadow-sm">
                </div>
                <div class="col-lg-6 order-lg-1" data-aos="fade-right">
                    <h2 class="text-success mb-4">{{ app()->getLocale() == 'id' ? 'Program Pendidikan Berkualitas' : 'Quality Educational Programs' }}</h2>
                    <p>{{ app()->getLocale() == 'id' ? 'Pondok Pesantren Nurul Hayah 4 menawarkan berbagai program pendidikan yang dirancang untuk mengembangkan potensi santri secara komprehensif, baik dalam bidang akademik, keagamaan, maupun keterampilan hidup.' : 'Nurul Hayah 4 Islamic Boarding School offers various educational programs designed to develop students\' potential comprehensively, both in academic, religious, and life skills.' }}</p>
                    <p>{{ app()->getLocale() == 'id' ? 'Program-program kami mengintegrasikan kurikulum nasional dengan pendidikan Islam yang mendalam, serta dilengkapi dengan berbagai kegiatan ekstrakurikuler untuk mengembangkan bakat dan minat santri.' : 'Our programs integrate the national curriculum with in-depth Islamic education, and are complemented by various extracurricular activities to develop students\' talents and interests.' }}</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Programs -->
    <section class="section-padding">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>{{ app()->getLocale() == 'id' ? 'Program Unggulan' : 'Featured Programs' }}</h2>
                <p>{{ app()->getLocale() == 'id' ? 'Program-program unggulan yang kami tawarkan' : 'Featured programs that we offer' }}</p>
            </div>

            <div class="featured-program-carousel">
                @if($programs->where('is_featured', true)->isNotEmpty())
                    <div class="owl-carousel owl-theme">
                        @foreach($programs->where('is_featured', true) as $program)
                            <div class="item">
                                <div class="card h-100 border-0 shadow-sm">
                                    @if($program->image)
                                        <img src="{{ asset('storage/' . $program->image) }}" class="card-img-top" alt="{{ app()->getLocale() == 'id' ? $program->name : $program->name_en }}">
                                    @else
                                        <img src="{{ asset('images/program-placeholder.jpg') }}" class="card-img-top" alt="{{ app()->getLocale() == 'id' ? $program->name : $program->name_en }}">
                                    @endif
                                    <div class="card-body">
                                        <div class="program-icon mb-3 text-success">
                                            @if($program->icon)
                                                <i class="{{ $program->icon }} fa-2x"></i>
                                            @else
                                                <i class="fas fa-graduation-cap fa-2x"></i>
                                            @endif
                                        </div>
                                        <h4 class="card-title">{{ app()->getLocale() == 'id' ? $program->name : $program->name_en }}</h4>
                                        <p class="card-text">{!! app()->getLocale() == 'id' ? $program->description : $program->description_en !!}</p>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="row">
                        <div class="col-12 text-center">
                            <div class="alert alert-info">
                                {{ app()->getLocale() == 'id' ? 'Belum ada program unggulan yang tersedia.' : 'No featured programs available yet.' }}
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </section>

    <!-- Regular Programs -->
    <section class="section-padding bg-light">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>{{ app()->getLocale() == 'id' ? 'Program Reguler' : 'Regular Programs' }}</h2>
                <p>{{ app()->getLocale() == 'id' ? 'Program-program reguler yang kami tawarkan' : 'Regular programs that we offer' }}</p>
            </div>

            <div class="row">
                @forelse($programs->where('is_featured', false) as $program)
                    <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="{{ $loop->iteration % 3 * 100 }}">
                        <div class="card h-100 border-0 shadow-sm">
                            @if($program->image)
                                <img src="{{ asset('storage/' . $program->image) }}" class="card-img-top" alt="{{ app()->getLocale() == 'id' ? $program->name : $program->name_en }}">
                            @else
                                <img src="{{ asset('images/program-placeholder.jpg') }}" class="card-img-top" alt="{{ app()->getLocale() == 'id' ? $program->name : $program->name_en }}">
                            @endif
                            <div class="card-body">
                                <div class="program-icon mb-3 text-success">
                                    @if($program->icon)
                                        <i class="{{ $program->icon }} fa-2x"></i>
                                    @else
                                        <i class="fas fa-book fa-2x"></i>
                                    @endif
                                </div>
                                <h4 class="card-title">{{ app()->getLocale() == 'id' ? $program->name : $program->name_en }}</h4>
                                <p class="card-text">{!! app()->getLocale() == 'id' ? $program->description : $program->description_en !!}</p>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12 text-center">
                        <div class="alert alert-info">
                            {{ app()->getLocale() == 'id' ? 'Belum ada program reguler yang tersedia.' : 'No regular programs available yet.' }}
                        </div>
                    </div>
                @endforelse
            </div>
        </div>
    </section>

    <!-- Extracurricular Activities -->
    <section class="section-padding">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>{{ app()->getLocale() == 'id' ? 'Kegiatan Ekstrakurikuler' : 'Extracurricular Activities' }}</h2>
                <p>{{ app()->getLocale() == 'id' ? 'Berbagai kegiatan ekstrakurikuler untuk mengembangkan bakat dan minat santri' : 'Various extracurricular activities to develop students\' talents and interests' }}</p>
            </div>

            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up">
                    <div class="card h-100 border-0 shadow-sm text-center">
                        <div class="card-body">
                            <div class="extracurricular-icon mb-3 text-success">
                                <i class="fas fa-futbol fa-3x"></i>
                            </div>
                            <h4 class="card-title">{{ app()->getLocale() == 'id' ? 'Olahraga' : 'Sports' }}</h4>
                            <p class="card-text">{{ app()->getLocale() == 'id' ? 'Sepak bola, bola voli, bulu tangkis, tenis meja, dan lain-lain.' : 'Football, volleyball, badminton, table tennis, and others.' }}</p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="card h-100 border-0 shadow-sm text-center">
                        <div class="card-body">
                            <div class="extracurricular-icon mb-3 text-success">
                                <i class="fas fa-paint-brush fa-3x"></i>
                            </div>
                            <h4 class="card-title">{{ app()->getLocale() == 'id' ? 'Seni' : 'Arts' }}</h4>
                            <p class="card-text">{{ app()->getLocale() == 'id' ? 'Kaligrafi, lukis, musik, tari, dan drama.' : 'Calligraphy, painting, music, dance, and drama.' }}</p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="card h-100 border-0 shadow-sm text-center">
                        <div class="card-body">
                            <div class="extracurricular-icon mb-3 text-success">
                                <i class="fas fa-language fa-3x"></i>
                            </div>
                            <h4 class="card-title">{{ app()->getLocale() == 'id' ? 'Bahasa' : 'Languages' }}</h4>
                            <p class="card-text">{{ app()->getLocale() == 'id' ? 'Klub bahasa Arab, bahasa Inggris, dan bahasa asing lainnya.' : 'Arabic, English, and other foreign language clubs.' }}</p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="300">
                    <div class="card h-100 border-0 shadow-sm text-center">
                        <div class="card-body">
                            <div class="extracurricular-icon mb-3 text-success">
                                <i class="fas fa-laptop-code fa-3x"></i>
                            </div>
                            <h4 class="card-title">{{ app()->getLocale() == 'id' ? 'Teknologi' : 'Technology' }}</h4>
                            <p class="card-text">{{ app()->getLocale() == 'id' ? 'Komputer, robotik, dan pemrograman.' : 'Computer, robotics, and programming.' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="section-padding bg-success text-white">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8" data-aos="fade-right">
                    <h2 class="mb-3">{{ app()->getLocale() == 'id' ? 'Siap Bergabung dengan Program Kami?' : 'Ready to Join Our Programs?' }}</h2>
                    <p class="mb-4">{{ app()->getLocale() == 'id' ? 'Daftarkan diri Anda sekarang dan kembangkan potensi Anda bersama kami.' : 'Register yourself now and develop your potential with us.' }}</p>
                </div>
                <div class="col-lg-4 text-lg-end" data-aos="fade-left">
                    <a href="{{ route('registration') }}" class="btn btn-light btn-lg">{{ app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now' }}</a>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
<script>
    $(document).ready(function(){
        // Initialize the featured program carousel
        $('.featured-program-carousel .owl-carousel').owlCarousel({
            loop: true,
            margin: 20,
            nav: false,
            dots: true,
            autoplay: true,
            autoplayTimeout: 4000,
            autoplayHoverPause: true,
            responsive: {
                0: {
                    items: 1,
                    margin: 15
                },
                576: {
                    items: 2,
                    margin: 15
                },
                768: {
                    items: 2,
                    margin: 20
                },
                992: {
                    items: 3,
                    margin: 20
                }
            }
        });
    });
</script>
@endpush
