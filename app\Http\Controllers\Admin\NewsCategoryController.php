<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\NewsCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class NewsCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $categories = NewsCategory::orderBy('id')->paginate(10);
        return view('admin.news_categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.news_categories.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:news_categories',
            'slug_en' => 'required|string|max:255|unique:news_categories',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
        ]);

        $data = $request->all();
        $data['is_active'] = true;

        NewsCategory::create($data);

        return redirect()->route('admin.categories.index')
            ->with('success', 'News category created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\NewsCategory  $category
     * @return \Illuminate\View\View
     */
    public function show(NewsCategory $category)
    {
        return view('admin.news_categories.show', compact('category'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\NewsCategory  $category
     * @return \Illuminate\View\View
     */
    public function edit(NewsCategory $category)
    {
        return view('admin.news_categories.edit', compact('category'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\NewsCategory  $category
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, NewsCategory $category)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:news_categories,slug,' . $category->id,
            'slug_en' => 'required|string|max:255|unique:news_categories,slug_en,' . $category->id,
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
        ]);

        $data = $request->all();
        $data['is_active'] = true;

        $category->update($data);

        return redirect()->route('admin.categories.index')
            ->with('success', 'News category updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\NewsCategory  $category
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(NewsCategory $category)
    {
        // Check if the category has news articles
        if ($category->news()->count() > 0) {
            return redirect()->route('admin.categories.index')
                ->with('error', 'Cannot delete category because it has news articles associated with it.');
        }

        $category->delete();

        return redirect()->route('admin.categories.index')
            ->with('success', 'News category deleted successfully.');
    }
}
