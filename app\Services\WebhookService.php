<?php

namespace App\Services;

use App\Models\Webhook;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WebhookService
{
    /**
     * Dispatch webhooks for a specific event.
     *
     * @param  string  $event
     * @param  array  $payload
     * @return array
     */
    public function dispatchEvent($event, $payload = [])
    {
        $webhooks = Webhook::active()->forEvent($event)->get();
        $results = [];

        foreach ($webhooks as $webhook) {
            $results[$webhook->id] = $this->dispatchWebhook($webhook, $payload);
        }

        return $results;
    }

    /**
     * Dispatch a single webhook.
     *
     * @param  \App\Models\Webhook  $webhook
     * @param  array  $payload
     * @return bool
     */
    public function dispatchWebhook(Webhook $webhook, $payload = [])
    {
        try {
            // Prepare the payload using the template if available
            $finalPayload = $this->preparePayload($webhook, $payload);
            
            // Generate signature if secret is set
            $signature = $webhook->generateSignature($finalPayload);
            
            // Prepare headers
            $headers = $webhook->headers ?? [];
            if ($signature) {
                $headers['X-Webhook-Signature'] = $signature;
            }
            
            // Add content type header if not present
            if (!isset($headers['Content-Type'])) {
                $headers['Content-Type'] = 'application/json';
            }
            
            // Add user agent
            $headers['User-Agent'] = 'Nurul Hayah Webhook Service';
            
            // Log the webhook dispatch attempt
            Log::info('Dispatching webhook', [
                'webhook_id' => $webhook->id,
                'webhook_name' => $webhook->name,
                'event' => $webhook->event,
                'url' => $webhook->url,
            ]);
            
            // Send the request
            $response = Http::withHeaders($headers)
                ->timeout(30)
                ->post($webhook->url, $finalPayload);
            
            // Check if the request was successful
            if ($response->successful()) {
                $webhook->markAsTriggered();
                $webhook->resetRetryCount();
                
                Log::info('Webhook dispatched successfully', [
                    'webhook_id' => $webhook->id,
                    'status_code' => $response->status(),
                ]);
                
                return true;
            } else {
                $errorMessage = "HTTP Error: {$response->status()} - {$response->body()}";
                $webhook->markAsFailed($errorMessage);
                
                Log::error('Webhook dispatch failed', [
                    'webhook_id' => $webhook->id,
                    'status_code' => $response->status(),
                    'response' => $response->body(),
                ]);
                
                return false;
            }
        } catch (\Exception $e) {
            $webhook->markAsFailed($e->getMessage());
            
            Log::error('Exception while dispatching webhook', [
                'webhook_id' => $webhook->id,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return false;
        }
    }

    /**
     * Prepare the payload using the template if available.
     *
     * @param  \App\Models\Webhook  $webhook
     * @param  array  $payload
     * @return array
     */
    protected function preparePayload(Webhook $webhook, $payload)
    {
        // If no template is defined, use the payload as is
        if (empty($webhook->payload_template)) {
            return $payload;
        }

        // Start with the template
        $finalPayload = $webhook->payload_template;

        // Add event information
        $finalPayload['event'] = $webhook->event;
        $finalPayload['triggered_at'] = now()->toIso8601String();
        
        // Add the original payload data
        $finalPayload['data'] = $payload;

        return $finalPayload;
    }

    /**
     * Test a webhook by sending a test payload.
     *
     * @param  \App\Models\Webhook  $webhook
     * @return bool
     */
    public function testWebhook(Webhook $webhook)
    {
        $testPayload = [
            'event' => $webhook->event,
            'test' => true,
            'timestamp' => now()->toIso8601String(),
            'message' => 'This is a test webhook from Nurul Hayah.',
        ];

        return $this->dispatchWebhook($webhook, $testPayload);
    }
}
