<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\RegistrationSchedule;
use App\Models\RegistrationRequirement;
use App\Models\RegistrationFee;
use App\Models\RegistrationInfo;

class RegistrationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create registration schedule
        RegistrationSchedule::create([
            'title' => 'Pendaftaran Tahun Ajaran 2025/2026',
            'title_en' => 'Registration for Academic Year 2025/2026',
            'start_date' => '2025-05-01',
            'end_date' => '2025-07-31',
            'description' => 'Pendaftaran santri baru untuk tahun ajaran 2025/2026',
            'description_en' => 'New student registration for academic year 2025/2026',
            'is_active' => true
        ]);

        // Create registration requirements
        $requirements = [
            [
                'name' => 'Fotokopi Akta Kelahiran',
                'name_en' => 'Copy of Birth Certificate',
                'description' => 'Fotokopi akta kelahiran yang dilegalisir',
                'description_en' => 'Legalized copy of birth certificate',
                'order' => 1,
                'is_active' => true
            ],
            [
                'name' => 'Fotokopi Kartu Keluarga',
                'name_en' => 'Copy of Family Card',
                'description' => 'Fotokopi kartu keluarga yang masih berlaku',
                'description_en' => 'Valid copy of family card',
                'order' => 2,
                'is_active' => true
            ],
            [
                'name' => 'Fotokopi Rapor Terakhir',
                'name_en' => 'Copy of Latest Report Card',
                'description' => 'Fotokopi rapor terakhir yang dilegalisir',
                'description_en' => 'Legalized copy of latest report card',
                'order' => 3,
                'is_active' => true
            ],
            [
                'name' => 'Pas Foto Berwarna 3x4',
                'name_en' => 'Colored Passport Photo 3x4',
                'description' => 'Pas foto berwarna ukuran 3x4 sebanyak 4 lembar',
                'description_en' => 'Colored passport photo size 3x4 (4 pieces)',
                'order' => 4,
                'is_active' => true
            ],
            [
                'name' => 'Surat Keterangan Sehat',
                'name_en' => 'Health Certificate',
                'description' => 'Surat keterangan sehat dari dokter',
                'description_en' => 'Health certificate from doctor',
                'order' => 5,
                'is_active' => true
            ]
        ];

        foreach ($requirements as $requirement) {
            RegistrationRequirement::create($requirement);
        }

        // Create registration fees
        $fees = [
            [
                'name' => 'Biaya Pendaftaran',
                'name_en' => 'Registration Fee',
                'amount' => 500000,
                'description' => 'Biaya pendaftaran santri baru',
                'description_en' => 'New student registration fee',
                'order' => 1,
                'is_active' => true
            ],
            [
                'name' => 'Biaya Seragam',
                'name_en' => 'Uniform Fee',
                'amount' => 750000,
                'description' => 'Biaya seragam (4 stel)',
                'description_en' => 'Uniform fee (4 sets)',
                'order' => 2,
                'is_active' => true
            ],
            [
                'name' => 'Biaya Asrama',
                'name_en' => 'Dormitory Fee',
                'amount' => 1500000,
                'description' => 'Biaya asrama untuk satu semester',
                'description_en' => 'Dormitory fee for one semester',
                'order' => 3,
                'is_active' => true
            ],
            [
                'name' => 'Biaya SPP',
                'name_en' => 'Tuition Fee',
                'amount' => 850000,
                'description' => 'Biaya SPP per bulan',
                'description_en' => 'Monthly tuition fee',
                'order' => 4,
                'is_active' => true
            ]
        ];

        foreach ($fees as $fee) {
            RegistrationFee::create($fee);
        }

        // Create registration info
        RegistrationInfo::create([
            'content' => '<p>Pendaftaran dapat dilakukan secara online melalui website ini atau langsung datang ke kantor kami. Pembayaran dapat dilakukan melalui transfer bank ke rekening berikut:</p><p>Bank BRI<br>No. Rekening: 1234-5678-9012-3456<br>Atas Nama: Yayasan Nurul Hayah 4</p><p>Setelah melakukan pembayaran, harap konfirmasi melalui WhatsApp ke nomor 0812-3456-7890.</p>',
            'content_en' => '<p>Registration can be done online through this website or directly at our office. Payment can be made via bank transfer to the following account:</p><p>BRI Bank<br>Account Number: 1234-5678-9012-3456<br>Account Name: Yayasan Nurul Hayah 4</p><p>After making payment, please confirm via WhatsApp to 0812-3456-7890.</p>',
            'is_active' => true
        ]);
    }
}
