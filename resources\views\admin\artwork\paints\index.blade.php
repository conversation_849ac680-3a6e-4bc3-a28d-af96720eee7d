@extends('admin.layouts.app')

@section('title', 'Paints')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Paints</h1>
        <a href="{{ route('admin.artwork.paints.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Paint
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            @if($paints->isEmpty())
                <div class="alert alert-info">
                    No paints found. <a href="{{ route('admin.artwork.paints.create') }}">Create your first paint</a>.
                </div>
            @else
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Image</th>
                                <th>Title</th>
                                <th>Artist</th>
                                <th>Year</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($paints as $paint)
                                <tr>
                                    <td>
                                        @if($paint->image)
                                            <img src="{{ asset('storage/' . $paint->image) }}" alt="{{ $paint->title }}" class="img-thumbnail" style="max-height: 50px;">
                                        @else
                                            <span class="text-muted">No image</span>
                                        @endif
                                    </td>
                                    <td>{{ $paint->title }}</td>
                                    <td>{{ $paint->artist }}</td>
                                    <td>{{ $paint->year ?: '-' }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.artwork.paints.edit', $paint->id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <form action="{{ route('admin.artwork.paints.destroy', $paint->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this paint?');">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i> Delete
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div class="mt-4">
                    {{ $paints->links() }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
