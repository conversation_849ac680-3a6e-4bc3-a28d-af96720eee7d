@extends('admin.layouts.app')

@section('title', 'View Profile')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View Profile</h1>
        <div>
            <a href="{{ route('admin.profiles.edit', $profile) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="{{ route('admin.profiles.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Profile Details</h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-3 fw-bold">ID:</div>
                <div class="col-md-9">{{ $profile->id }}</div>
            </div>

            <div class="row mb-3">
                <div class="col-md-3 fw-bold">Type:</div>
                <div class="col-md-9">
                    @if($profile->type == 'motto')
                        <span class="badge bg-warning">Motto</span>
                    @elseif($profile->type == 'vision')
                        <span class="badge bg-primary">Vision</span>
                    @elseif($profile->type == 'mission')
                        <span class="badge bg-success">Mission</span>
                    @elseif($profile->type == 'history')
                        <span class="badge bg-info">History</span>
                    @endif
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-3 fw-bold">Created At:</div>
                <div class="col-md-9">{{ $profile->created_at->format('d M Y H:i:s') }}</div>
            </div>

            <div class="row mb-3">
                <div class="col-md-3 fw-bold">Updated At:</div>
                <div class="col-md-9">{{ $profile->updated_at->format('d M Y H:i:s') }}</div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Indonesian Content</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Title:</div>
                        <div class="col-md-9">{{ $profile->title }}</div>
                    </div>

                    <div class="row">
                        <div class="col-md-3 fw-bold">Content:</div>
                        <div class="col-md-9">{!! $profile->content !!}</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">English Content</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Title:</div>
                        <div class="col-md-9">{{ $profile->title_en }}</div>
                    </div>

                    <div class="row">
                        <div class="col-md-3 fw-bold">Content:</div>
                        <div class="col-md-9">{!! $profile->content_en !!}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if($profile->type === 'history' && $profile->image)
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Content Image</h5>
            </div>
            <div class="card-body text-center">
                <img src="{{ asset('storage/' . $profile->image) }}" alt="Content Image" class="img-thumbnail" style="max-height: 300px;">
            </div>
        </div>
    @endif

    @if($profile->type === 'history' && count($timelines) > 0)
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Timeline Entries</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Year</th>
                                <th>Title (ID)</th>
                                <th>Title (EN)</th>
                                <th>Content (ID)</th>
                                <th>Content (EN)</th>
                                <th>Image</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($timelines as $timeline)
                                <tr>
                                    <td>{{ $timeline->year }}</td>
                                    <td>{{ $timeline->title }}</td>
                                    <td>{{ $timeline->title_en }}</td>
                                    <td>{{ Str::limit($timeline->content, 50) }}</td>
                                    <td>{{ Str::limit($timeline->content_en, 50) }}</td>
                                    <td>
                                        @if($timeline->image)
                                            <img src="{{ asset('storage/' . $timeline->image) }}" alt="Timeline Image" class="img-thumbnail" style="max-height: 50px;">
                                        @else
                                            <span class="text-muted">No image</span>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif
@endsection
