<?php

namespace App\Http\Controllers\Api;

use App\Models\News;
use App\Services\WebhookService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class NewsController extends BaseApiController
{
    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\WebhookService  $webhookService
     * @return void
     */
    public function __construct(WebhookService $webhookService)
    {
        parent::__construct($webhookService);
        $this->model = new News();
        $this->modelName = 'news';
        
        $this->storeRules = [
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:news',
            'slug_en' => 'nullable|string|max:255|unique:news',
            'content' => 'required|string',
            'content_en' => 'required|string',
            'category_id' => 'required|exists:news_categories,id',
            'is_published' => 'boolean',
            'published_at' => 'nullable|date',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ];
        
        $this->updateRules = [
            'title' => 'string|max:255',
            'title_en' => 'string|max:255',
            'slug' => 'nullable|string|max:255|unique:news,slug,' . request()->route('id'),
            'slug_en' => 'nullable|string|max:255|unique:news,slug_en,' . request()->route('id'),
            'content' => 'string',
            'content_en' => 'string',
            'category_id' => 'exists:news_categories,id',
            'is_published' => 'boolean',
            'published_at' => 'nullable|date',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ];
    }

    /**
     * Display a listing of the news.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = $this->model->query();

        // Apply filters
        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->has('is_published')) {
            $query->where('is_published', $request->is_published);
        }

        // Apply search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('title_en', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhere('content_en', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortField = $request->input('sort', 'published_at');
        $sortDirection = $request->input('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        // Apply pagination
        $perPage = $request->input('per_page', 15);
        $news = $query->with('category')->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $news,
        ]);
    }

    /**
     * Store a newly created news in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), $this->storeRules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $data = $request->all();

        // Handle image uploads
        if ($request->hasFile('image')) {
            $data['image'] = $this->model->handleImageUpload($request->file('image'), 'image');
        }

        if ($request->hasFile('thumbnail')) {
            $data['thumbnail'] = $this->model->handleImageUpload($request->file('thumbnail'), 'thumbnail');
        }

        // Set user_id to authenticated user
        $data['user_id'] = auth()->id();

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = \Str::slug($data['title']);
        }

        if (empty($data['slug_en'])) {
            $data['slug_en'] = \Str::slug($data['title_en']);
        }

        // Create the news
        $news = $this->model->create($data);

        // Dispatch webhook event
        $this->webhookService->dispatchEvent('news.created', $news->toArray());

        return response()->json([
            'success' => true,
            'message' => 'News created successfully',
            'data' => $news,
        ], 201);
    }

    /**
     * Update the specified news in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $news = $this->model->findOrFail($id);

        $validator = Validator::make($request->all(), $this->updateRules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $data = $request->all();

        // Handle image uploads
        if ($request->hasFile('image')) {
            // Delete old image first
            $news->deleteImage('image');

            // Upload new image
            $data['image'] = $news->handleImageUpload($request->file('image'), 'image');
        }

        if ($request->hasFile('thumbnail')) {
            // Delete old thumbnail first
            $news->deleteImage('thumbnail');

            // Upload new thumbnail
            $data['thumbnail'] = $news->handleImageUpload($request->file('thumbnail'), 'thumbnail');
        }

        // Update the news
        $news->update($data);

        // Dispatch webhook event
        $this->webhookService->dispatchEvent('news.updated', $news->toArray());

        return response()->json([
            'success' => true,
            'message' => 'News updated successfully',
            'data' => $news,
        ]);
    }
}
