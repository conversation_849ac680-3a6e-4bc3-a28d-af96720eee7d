@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? $paint->title : $paint->title_en)
@section('meta_description', app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit(strip_tags($paint->description), 160) : \Illuminate\Support\Str::limit(strip_tags($paint->description_en), 160))

@section('content')
    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="page-title" data-aos="fade-up">{{ app()->getLocale() == 'id' ? $paint->title : $paint->title_en }}</h1>
                    <nav aria-label="breadcrumb" data-aos="fade-up" data-aos-delay="100">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('artwork') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Karya Seni' : 'Artwork' }}</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('artwork.paints') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Lukisan' : 'Paintings' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? $paint->title : $paint->title_en }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Paint Detail -->
    <section class="section-padding">
        <div class="container">
            <div class="row">
                <!-- Paint Image -->
                <div class="col-lg-6 mb-4 mb-lg-0" data-aos="fade-right">
                    <div class="paint-image-container">
                        @if($paint->image)
                            <img src="{{ asset('storage/' . $paint->image) }}" alt="{{ app()->getLocale() == 'id' ? $paint->title : $paint->title_en }}" class="img-fluid rounded shadow">
                            <a href="{{ asset('storage/' . $paint->image) }}" class="image-zoom" title="{{ app()->getLocale() == 'id' ? $paint->title : $paint->title_en }}">
                                <i class="fas fa-search-plus"></i>
                            </a>
                        @else
                            <img src="{{ asset('images/artwork-placeholder.jpg') }}" alt="{{ app()->getLocale() == 'id' ? $paint->title : $paint->title_en }}" class="img-fluid rounded shadow">
                        @endif
                    </div>
                </div>

                <!-- Paint Details -->
                <div class="col-lg-6" data-aos="fade-left">
                    <div class="paint-details bg-white p-4 rounded shadow-sm">
                        <h2 class="mb-3">{{ app()->getLocale() == 'id' ? $paint->title : $paint->title_en }}</h2>

                        <div class="paint-meta mb-4">
                            <div class="row">
                                <div class="col-md-6 mb-2">
                                    <div class="d-flex align-items-center">
                                        <div class="icon-box me-2">
                                            <i class="fas fa-user-alt"></i>
                                        </div>
                                        <div>
                                            <small class="text-muted d-block">{{ app()->getLocale() == 'id' ? 'Seniman' : 'Artist' }}</small>
                                            <span>{{ app()->getLocale() == 'id' ? $paint->artist : $paint->artist_en }}</span>
                                        </div>
                                    </div>
                                </div>

                                @if($paint->year)
                                <div class="col-md-6 mb-2">
                                    <div class="d-flex align-items-center">
                                        <div class="icon-box me-2">
                                            <i class="fas fa-calendar-alt"></i>
                                        </div>
                                        <div>
                                            <small class="text-muted d-block">{{ app()->getLocale() == 'id' ? 'Tahun' : 'Year' }}</small>
                                            <span>{{ $paint->year }}</span>
                                        </div>
                                    </div>
                                </div>
                                @endif


                            </div>
                        </div>

                        <div class="paint-description mb-4">
                            <h5>{{ app()->getLocale() == 'id' ? 'Deskripsi' : 'Description' }}</h5>
                            <div class="content-wrapper">
                                {!! app()->getLocale() == 'id' ? $paint->description : $paint->description_en !!}
                            </div>
                        </div>

                        <div class="paint-actions">
                            <a href="{{ route('artwork.paints') }}" class="btn btn-outline-success">
                                <i class="fas fa-arrow-left me-2"></i>
                                {{ app()->getLocale() == 'id' ? 'Kembali ke Lukisan' : 'Back to Paintings' }}
                            </a>

                            @if($paint->image)
                            <a href="{{ asset('storage/' . $paint->image) }}" class="btn btn-success ms-2 view-fullscreen">
                                <i class="fas fa-expand me-2"></i>
                                {{ app()->getLocale() == 'id' ? 'Lihat Ukuran Penuh' : 'View Full Size' }}
                            </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Paintings -->
    @if($relatedPaints->count() > 0)
    <section class="section-padding bg-light">
        <div class="container">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="section-title text-center" data-aos="fade-up">
                        <h2>{{ app()->getLocale() == 'id' ? 'Lukisan Terkait' : 'Related Paintings' }}</h2>
                        <div class="divider mx-auto"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                @foreach($relatedPaints as $relatedPaint)
                    <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="{{ $loop->iteration * 100 }}">
                        <div class="artwork-card">
                            <div class="artwork-image">
                                @if($relatedPaint->image)
                                    <img src="{{ asset('storage/' . $relatedPaint->image) }}" alt="{{ app()->getLocale() == 'id' ? $relatedPaint->title : $relatedPaint->title_en }}">
                                @else
                                    <img src="{{ asset('images/artwork-placeholder.jpg') }}" alt="{{ app()->getLocale() == 'id' ? $relatedPaint->title : $relatedPaint->title_en }}">
                                @endif
                                <div class="artwork-overlay">
                                    <a href="{{ route('artwork.paints.show', $relatedPaint->id) }}" class="artwork-link">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="artwork-info bg-white p-3">
                                <h5><a href="{{ route('artwork.paints.show', $relatedPaint->id) }}" class="text-decoration-none">{{ app()->getLocale() == 'id' ? $relatedPaint->title : $relatedPaint->title_en }}</a></h5>
                                <p class="artist mb-1"><i class="fas fa-user-alt me-2"></i>{{ app()->getLocale() == 'id' ? $relatedPaint->artist : $relatedPaint->artist_en }}</p>
                                @if($relatedPaint->year)
                                    <p class="year mb-0"><i class="fas fa-calendar-alt me-2"></i>{{ $relatedPaint->year }}</p>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif
@endsection

@push('styles')
<style>
    .page-header {
        background: linear-gradient(rgba(0, 0, 0, 0.65), rgba(0, 0, 0, 0.65)), url('{{ asset('images/header-bg.jpg') }}') center/cover no-repeat;
        padding: 80px 0;
        margin-bottom: 0;
    }

    .page-title {
        color: white;
        font-weight: 700;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .breadcrumb-item, .breadcrumb-item a {
        color: rgba(255, 255, 255, 0.8);
    }

    .breadcrumb-item.active {
        color: white;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        color: rgba(255, 255, 255, 0.6);
    }

    .section-padding {
        padding: 70px 0;
    }

    .section-title h2 {
        font-weight: 600;
        color: #333;
        position: relative;
        margin-bottom: 15px;
    }

    .divider {
        width: 60px;
        height: 3px;
        background-color: #198754;
        margin-bottom: 20px;
    }

    .paint-image-container {
        position: relative;
        overflow: hidden;
        border-radius: 8px;
    }

    .paint-image-container img {
        width: 100%;
        transition: transform 0.5s ease;
    }

    .image-zoom {
        position: absolute;
        bottom: 15px;
        right: 15px;
        width: 40px;
        height: 40px;
        background-color: rgba(25, 135, 84, 0.8);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .image-zoom:hover {
        background-color: rgba(25, 135, 84, 1);
        color: white;
    }

    .icon-box {
        width: 40px;
        height: 40px;
        background-color: #f8f9fa;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #198754;
    }

    .artwork-card {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        height: 100%;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .artwork-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .artwork-image {
        position: relative;
        height: 250px;
        overflow: hidden;
    }

    .artwork-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }

    .artwork-card:hover .artwork-image img {
        transform: scale(1.1);
    }

    .artwork-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(25, 135, 84, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .artwork-card:hover .artwork-overlay {
        opacity: 1;
    }

    .artwork-link {
        color: white;
        font-size: 24px;
    }

    .artist, .year {
        font-size: 0.9rem;
        color: #6c757d;
    }

    /* Lightbox Styles */
    .lightbox {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }

    .lightbox-content {
        position: relative;
        max-width: 90%;
        max-height: 90%;
    }

    .lightbox-content img {
        max-width: 100%;
        max-height: 90vh;
        display: block;
        margin: 0 auto;
    }

    .lightbox-close {
        position: absolute;
        top: -40px;
        right: 0;
        color: white;
        font-size: 30px;
        cursor: pointer;
    }
</style>
@endpush

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Function to create and show lightbox
        function showLightbox(imgSrc) {
            // Check if a lightbox already exists and remove it first
            const existingLightbox = document.querySelector('.lightbox');
            if (existingLightbox) {
                document.body.removeChild(existingLightbox);
            }

            // Create new lightbox
            const lightbox = document.createElement('div');
            lightbox.classList.add('lightbox');

            lightbox.innerHTML = `
                <div class="lightbox-content">
                    <img src="${imgSrc}" alt="{{ app()->getLocale() == 'id' ? $paint->title : $paint->title_en }}">
                    <span class="lightbox-close">&times;</span>
                </div>
            `;
            document.body.appendChild(lightbox);

            // Prevent scrolling when lightbox is open
            document.body.style.overflow = 'hidden';

            // Close lightbox when clicking on the close button or outside the image
            lightbox.addEventListener('click', function(e) {
                if (e.target === this || e.target.classList.contains('lightbox-close')) {
                    document.body.removeChild(lightbox);
                    document.body.style.overflow = 'auto';
                }
            });

            // Also close on ESC key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    const lightbox = document.querySelector('.lightbox');
                    if (lightbox) {
                        document.body.removeChild(lightbox);
                        document.body.style.overflow = 'auto';
                    }
                }
            });
        }

        // Image zoom functionality
        const imageZoom = document.querySelector('.image-zoom');
        if (imageZoom) {
            imageZoom.addEventListener('click', function(e) {
                e.preventDefault();
                const imgSrc = this.getAttribute('href');
                showLightbox(imgSrc);
            });
        }

        // View fullscreen button
        const viewFullscreen = document.querySelector('.view-fullscreen');
        if (viewFullscreen) {
            viewFullscreen.addEventListener('click', function(e) {
                e.preventDefault();
                const imgSrc = this.getAttribute('href');
                showLightbox(imgSrc);
            });
        }
    });
</script>
@endpush
