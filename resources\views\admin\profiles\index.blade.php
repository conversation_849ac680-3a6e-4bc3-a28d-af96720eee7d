@extends('admin.layouts.app')

@section('title', 'Profile Management')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Profile Management</h1>
        <a href="{{ route('admin.profiles.create') }}" class="btn btn-success">
            <i class="fas fa-plus"></i> Add New
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Title</th>
                            <th>Title (EN)</th>
                            <th>Type</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($profiles as $profile)
                            <tr>
                                <td>{{ $profile->id }}</td>
                                <td>{{ $profile->title }}</td>
                                <td>{{ $profile->title_en }}</td>
                                <td>
                                    @if($profile->type == 'motto')
                                        <span class="badge bg-warning">Motto</span>
                                    @elseif($profile->type == 'vision')
                                        <span class="badge bg-primary">Vision</span>
                                    @elseif($profile->type == 'mission')
                                        <span class="badge bg-success">Mission</span>
                                    @elseif($profile->type == 'history')
                                        <span class="badge bg-info">History</span>
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('admin.profiles.show', $profile) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.profiles.edit', $profile) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.profiles.destroy', $profile) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger btn-delete" onclick="return confirm('Are you sure you want to delete this item?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="text-center">No profiles found.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection
