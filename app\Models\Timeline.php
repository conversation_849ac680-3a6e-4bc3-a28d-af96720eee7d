<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Timeline extends Model
{
    protected $fillable = [
        'profile_id',
        'year',
        'title',
        'title_en',
        'content',
        'content_en',
        'image',
        'order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the profile that owns the timeline entry.
     */
    public function profile()
    {
        return $this->belongsTo(Profile::class);
    }
}
