@extends('admin.layouts.app')

@section('title', 'View Message')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View Message</h1>
        <a href="{{ route('admin.contacts.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Message Details</h5>
                    <div>
                        <span class="badge {{ $contact->is_read ? 'bg-success' : 'bg-warning' }} fs-6">
                            {{ $contact->is_read ? 'Read' : 'Unread' }}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Subject:</div>
                        <div class="col-md-9">{{ $contact->subject }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Date:</div>
                        <div class="col-md-9">{{ $contact->created_at->format('d M Y H:i:s') }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">From:</div>
                        <div class="col-md-9">{{ $contact->name }} ({{ $contact->email }})</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Phone:</div>
                        <div class="col-md-9">{{ $contact->phone ?: 'Not provided' }}</div>
                    </div>

                    <div class="row">
                        <div class="col-md-3 fw-bold">Message:</div>
                        <div class="col-md-9">
                            <div class="p-3 bg-light rounded">
                                {!! nl2br(e($contact->message)) !!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            @if($contact->hasReply())
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        Your Reply

                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Date:</div>
                        <div class="col-md-9">{{ $contact->replied_at->format('d M Y H:i:s') }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">By:</div>
                        <div class="col-md-9">
                            {{ $contact->repliedBy ? $contact->repliedBy->name : 'System' }}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3 fw-bold">Reply:</div>
                        <div class="col-md-9">
                            <div class="p-3 bg-light rounded">
                                {!! nl2br(e($contact->reply_message)) !!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @else
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Reply to Message</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.contacts.reply', $contact) }}" method="POST">
                        @csrf
                        <div class="mb-3">
                            <label for="reply_message" class="form-label">Your Reply</label>
                            <textarea class="form-control @error('reply_message') is-invalid @enderror" id="reply_message" name="reply_message" rows="6" required>{{ old('reply_message') }}</textarea>
                            @error('reply_message')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-paper-plane me-1"></i> Send Reply
                        </button>
                    </form>
                </div>
            </div>
            @endif
        </div>

        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <a href="mailto:{{ $contact->email }}" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-reply me-1"></i> Reply via Email
                    </a>

                    @if(!$contact->is_read)
                        <form action="{{ route('admin.contacts.mark-as-read', $contact) }}" method="POST" class="mb-2">
                            @csrf
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-check me-1"></i> Mark as Read
                            </button>
                        </form>
                    @endif

                    <form action="{{ route('admin.contacts.destroy', $contact) }}" method="POST">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Are you sure you want to delete this message? This action cannot be undone.')">
                            <i class="fas fa-trash me-1"></i> Delete Message
                        </button>
                    </form>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Contact Information</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            <div class="bg-light rounded-circle p-2">
                                <i class="fas fa-user text-primary"></i>
                            </div>
                        </div>
                        <div>
                            <div class="small text-muted">Name</div>
                            <div>{{ $contact->name }}</div>
                        </div>
                    </div>

                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            <div class="bg-light rounded-circle p-2">
                                <i class="fas fa-envelope text-primary"></i>
                            </div>
                        </div>
                        <div>
                            <div class="small text-muted">Email</div>
                            <div>{{ $contact->email }}</div>
                        </div>
                    </div>

                    @if($contact->phone)
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <div class="bg-light rounded-circle p-2">
                                    <i class="fas fa-phone text-primary"></i>
                                </div>
                            </div>
                            <div>
                                <div class="small text-muted">Phone</div>
                                <div>{{ $contact->phone }}</div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection
