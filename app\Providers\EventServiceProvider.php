<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;
use App\Events\AnnouncementCreated;
use App\Events\AnnouncementUpdated;
use App\Events\ContactCreated;
use App\Events\RegistrationCreated;
use App\Listeners\SendAnnouncementNotification;
use App\Listeners\SendContactNotification;
use App\Listeners\SendRegistrationNotification;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        AnnouncementCreated::class => [
            SendAnnouncementNotification::class,
        ],
        AnnouncementUpdated::class => [
            SendAnnouncementNotification::class,
        ],
        ContactCreated::class => [
            SendContactNotification::class,
        ],
        RegistrationCreated::class => [
            SendRegistrationNotification::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
