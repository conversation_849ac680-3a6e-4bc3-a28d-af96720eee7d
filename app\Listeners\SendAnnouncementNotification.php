<?php

namespace App\Listeners;

use App\Models\Announcement;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Models\Setting;
use Illuminate\Support\Facades\Log;

class SendAnnouncementNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        // Constructor
    }

    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        // Check if the event has an announcement
        if (!isset($event->announcement) || !($event->announcement instanceof Announcement)) {
            Log::warning('Event does not have a valid announcement property');
            return;
        }

        $announcement = $event->announcement;

        // Here you could implement other notification methods like email
        // For now, we'll just log that we received the announcement
        Log::info('Announcement notification', [
            'announcement_id' => $announcement->id,
            'title' => $announcement->title,
            'start_date' => $announcement->start_date,
            'end_date' => $announcement->end_date
        ]);
    }
}
