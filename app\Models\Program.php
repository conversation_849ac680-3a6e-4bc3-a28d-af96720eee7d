<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Program extends Model
{
    protected $fillable = [
        'name',
        'name_en',
        'description',
        'description_en',
        'icon',
        'image',
        'is_featured',
        'order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
    ];

    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->orderBy('order', 'asc');
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_active', true)
            ->where('is_featured', true)
            ->orderBy('order', 'asc');
    }
}
