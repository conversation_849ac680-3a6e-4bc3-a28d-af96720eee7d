<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Services\ImageService;

class GalleryController extends Controller
{
    /**
     * The image service instance.
     *
     * @var \App\Services\ImageService
     */
    protected $imageService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\ImageService  $imageService
     * @return void
     */
    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Display a listing of the galleries.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $galleries = \App\Models\Gallery::latest()->paginate(12);
        return view('admin.galleries.index', compact('galleries'));
    }

    /**
     * Show the form for creating a new gallery.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.galleries.create');
    }

    /**
     * Store a newly created gallery in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'category' => 'required|string|in:Events,Activities',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif',
        ]);

        $data = $request->all();

        // Handle image upload with compression
        if ($request->hasFile('image')) {
            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'galleries',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }

        \App\Models\Gallery::create($data);

        return redirect()->route('admin.galleries.index')
            ->with('success', 'Gallery created successfully.');
    }

    /**
     * Display the specified gallery.
     *
     * @param  \App\Models\Gallery  $gallery
     * @return \Illuminate\View\View
     */
    public function show(\App\Models\Gallery $gallery)
    {
        return view('admin.galleries.show', compact('gallery'));
    }

    /**
     * Show the form for editing the specified gallery.
     *
     * @param  \App\Models\Gallery  $gallery
     * @return \Illuminate\View\View
     */
    public function edit(\App\Models\Gallery $gallery)
    {
        return view('admin.galleries.edit', compact('gallery'));
    }

    /**
     * Update the specified gallery in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Gallery  $gallery
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, \App\Models\Gallery $gallery)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'category' => 'required|string|in:Events,Activities',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif',
        ]);

        $data = $request->all();

        // Handle image upload with compression
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($gallery->image) {
                $this->imageService->deleteImage($gallery->image);
            }

            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'galleries',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }

        $gallery->update($data);

        return redirect()->route('admin.galleries.index')
            ->with('success', 'Gallery updated successfully.');
    }

    /**
     * Remove the specified gallery from storage.
     *
     * @param  \App\Models\Gallery  $gallery
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(\App\Models\Gallery $gallery)
    {
        // Delete associated image
        if ($gallery->image) {
            $this->imageService->deleteImage($gallery->image);
        }

        $gallery->delete();

        return redirect()->route('admin.galleries.index')
            ->with('success', 'Gallery deleted successfully.');
    }
}
