<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class ProfileController extends Controller
{
    /**
     * Display the profile page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get motto profile
        $motto = \App\Models\Profile::where('type', 'motto')
            ->where('is_active', true)
            ->orderBy('order')
            ->first();

        // Get vision profile
        $vision = \App\Models\Profile::where('type', 'vision')
            ->where('is_active', true)
            ->orderBy('order')
            ->first();

        // Get mission profile
        $mission = \App\Models\Profile::where('type', 'mission')
            ->where('is_active', true)
            ->orderBy('order')
            ->first();

        // Get history profile
        $history = \App\Models\Profile::where('type', 'history')
            ->where('is_active', true)
            ->orderBy('order')
            ->first();

        return view('public.profile.index', compact('motto', 'vision', 'mission', 'history'));
    }

    /**
     * Display the vision and mission page.
     *
     * @return \Illuminate\View\View
     */
    public function visionMission()
    {
        // Get motto profile
        $motto = \App\Models\Profile::where('type', 'motto')
            ->where('is_active', true)
            ->orderBy('order')
            ->first();

        // Get vision profile
        $vision = \App\Models\Profile::where('type', 'vision')
            ->where('is_active', true)
            ->orderBy('order')
            ->first();

        // Get mission profile
        $mission = \App\Models\Profile::where('type', 'mission')
            ->where('is_active', true)
            ->orderBy('order')
            ->first();

        return view('public.profile.vision-mission', compact('motto', 'vision', 'mission'));
    }

    /**
     * Display the history page.
     *
     * @return \Illuminate\View\View
     */
    public function history()
    {
        $history = \App\Models\Profile::where('type', 'history')
            ->where('is_active', true)
            ->orderBy('order')
            ->first();

        $timelines = [];
        if ($history) {
            $timelines = $history->timelines()
                ->where('is_active', true)
                ->orderBy('order')
                ->get();
        }

        return view('public.profile.history', compact('history', 'timelines'));
    }
}
