<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\TelegramBotService;
use App\Models\TelegramSubscriber;

class TestTelegramAutoSubscribe extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:test-auto-subscribe';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the automatic subscription of Telegram users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing automatic Telegram subscription...');
        
        // Get all subscribers
        $subscribers = TelegramSubscriber::all();
        $this->info('Total subscribers: ' . $subscribers->count());
        
        $activeSubscribers = TelegramSubscriber::where('is_active', true)->get();
        $this->info('Active subscribers: ' . $activeSubscribers->count());
        
        $inactiveSubscribers = TelegramSubscriber::where('is_active', false)->get();
        $this->info('Inactive subscribers: ' . $inactiveSubscribers->count());
        
        // Display the most recent subscribers
        $this->info("\nMost recent subscribers:");
        $recentSubscribers = TelegramSubscriber::orderBy('created_at', 'desc')->take(5)->get();
        
        $headers = ['ID', 'Chat ID', 'Username', 'Name', 'Active', 'Subscribed At'];
        $rows = [];
        
        foreach ($recentSubscribers as $subscriber) {
            $rows[] = [
                $subscriber->id,
                $subscriber->chat_id,
                $subscriber->username ?? 'N/A',
                $subscriber->first_name . ' ' . $subscriber->last_name,
                $subscriber->is_active ? 'Yes' : 'No',
                $subscriber->subscribed_at->format('Y-m-d H:i:s')
            ];
        }
        
        $this->table($headers, $rows);
        
        // Simulate a message to test auto-subscription
        $this->info("\nSimulating a message to test auto-subscription...");
        $this->info("Note: This is just a simulation. No actual message will be sent.");
        
        // Show the code that would be executed
        $this->info("\nThe following code would be executed when a user sends a message:");
        $this->line('$subscriber = TelegramSubscriber::where(\'chat_id\', $chatId)->first();');
        $this->line('if (!$subscriber) {');
        $this->line('    // New subscriber - create and activate automatically');
        $this->line('    $subscriber = new TelegramSubscriber([');
        $this->line('        \'chat_id\' => $chatId,');
        $this->line('        \'username\' => $username,');
        $this->line('        \'first_name\' => $firstName,');
        $this->line('        \'last_name\' => $lastName,');
        $this->line('        \'is_active\' => true, // Automatically activate');
        $this->line('        \'subscribed_at\' => now(),');
        $this->line('        \'last_interaction_at\' => now(),');
        $this->line('    ]);');
        $this->line('    $subscriber->save();');
        $this->line('}');
        
        $this->info("\nTest completed successfully!");
        
        return 0;
    }
}
