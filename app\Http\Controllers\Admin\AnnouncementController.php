<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\AnnouncementRequest;
use App\Models\Announcement;
use App\Events\AnnouncementCreated;
use App\Events\AnnouncementUpdated;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AnnouncementController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $announcements = Announcement::orderBy('order', 'asc')->get();
        return view('admin.announcements.index', compact('announcements'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.announcements.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\AnnouncementRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(AnnouncementRequest $request)
    {
        $data = $request->validated();

        // Set default order if not provided
        if (!isset($data['order'])) {
            $maxOrder = Announcement::max('order');
            $data['order'] = $maxOrder ? $maxOrder + 1 : 1;
        }

        // Process HTML entities in content
        $data['content'] = html_entity_decode($data['content']);
        $data['content_en'] = html_entity_decode($data['content_en']);

        // Set is_active to true by default
        $data['is_active'] = true;

        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image'] = Announcement::make()->handleImageUpload($request->file('image'), 'image');
        }

        // Handle file upload (PDF)
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('announcements/files', $fileName, 'public');
            $data['file'] = $filePath;
        }

        // Create the announcement
        $announcement = Announcement::create($data);

        // Dispatch the event
        event(new AnnouncementCreated($announcement));

        return redirect()->route('admin.announcements.index')
            ->with('success', 'Announcement created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show(string $id)
    {
        $announcement = Announcement::findOrFail($id);
        return view('admin.announcements.show', compact('announcement'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit(string $id)
    {
        $announcement = Announcement::findOrFail($id);
        return view('admin.announcements.edit', compact('announcement'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\AnnouncementRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(AnnouncementRequest $request, string $id)
    {
        $announcement = Announcement::findOrFail($id);
        $data = $request->validated();

        // Process HTML entities in content
        $data['content'] = html_entity_decode($data['content']);
        $data['content_en'] = html_entity_decode($data['content_en']);

        // Set is_active to true by default
        $data['is_active'] = true;

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image first
            $announcement->deleteImage('image');

            // Upload new image
            $data['image'] = $announcement->handleImageUpload($request->file('image'), 'image');
        }

        // Handle file upload (PDF)
        if ($request->hasFile('file')) {
            // Delete old file if exists
            if ($announcement->file) {
                Storage::disk('public')->delete($announcement->file);
            }

            // Upload new file
            $file = $request->file('file');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('announcements/files', $fileName, 'public');
            $data['file'] = $filePath;
        }

        // Update the announcement
        $announcement->update($data);

        // Dispatch the event
        event(new AnnouncementUpdated($announcement));

        return redirect()->route('admin.announcements.index')
            ->with('success', 'Announcement updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(string $id)
    {
        $announcement = Announcement::findOrFail($id);

        // The ImageUploadable trait will automatically delete associated images
        $announcement->delete();

        return redirect()->route('admin.announcements.index')
            ->with('success', 'Announcement deleted successfully.');
    }
}
