<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Call the ResetUsersSeeder to reset users table and create admin user
        $this->call(ResetUsersSeeder::class);

        // Create default settings
        $this->createDefaultSettings();

        // Create default profiles
        $this->createDefaultProfiles();

        // Create default menu
        $this->call(MenuSeeder::class);

        // Fix programs table
        $this->call(FixProgramsTableSeeder::class);

        // Create literature types and placeholder literature
        $this->call(LiteratureSeeder::class);
    }

    /**
     * Create default settings.
     */
    private function createDefaultSettings(): void
    {
        $settings = [
            // General settings
            ['key' => 'site_name', 'value' => 'Pondok Pesantren Nurul Hayah 4', 'group' => 'general', 'is_translatable' => true],
            ['key' => 'site_description', 'value' => 'Pondok Pesantren Modern dengan Pendidikan Berkualitas', 'group' => 'general', 'is_translatable' => true],
            ['key' => 'site_logo', 'value' => 'logo.png', 'group' => 'general', 'is_translatable' => false],
            ['key' => 'site_favicon', 'value' => 'favicon.ico', 'group' => 'general', 'is_translatable' => false],

            // Contact settings
            ['key' => 'address', 'value' => 'Jl. Pesantren No. 123, Kota Jakarta, Indonesia', 'group' => 'contact', 'is_translatable' => true],
            ['key' => 'phone', 'value' => '+62 123 4567 890', 'group' => 'contact', 'is_translatable' => false],
            ['key' => 'email', 'value' => '<EMAIL>', 'group' => 'contact', 'is_translatable' => false],
            ['key' => 'map_url', 'value' => 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3966.6664463317765!2d106.82496231476882!3d-6.175392395529971!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x2e69f5d2e764b12d%3A0x3d2ad6e1e0e9bcc8!2sMonumen%20Nasional!5e0!3m2!1sen!2sid!4v1650123456789!5m2!1sen!2sid', 'group' => 'contact', 'is_translatable' => false],

            // Social media settings
            ['key' => 'facebook', 'value' => 'https://facebook.com/nurulhayah4', 'group' => 'social', 'is_translatable' => false],
            ['key' => 'instagram', 'value' => 'https://instagram.com/nurulhayah4', 'group' => 'social', 'is_translatable' => false],
            ['key' => 'twitter', 'value' => 'https://twitter.com/nurulhayah4', 'group' => 'social', 'is_translatable' => false],
            ['key' => 'youtube', 'value' => 'https://youtube.com/nurulhayah4', 'group' => 'social', 'is_translatable' => false],
        ];

        foreach ($settings as $setting) {
            \App\Models\Setting::create($setting);
        }
    }

    /**
     * Create default profiles.
     */
    private function createDefaultProfiles(): void
    {
        $profiles = [
            // Vision
            [
                'type' => 'vision_mission',
                'title' => 'Visi',
                'title_en' => 'Vision',
                'content' => 'Menjadi lembaga pendidikan Islam terkemuka yang menghasilkan generasi Muslim yang berakhlak mulia, berwawasan luas, dan berdaya saing global.',
                'content_en' => 'To become a leading Islamic educational institution that produces a generation of Muslims with noble character, broad insight, and global competitiveness.',
                'order' => 1,
                'is_active' => true,
            ],

            // Mission
            [
                'type' => 'vision_mission',
                'title' => 'Misi',
                'title_en' => 'Mission',
                'content' => "1. Menyelenggarakan pendidikan Islam yang berkualitas dan komprehensif.\n2. Membentuk karakter dan kepribadian santri yang berakhlak mulia berdasarkan Al-Qur'an dan Sunnah.\n3. Mengembangkan potensi santri dalam bidang akademik, keterampilan, dan kepemimpinan.\n4. Membekali santri dengan penguasaan bahasa Arab dan Inggris.\n5. Mempersiapkan santri untuk melanjutkan pendidikan ke jenjang yang lebih tinggi.",
                'content_en' => "1. Providing quality and comprehensive Islamic education.\n2. Shaping the character and personality of students with noble character based on the Qur'an and Sunnah.\n3. Developing students' potential in academics, skills, and leadership.\n4. Equipping students with Arabic and English language proficiency.\n5. Preparing students to continue their education to a higher level.",
                'order' => 2,
                'is_active' => true,
            ],

            // History
            [
                'type' => 'history',
                'title' => 'Sejarah Singkat',
                'title_en' => 'Brief History',
                'content' => 'Pondok Pesantren Nurul Hayah 4 didirikan pada tahun 2010 oleh KH. Ahmad Fauzi sebagai wujud kepedulian terhadap pendidikan Islam yang berkualitas. Berawal dari sebuah musholla kecil dengan santri yang terbatas, kini Pondok Pesantren Nurul Hayah 4 telah berkembang menjadi lembaga pendidikan Islam modern yang dilengkapi dengan fasilitas memadai dan sistem pendidikan yang terintegrasi. Dalam perjalanannya, Pondok Pesantren Nurul Hayah 4 terus berkomitmen untuk menghasilkan generasi Muslim yang berakhlak mulia, berwawasan luas, dan berdaya saing global.',
                'content_en' => 'Nurul Hayah 4 Islamic Boarding School was founded in 2010 by KH. Ahmad Fauzi as a form of concern for quality Islamic education. Starting from a small prayer room with limited students, now Nurul Hayah 4 Islamic Boarding School has developed into a modern Islamic educational institution equipped with adequate facilities and an integrated education system. Throughout its journey, Nurul Hayah 4 Islamic Boarding School continues to be committed to producing a generation of Muslims with noble character, broad insight, and global competitiveness.',
                'order' => 1,
                'is_active' => true,
            ],
        ];

        foreach ($profiles as $profile) {
            \App\Models\Profile::create($profile);
        }
    }
}
