<?php

namespace App\Traits;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use App\Services\ImageService;

trait ImageUploadable
{
    /**
     * Get the image fields for this model.
     * 
     * Override this method in your model to define which fields are images.
     * 
     * @return array
     */
    public function getImageFields(): array
    {
        return [];
    }

    /**
     * Get the image upload paths for this model.
     * 
     * Override this method in your model to define the upload paths for each image field.
     * 
     * @return array
     */
    public function getImagePaths(): array
    {
        return [];
    }

    /**
     * Get the image dimensions for this model.
     * 
     * Override this method in your model to define custom dimensions for each image field.
     * Format: [field_name => ['width' => int, 'height' => int, 'quality' => int]]
     * 
     * @return array
     */
    public function getImageDimensions(): array
    {
        return [];
    }

    /**
     * Handle image upload for the model.
     * 
     * @param UploadedFile $file
     * @param string $field
     * @return string|null
     */
    public function handleImageUpload(UploadedFile $file, string $field): ?string
    {
        $imageService = app(ImageService::class);
        $paths = $this->getImagePaths();
        $dimensions = $this->getImageDimensions();
        
        // Get the path for this field or use a default
        $path = $paths[$field] ?? strtolower(class_basename($this)) . 's';
        
        // Get dimensions for this field or use defaults
        $width = $dimensions[$field]['width'] ?? config('image.max_width');
        $height = $dimensions[$field]['height'] ?? config('image.max_height');
        $quality = $dimensions[$field]['quality'] ?? config('image.quality');
        
        // Upload and compress the image
        return $imageService->compressAndSave(
            $file,
            $path,
            $quality,
            $width,
            $height
        );
    }

    /**
     * Delete an image for the model.
     * 
     * @param string $field
     * @return bool
     */
    public function deleteImage(string $field): bool
    {
        if (empty($this->{$field})) {
            return false;
        }
        
        $imageService = app(ImageService::class);
        return $imageService->deleteImage($this->{$field});
    }

    /**
     * Delete all images associated with the model.
     * 
     * @return void
     */
    public function deleteAllImages(): void
    {
        $imageFields = $this->getImageFields();
        
        foreach ($imageFields as $field) {
            $this->deleteImage($field);
        }
    }

    /**
     * Boot the trait.
     * 
     * @return void
     */
    public static function bootImageUploadable(): void
    {
        // Delete images when model is deleted
        static::deleting(function ($model) {
            $model->deleteAllImages();
        });
    }
}
