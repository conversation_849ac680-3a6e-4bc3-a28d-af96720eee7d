<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Director;
use App\Models\Insight;
use Illuminate\Http\Request;

class DirectorInsightController extends Controller
{
    /**
     * Display the Director's Insight page with tabs.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $activeTab = $request->query('tab', 'director');

        // Validate tab parameter
        if (!in_array($activeTab, ['director', 'insight'])) {
            $activeTab = 'director';
        }

        if ($activeTab === 'director') {
            $items = Director::orderBy('order', 'asc')->get();
            $directorExists = $items->count() > 0;
        } else {
            $items = Insight::orderBy('order', 'asc')->get();
            $directorExists = Director::count() > 0;
        }

        return view('admin.directors_insight.index', compact(
            'items',
            'activeTab',
            'directorExists'
        ));
    }
}
