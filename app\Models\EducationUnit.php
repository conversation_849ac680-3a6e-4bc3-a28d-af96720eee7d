<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class EducationUnit extends Model
{
    protected $fillable = [
        'name',
        'name_en',
        'edu_type',
        'description',
        'description_en',
        'level',
        'level_en',
        'facilities',
        'facilities_en',
        'address',
        'phone',
        'email',
        'website',
        'principal_name',
        'principal_name_en',
        'principal_education',
        'principal_education_en',
        'principal_experience',
        'principal_experience_en',
        'principal_achievements',
        'principal_achievements_en',
        'principal_image',
        'image',
        'order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'order' => 'integer',
    ];

    /**
     * Scope a query to only include active education units.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->orderBy('order', 'asc');
    }

    /**
     * Scope a query to only include formal education units.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFormal($query)
    {
        return $query->where('edu_type', 'formal');
    }

    /**
     * Scope a query to only include non-formal education units.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNonFormal($query)
    {
        return $query->where('edu_type', 'non-formal');
    }
}
