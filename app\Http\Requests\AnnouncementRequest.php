<?php

namespace App\Http\Requests;

class AnnouncementRequest extends ImageUploadRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the base validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    protected function baseRules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'content' => 'required|string',
            'content_en' => 'required|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'order' => 'nullable|integer',
            'file' => 'nullable|file|mimes:pdf|max:10240', // Max 10MB, PDF only
        ];
    }

    /**
     * Get the image fields for this request.
     *
     * @return array
     */
    protected function getImageFields(): array
    {
        return [
            'image' => false, // Not required
        ];
    }
}
