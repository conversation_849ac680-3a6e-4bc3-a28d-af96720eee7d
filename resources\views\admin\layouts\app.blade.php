<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Admin') | {{ \App\Helpers\SettingHelper::getWebsiteName() }}</title>

    <!-- Favicon -->
    <link rel="icon" href="{{ asset('storage/' . \App\Models\Setting::getValue('favicon', 'images/favicon.ico')) }}">
    <link rel="shortcut icon" href="{{ asset('storage/' . \App\Models\Setting::getValue('favicon', 'images/favicon.ico')) }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- Flag Icon CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/6.6.6/css/flag-icons.min.css">

    <!-- Summernote Lite CSS -->
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-lite.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{{ asset('css/admin.css') }}" rel="stylesheet">
    <link href="{{ asset('css/summernote-custom.css') }}?v={{ time() }}" rel="stylesheet">

    @vite(['resources/sass/app.scss', 'resources/js/app.js'])
    @stack('styles')

    <!-- Custom Dropdown CSS -->
    <style>
        /* Required field indicator */
        .form-label.required:after {
            content: " *";
            color: red;
        }

        /* User menu styles */
        #userMenu {
            color: #333;
            padding: 0.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
        }

        #userMenu:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        #userMenu::after {
            display: none; /* Hide default caret */
        }

        /* Dropdown menu styles */
        .dropdown-menu {
            border: none;
            border-radius: 0.5rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            padding: 0.5rem 0;
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .dropdown-item:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .dropdown-item i {
            width: 1.25rem;
            text-align: center;
        }
    </style>



    <!-- Vite Development Server Check -->
    <script type="module">
        import { createApp } from 'vue';
        console.log('Vue is available:', !!createApp);
    </script>
</head>
<body>
    <div class="d-flex" id="wrapper">
        <!-- Sidebar -->
        @include('admin.layouts.sidebar')

        <!-- Page Content -->
        <div id="page-content-wrapper">
            <!-- Top Navigation -->
            @include('admin.layouts.navbar')

            <!-- Main Content -->
            <div class="container-fluid p-4">
                @include('admin.layouts.alerts')

                @yield('content')
            </div>
        </div>
    </div>

    <!-- jQuery (must be loaded first) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Summernote Lite JS -->
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-lite.min.js"></script>

    <!-- Custom JS -->
    <script src="{{ asset('js/admin.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/summernote-init.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/simple-image-upload.js') }}?v={{ time() }}"></script>

    <!-- Summernote initialization is now handled in summernote-init.js -->

    @stack('scripts')

    <script>
        console.log('Admin layout loaded');

        // Ensure dropdowns work properly
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all dropdowns
            var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
            var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl, {
                    offset: [0, 10], // Add some offset for better appearance
                    boundary: 'viewport' // Keep dropdown within viewport
                });
            });

            // Add click handler for user menu specifically
            var userMenu = document.getElementById('userMenu');
            if (userMenu) {
                userMenu.addEventListener('click', function(e) {
                    e.preventDefault();
                });
            }

            // Add click handler for dropdown items to prevent immediate closing
            var dropdownItems = document.querySelectorAll('.dropdown-item');
            dropdownItems.forEach(function(item) {
                item.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            });
        });
    </script>
</body>
</html>
