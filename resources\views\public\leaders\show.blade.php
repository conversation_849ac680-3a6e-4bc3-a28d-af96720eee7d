@extends('public.layouts.app')

@php
    $name = app()->getLocale() == 'id' ? $leader->name : $leader->name_en;
    // If English name is empty, use Indonesian name
    if (app()->getLocale() == 'en' && empty($name) && !empty($leader->name)) {
        $name = $leader->name;
    }
@endphp

@section('title', $name)

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    @php
                        $name = app()->getLocale() == 'id' ? $leader->name : $leader->name_en;
                        $position = app()->getLocale() == 'id' ? $leader->position : $leader->position_en;

                        // If English name is empty, use Indonesian name
                        if (app()->getLocale() == 'en' && empty($name) && !empty($leader->name)) {
                            $name = $leader->name;
                        }

                        // If English position is empty, use Indonesian position
                        if (app()->getLocale() == 'en' && empty($position) && !empty($leader->position)) {
                            $position = $leader->position;
                        }
                    @endphp
                    <h1 class="fw-bold mb-3">{{ $name }}</h1>
                    <p class="lead mb-3">{{ $position }}</p>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('leaders') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Pemimpin Kami' : 'Our Leaders' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ $name }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Leader Profile Section -->
    <section class="section-padding">
        <div class="container">
            <div class="row">
                <!-- Leader Profile Sidebar -->
                <div class="col-lg-4 mb-5 mb-lg-0">
                    <div class="card border-0 shadow-sm sticky-lg-top" style="top: 100px; z-index: 1;">
                        <div class="card-body p-0">
                            <div class="leader-profile-image position-relative">
                                @php
                                    $name = app()->getLocale() == 'id' ? $leader->name : $leader->name_en;
                                    // If English name is empty, use Indonesian name
                                    if (app()->getLocale() == 'en' && empty($name) && !empty($leader->name)) {
                                        $name = $leader->name;
                                    }
                                @endphp
                                @if($leader->image)
                                    <img src="{{ asset('storage/' . $leader->image) }}" alt="{{ $name }}" class="img-fluid w-100" style="object-fit: cover; height: 400px;">
                                @else
                                    <img src="{{ asset('images/leader-placeholder.jpg') }}" alt="{{ $name }}" class="img-fluid w-100" style="object-fit: cover; height: 400px;">
                                @endif
                                <div class="leader-profile-social">
                                    @if($leader->social_facebook)
                                        <a href="{{ $leader->social_facebook }}" target="_blank" class="social-icon facebook"><i class="fab fa-facebook-f"></i></a>
                                    @endif
                                    @if($leader->social_twitter)
                                        <a href="{{ $leader->social_twitter }}" target="_blank" class="social-icon twitter"><i class="fab fa-x-twitter"></i></a>
                                    @endif
                                    @if($leader->social_instagram)
                                        <a href="{{ $leader->social_instagram }}" target="_blank" class="social-icon instagram"><i class="fab fa-instagram"></i></a>
                                    @endif
                                    @if($leader->social_linkedin)
                                        <a href="{{ $leader->social_linkedin }}" target="_blank" class="social-icon linkedin"><i class="fab fa-telegram"></i></a>
                                    @endif
                                </div>
                            </div>
                            <div class="p-4">
                                @php
                                    $name = app()->getLocale() == 'id' ? $leader->name : $leader->name_en;
                                    $position = app()->getLocale() == 'id' ? $leader->position : $leader->position_en;

                                    // If English name is empty, use Indonesian name
                                    if (app()->getLocale() == 'en' && empty($name) && !empty($leader->name)) {
                                        $name = $leader->name;
                                    }

                                    // If English position is empty, use Indonesian position
                                    if (app()->getLocale() == 'en' && empty($position) && !empty($leader->position)) {
                                        $position = $leader->position;
                                    }
                                @endphp
                                <h3 class="mb-1">{{ $name }}</h3>
                                <p class="text-success mb-3">{{ $position }}</p>

                                <div class="leader-profile-contact">
                                    @if($leader->email)
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="contact-icon me-3">
                                                <i class="fas fa-envelope"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 text-muted small">{{ app()->getLocale() == 'id' ? 'Email' : 'Email' }}</h6>
                                                <a href="mailto:{{ $leader->email }}" class="text-dark">{{ $leader->email }}</a>
                                            </div>
                                        </div>
                                    @endif

                                    @if($leader->phone)
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="contact-icon me-3">
                                                <i class="fas fa-phone"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 text-muted small">{{ app()->getLocale() == 'id' ? 'Telepon' : 'Phone' }}</h6>
                                                <a href="tel:{{ $leader->phone }}" class="text-dark">{{ $leader->phone }}</a>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Leader Profile Content -->
                <div class="col-lg-8">
                    <!-- Biography Section -->
                    <div class="card border-0 shadow-sm mb-4 overflow-hidden">
                        <div class="card-header bg-white py-3">
                            <h4 class="mb-0">{{ app()->getLocale() == 'id' ? 'Biografi' : 'Biography' }}</h4>
                        </div>
                        <div class="card-body">
                            <div class="leader-bio">
                                @php
                                    $bio = app()->getLocale() == 'id' ? $leader->bio : $leader->bio_en;
                                    // If English bio is empty, use Indonesian bio
                                    if (app()->getLocale() == 'en' && empty($bio) && !empty($leader->bio)) {
                                        $bio = $leader->bio;
                                    }
                                @endphp
                                {!! $bio !!}
                            </div>
                        </div>
                    </div>

                    <!-- Tabs Section -->
                    <div class="card border-0 shadow-sm mb-4 overflow-hidden">
                        <div class="card-header bg-white p-0">
                            <ul class="nav nav-tabs" id="leaderProfileTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active px-4 py-3" id="motto-tab" data-bs-toggle="tab" data-bs-target="#motto" type="button" role="tab" aria-controls="motto" aria-selected="true">
                                        {{ app()->getLocale() == 'id' ? 'Motto' : 'Motto' }}
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link px-4 py-3" id="education-tab" data-bs-toggle="tab" data-bs-target="#education" type="button" role="tab" aria-controls="education" aria-selected="false">
                                        {{ app()->getLocale() == 'id' ? 'Pendidikan' : 'Education' }}
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link px-4 py-3" id="experience-tab" data-bs-toggle="tab" data-bs-target="#experience" type="button" role="tab" aria-controls="experience" aria-selected="false">
                                        {{ app()->getLocale() == 'id' ? 'Pengalaman' : 'Experience' }}
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link px-4 py-3" id="achievements-tab" data-bs-toggle="tab" data-bs-target="#achievements" type="button" role="tab" aria-controls="achievements" aria-selected="false">
                                        {{ app()->getLocale() == 'id' ? 'Prestasi' : 'Achievements' }}
                                    </button>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body p-4">
                            <div class="tab-content" id="leaderProfileTabsContent">
                                <!-- Motto Tab -->
                                <div class="tab-pane fade show active" id="motto" role="tabpanel" aria-labelledby="motto-tab">
                                    @php
                                        $motto = app()->getLocale() == 'id' ? $leader->motto : $leader->motto_en;
                                        // If English motto is empty, use Indonesian motto
                                        if (app()->getLocale() == 'en' && empty($motto) && !empty($leader->motto)) {
                                            $motto = $leader->motto;
                                        }
                                    @endphp

                                    @if($motto)
                                        <div class="motto-content text-center py-5">
                                            <blockquote class="blockquote">
                                                <p class="mb-0 display-6 fw-light fst-italic">{{ $motto }}</p>
                                                <footer class="blockquote-footer mt-3">
                                                    <cite title="{{ $name }}">{{ $name }}</cite>
                                                </footer>
                                            </blockquote>
                                        </div>
                                    @else
                                        <div class="alert alert-light">
                                            {{ app()->getLocale() == 'id' ? 'Tidak ada motto yang tersedia.' : 'No motto available.' }}
                                        </div>
                                    @endif
                                </div>

                                <!-- Education Tab -->
                                <div class="tab-pane fade" id="education" role="tabpanel" aria-labelledby="education-tab">
                                    @php
                                        $educationHistory = json_decode(app()->getLocale() == 'id' ? $leader->education_history : $leader->education_history_en, true) ?? [];
                                        $educationHistoryId = json_decode($leader->education_history, true) ?? [];

                                        // If English version is empty but Indonesian version exists, use Indonesian data
                                        if (app()->getLocale() == 'en' && (empty($educationHistory) || count($educationHistory) == 0) && count($educationHistoryId) > 0) {
                                            $educationHistory = $educationHistoryId;
                                        } else if (app()->getLocale() == 'en' && count($educationHistory) > 0 && count($educationHistoryId) > 0) {
                                            // For each item in English version, fill in missing fields from Indonesian version
                                            foreach ($educationHistory as $key => $item) {
                                                if (isset($educationHistoryId[$key])) {
                                                    // For fields that should be the same in both languages
                                                    if (!isset($item['year']) || empty($item['year'])) {
                                                        $educationHistory[$key]['year'] = $educationHistoryId[$key]['year'] ?? '';
                                                    }
                                                    if (!isset($item['location']) || empty($item['location'])) {
                                                        $educationHistory[$key]['location'] = $educationHistoryId[$key]['location'] ?? '';
                                                    }
                                                }
                                            }
                                        }
                                    @endphp

                                    @if(count($educationHistory) > 0)
                                        <div class="timeline-modern">
                                            @foreach($educationHistory as $education)
                                                <div class="timeline-item fade-in-up" style="transition-delay: {{ $loop->iteration * 0.1 }}s">
                                                    <div class="timeline-left">
                                                        <div class="timeline-date">{{ $education['year'] ?? '' }}</div>
                                                    </div>
                                                    <div class="timeline-center">
                                                        <div class="timeline-dot"></div>
                                                    </div>
                                                    <div class="timeline-right">
                                                        <div class="timeline-content">
                                                            <h5>{{ $education['institution'] ?? '' }}</h5>
                                                            <p>{!! $education['degree'] ?? '' !!}</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    @else
                                        <div class="alert alert-light">
                                            {{ app()->getLocale() == 'id' ? 'Tidak ada riwayat pendidikan yang tersedia.' : 'No education history available.' }}
                                        </div>
                                    @endif
                                </div>

                                <!-- Experience Tab -->
                                <div class="tab-pane fade" id="experience" role="tabpanel" aria-labelledby="experience-tab">
                                    @php
                                        $workExperience = json_decode(app()->getLocale() == 'id' ? $leader->work_experience : $leader->work_experience_en, true) ?? [];
                                        $workExperienceId = json_decode($leader->work_experience, true) ?? [];

                                        // If English version is empty but Indonesian version exists, use Indonesian data
                                        if (app()->getLocale() == 'en' && (empty($workExperience) || count($workExperience) == 0) && count($workExperienceId) > 0) {
                                            $workExperience = $workExperienceId;
                                        } else if (app()->getLocale() == 'en' && count($workExperience) > 0 && count($workExperienceId) > 0) {
                                            // For each item in English version, fill in missing fields from Indonesian version
                                            foreach ($workExperience as $key => $item) {
                                                if (isset($workExperienceId[$key])) {
                                                    // For fields that should be the same in both languages
                                                    if (!isset($item['period']) || empty($item['period'])) {
                                                        $workExperience[$key]['period'] = $workExperienceId[$key]['period'] ?? '';
                                                    }
                                                    if (!isset($item['location']) || empty($item['location'])) {
                                                        $workExperience[$key]['location'] = $workExperienceId[$key]['location'] ?? '';
                                                    }
                                                    if (!isset($item['description']) || empty($item['description'])) {
                                                        $workExperience[$key]['description'] = $workExperienceId[$key]['description'] ?? '';
                                                    }
                                                }
                                            }
                                        }
                                    @endphp

                                    @if(count($workExperience) > 0)
                                        <div class="timeline-modern">
                                            @foreach($workExperience as $experience)
                                                <div class="timeline-item fade-in-up" style="transition-delay: {{ $loop->iteration * 0.1 }}s">
                                                    <div class="timeline-left">
                                                        <div class="timeline-date">{{ $experience['period'] ?? '' }}</div>
                                                    </div>
                                                    <div class="timeline-center">
                                                        <div class="timeline-dot"></div>
                                                    </div>
                                                    <div class="timeline-right">
                                                        <div class="timeline-content">
                                                            <h5>{{ $experience['position'] ?? '' }}</h5>
                                                            <div class="company-location">
                                                                <span class="company">{{ $experience['company'] ?? '' }}</span>
                                                                @if(isset($experience['location']) && !empty($experience['location']))
                                                                    <span class="location"><i class="fas fa-map-marker-alt ms-2 me-1"></i>{{ $experience['location'] }}</span>
                                                                @endif
                                                            </div>
                                                            @if(isset($experience['description']) && !empty($experience['description']))
                                                                <div class="mt-2">{!! $experience['description'] !!}</div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    @else
                                        <div class="alert alert-light">
                                            {{ app()->getLocale() == 'id' ? 'Tidak ada pengalaman kerja yang tersedia.' : 'No work experience available.' }}
                                        </div>
                                    @endif
                                </div>

                                <!-- Achievements Tab -->
                                <div class="tab-pane fade" id="achievements" role="tabpanel" aria-labelledby="achievements-tab">
                                    @php
                                        $achievements = json_decode(app()->getLocale() == 'id' ? $leader->achievements : $leader->achievements_en, true) ?? [];
                                        $achievementsId = json_decode($leader->achievements, true) ?? [];

                                        // If English version is empty but Indonesian version exists, use Indonesian data
                                        if (app()->getLocale() == 'en' && (empty($achievements) || count($achievements) == 0) && count($achievementsId) > 0) {
                                            $achievements = $achievementsId;
                                        } else if (app()->getLocale() == 'en' && count($achievements) > 0 && count($achievementsId) > 0) {
                                            // For each item in English version, fill in missing fields from Indonesian version
                                            foreach ($achievements as $key => $item) {
                                                if (isset($achievementsId[$key])) {
                                                    // For fields that should be the same in both languages
                                                    if (!isset($item['year']) || empty($item['year'])) {
                                                        $achievements[$key]['year'] = $achievementsId[$key]['year'] ?? '';
                                                    }
                                                    if (!isset($item['description']) || empty($item['description'])) {
                                                        $achievements[$key]['description'] = $achievementsId[$key]['description'] ?? '';
                                                    }
                                                }
                                            }
                                        }
                                    @endphp

                                    @if(count($achievements) > 0)
                                        <div class="timeline-modern">
                                            @foreach($achievements as $achievement)
                                                <div class="timeline-item fade-in-up" style="transition-delay: {{ $loop->iteration * 0.1 }}s">
                                                    <div class="timeline-left">
                                                        <div class="timeline-date">{{ $achievement['year'] ?? '' }}</div>
                                                    </div>
                                                    <div class="timeline-center">
                                                        <div class="timeline-dot"></div>
                                                    </div>
                                                    <div class="timeline-right">
                                                        <div class="timeline-content">
                                                            <h5>{{ $achievement['title'] ?? '' }}</h5>
                                                            <div class="mt-2">{!! $achievement['description'] ?? '' !!}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    @else
                                        <div class="alert alert-light">
                                            {{ app()->getLocale() == 'id' ? 'Tidak ada prestasi yang tersedia.' : 'No achievements available.' }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Other Leaders Section -->
                    <div class="card border-0 shadow-sm mb-4 overflow-hidden">
                        <div class="card-header bg-white py-3">
                            <h4 class="mb-0">{{ app()->getLocale() == 'id' ? 'Pemimpin Lainnya' : 'Other Leaders' }}</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @php
                                    $otherLeaders = \App\Models\Leader::active()
                                        ->where('id', '!=', $leader->id)
                                        ->orderBy('order', 'asc')
                                        ->limit(3)
                                        ->get();
                                @endphp

                                @forelse($otherLeaders as $otherLeader)
                                    <div class="col-md-4 mb-3 mb-md-0">
                                        <div class="other-leader-card">
                                            <a href="{{ route('leaders.show', $otherLeader->id) }}" class="text-decoration-none">
                                                <div class="other-leader-image">
                                                    @php
                                                        $name = app()->getLocale() == 'id' ? $otherLeader->name : $otherLeader->name_en;
                                                        // If English name is empty, use Indonesian name
                                                        if (app()->getLocale() == 'en' && empty($name) && !empty($otherLeader->name)) {
                                                            $name = $otherLeader->name;
                                                        }
                                                    @endphp
                                                    @if($otherLeader->image)
                                                        <img src="{{ asset('storage/' . $otherLeader->image) }}" alt="{{ $name }}" class="img-fluid rounded">
                                                    @else
                                                        <img src="{{ asset('images/leader-placeholder.jpg') }}" alt="{{ $name }}" class="img-fluid rounded">
                                                    @endif
                                                </div>
                                                <div class="other-leader-info mt-2">
                                                    @php
                                                        $name = app()->getLocale() == 'id' ? $otherLeader->name : $otherLeader->name_en;
                                                        $position = app()->getLocale() == 'id' ? $otherLeader->position : $otherLeader->position_en;

                                                        // If English name is empty, use Indonesian name
                                                        if (app()->getLocale() == 'en' && empty($name) && !empty($otherLeader->name)) {
                                                            $name = $otherLeader->name;
                                                        }

                                                        // If English position is empty, use Indonesian position
                                                        if (app()->getLocale() == 'en' && empty($position) && !empty($otherLeader->position)) {
                                                            $position = $otherLeader->position;
                                                        }
                                                    @endphp
                                                    <h6 class="mb-0">{{ $name }}</h6>
                                                    <p class="text-muted small mb-0">{{ $position }}</p>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                @empty
                                    <div class="col-12">
                                        <div class="alert alert-light">
                                            {{ app()->getLocale() == 'id' ? 'Tidak ada pemimpin lain yang tersedia.' : 'No other leaders available.' }}
                                        </div>
                                    </div>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
    /* Leader Profile Styles */
    .leader-profile-image {
        position: relative;
        overflow: hidden;
    }

    .leader-profile-social {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
        padding: 20px;
        display: flex;
        justify-content: center;
    }

    .social-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        color: #fff;
        margin: 0 5px;
        transition: all 0.3s ease;
    }

    .social-icon.facebook {
        background-color: #3b5998;
    }

    .social-icon.twitter {
        background-color: #1da1f2;
    }

    .social-icon.instagram {
        background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
    }

    .social-icon.linkedin {
        background-color: #0077b5;
    }

    .social-icon:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 10px rgba(0,0,0,0.2);
        color: #fff;
    }

    .contact-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #f8f9fa;
        color: #198754;
    }

    /* Timeline Modern Styles */
    .timeline-modern {
        position: relative;
    }

    .timeline-item {
        display: flex;
        margin-bottom: 30px;
    }

    .timeline-left {
        width: 120px;
        padding-right: 20px;
        text-align: right;
    }

    .timeline-date {
        font-weight: 600;
        color: #198754;
    }

    .timeline-center {
        position: relative;
        width: 20px;
    }

    .timeline-center:before {
        content: '';
        position: absolute;
        top: 0;
        bottom: -30px;
        left: 50%;
        width: 2px;
        background-color: #e9ecef;
        transform: translateX(-50%);
    }

    .timeline-item:last-child .timeline-center:before {
        bottom: 0;
    }

    .timeline-dot {
        position: relative;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #198754;
        top: 5px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1;
    }

    .timeline-right {
        flex: 1;
        padding-left: 20px;
    }

    .timeline-content {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 15px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }

    .timeline-content h5 {
        margin-bottom: 5px;
        color: #333;
    }

    .company-location {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        color: #6c757d;
        font-size: 0.9rem;
    }

    /* Other Leaders Styles */
    .other-leader-card {
        transition: all 0.3s ease;
    }

    .other-leader-card:hover {
        transform: translateY(-5px);
    }

    .other-leader-image img {
        width: 100%;
        height: 120px;
        object-fit: cover;
        transition: all 0.3s ease;
    }

    .other-leader-card:hover .other-leader-image img {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    /* Animation Classes */
    .fade-in-up {
        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.5s ease, transform 0.5s ease;
    }

    .fade-in-up.active {
        opacity: 1;
        transform: translateY(0);
    }

    /* Responsive Adjustments */
    @media (max-width: 991.98px) {
        .sticky-lg-top {
            position: relative !important;
            top: 0 !important;
        }
    }

    @media (max-width: 767.98px) {
        .timeline-left {
            width: 80px;
            padding-right: 10px;
        }

        .timeline-right {
            padding-left: 10px;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize animations for fade-in-up elements
        const fadeElements = document.querySelectorAll('.fade-in-up');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('active');
                }
            });
        }, {
            threshold: 0.1
        });

        fadeElements.forEach(element => {
            observer.observe(element);
        });

        // Tab change animations
        const tabLinks = document.querySelectorAll('.nav-link[data-bs-toggle="tab"]');

        tabLinks.forEach(link => {
            link.addEventListener('click', function() {
                // Get the target tab content
                const targetId = this.getAttribute('data-bs-target');
                const targetPane = document.querySelector(targetId);

                // Reset animations
                const animElements = targetPane.querySelectorAll('.fade-in-up');
                animElements.forEach(element => {
                    element.classList.remove('active');
                });

                // Add a slight delay to allow the Bootstrap tab transition to complete
                setTimeout(() => {
                    // Trigger animations for elements inside the active tab
                    animElements.forEach((element, index) => {
                        setTimeout(() => {
                            element.classList.add('active');
                        }, index * 100);
                    });
                }, 150);
            });
        });
    });
</script>
@endpush
