<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Webhook extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'url',
        'event',
        'secret',
        'api_key',
        'is_active',
        'headers',
        'payload_template',
        'retry_count',
        'last_triggered_at',
        'last_failed_at',
        'last_error',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'headers' => 'array',
        'payload_template' => 'array',
        'retry_count' => 'integer',
        'last_triggered_at' => 'datetime',
        'last_failed_at' => 'datetime',
    ];

    /**
     * Scope a query to only include active webhooks.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include webhooks for a specific event.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $event
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForEvent($query, $event)
    {
        return $query->where('event', $event);
    }

    /**
     * Mark the webhook as triggered.
     *
     * @return bool
     */
    public function markAsTriggered()
    {
        return $this->update([
            'last_triggered_at' => now(),
        ]);
    }

    /**
     * Mark the webhook as failed.
     *
     * @param  string  $error
     * @return bool
     */
    public function markAsFailed($error)
    {
        return $this->update([
            'last_failed_at' => now(),
            'retry_count' => $this->retry_count + 1,
            'last_error' => $error,
        ]);
    }

    /**
     * Reset the retry count.
     *
     * @return bool
     */
    public function resetRetryCount()
    {
        return $this->update([
            'retry_count' => 0,
            'last_error' => null,
        ]);
    }

    /**
     * Generate a signature for the payload.
     *
     * @param  array  $payload
     * @return string|null
     */
    public function generateSignature($payload)
    {
        if (empty($this->secret)) {
            return null;
        }

        return hash_hmac('sha256', json_encode($payload), $this->secret);
    }

    /**
     * Generate a new API key for the webhook.
     *
     * @return string
     */
    public function generateApiKey()
    {
        $apiKey = \Illuminate\Support\Str::random(32);
        $this->update(['api_key' => $apiKey]);
        return $apiKey;
    }
}
