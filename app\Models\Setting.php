<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    protected $fillable = [
        'key',
        'value',
        'group',
        'is_translatable',
    ];

    protected $casts = [
        'is_translatable' => 'boolean',
    ];

    public function scopeGroup($query, $group)
    {
        return $query->where('group', $group);
    }

    public static function getValue($key, $default = null)
    {
        $setting = self::where('key', $key)->first();

        return $setting ? $setting->value : $default;
    }

    public static function setValue($key, $value, $group = 'general', $isTranslatable = false)
    {
        return self::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'group' => $group,
                'is_translatable' => $isTranslatable,
            ]
        );
    }
}
