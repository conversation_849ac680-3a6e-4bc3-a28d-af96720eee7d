<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Menu;
use App\Models\MenuItem;

class SimpleMenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create main menu
        $menu = Menu::create([
            'name' => 'Main Menu',
            'location' => 'main',
            'is_active' => true,
        ]);

        // Create a few basic menu items
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Beranda',
            'title_en' => 'Home',
            'route_name' => 'home',
            'order' => 1,
            'is_active' => true,
        ]);
        
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Profil',
            'title_en' => 'Profile',
            'url' => '#',
            'order' => 2,
            'is_active' => true,
        ]);
        
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Kontak',
            'title_en' => 'Contact',
            'route_name' => 'contact',
            'order' => 3,
            'is_active' => true,
        ]);
    }
}
