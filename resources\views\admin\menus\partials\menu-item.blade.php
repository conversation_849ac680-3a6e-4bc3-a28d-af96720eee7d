<li class="list-group-item menu-item d-flex justify-content-between align-items-center" data-id="{{ $item->id }}">
    <div class="d-flex align-items-center flex-grow-1">
        <i class="fas fa-grip-vertical me-2 text-muted"></i>
        <div>
            <span class="menu-title">{{ $item->title }}</span>
            @if($item->title_en)
            <span class="text-muted ms-2">({{ $item->title_en }})</span>
            @endif
            <small class="d-block text-muted">{{ $item->url ?: '#' }}</small>

            @if($item->parent_id)
                <span class="badge bg-secondary me-1">Submenu</span>
            @else
                <span class="badge bg-primary me-1">Main Menu</span>
            @endif

            @if($item->children->count() > 0)
                <span class="badge bg-info">Has {{ $item->children->count() }} children</span>
            @endif
        </div>
    </div>
    <div class="menu-actions">
        <a href="{{ route('admin.website.menu.edit', $item->id) }}" class="btn btn-sm btn-primary me-1">
            <i class="fas fa-edit"></i>
        </a>
        <form action="{{ route('admin.website.menu.destroy', $item->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this menu item? All child items will also be deleted.')">
            @csrf
            @method('DELETE')
            <button type="submit" class="btn btn-sm btn-danger">
                <i class="fas fa-trash"></i>
            </button>
        </form>
    </div>
</li>
@if($item->children->count() > 0)
<li class="list-group-item p-0 border-0 submenu-container">
    <ul class="list-group submenu-items">
        @foreach($item->children as $child)
            @include('admin.menus.partials.menu-item', ['item' => $child])
        @endforeach
    </ul>
</li>
@endif
