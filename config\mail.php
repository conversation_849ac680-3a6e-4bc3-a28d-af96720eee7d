<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Mailer
    |--------------------------------------------------------------------------
    |
    | This option controls the default mailer that is used to send all email
    | messages unless another mailer is explicitly specified when sending
    | the message. All additional mailers can be configured within the
    | "mailers" array. Examples of each type of mailer are provided.
    |
    */

    'default' => env('MAIL_MAILER', 'default'),

    /*
    |--------------------------------------------------------------------------
    | Mailer Configurations
    |--------------------------------------------------------------------------
    |
    | Here you may configure all of the mailers used by your application plus
    | their respective settings. Several examples have been configured for
    | you and you are free to add your own as your application requires.
    |
    | Laravel supports a variety of mail "transport" drivers that can be used
    | when delivering an email. You may specify which one you're using for
    | your mailers below. You may also add additional mailers if needed.
    |
    | Supported: "smtp", "sendmail", "mailgun", "ses", "ses-v2",
    |            "postmark", "resend", "log", "array",
    |            "failover", "roundrobin"
    |
    */

    'mailers' => [

        // Default mailer for general emails
        'default' => [
            'transport' => 'smtp',
            'scheme' => env('MAIL_SCHEME', 'smtps'),
            'encryption' => env('MAIL_ENCRYPTION', 'ssl'), // Added explicit encryption setting
            'url' => env('MAIL_URL'),
            'host' => env('MAIL_HOST', 'mail.nurulhayah.com'),
            'port' => env('MAIL_PORT', 465),
            'username' => env('MAIL_USERNAME', '<EMAIL>'),
            'password' => env('MAIL_PASSWORD'),
            'timeout' => env('MAIL_TIMEOUT', 30), // Added timeout setting
            'local_domain' => env('MAIL_EHLO_DOMAIN', parse_url(env('APP_URL', 'http://localhost'), PHP_URL_HOST)),
        ],

        // Password reset mailer
        'password_reset' => [
            'transport' => 'smtp',
            'scheme' => env('MAIL_PASSWORD_RESET_SCHEME', env('MAIL_SCHEME', 'smtps')),
            'url' => env('MAIL_PASSWORD_RESET_URL', env('MAIL_URL')),
            'host' => env('MAIL_PASSWORD_RESET_HOST', env('MAIL_HOST', 'mail.nurulhayah.com')),
            'port' => env('MAIL_PASSWORD_RESET_PORT', env('MAIL_PORT', 465)),
            'username' => env('MAIL_PASSWORD_RESET_USERNAME', '<EMAIL>'),
            'password' => env('MAIL_PASSWORD_RESET_PASSWORD', env('MAIL_PASSWORD')),
            'timeout' => null,
            'local_domain' => env('MAIL_EHLO_DOMAIN', parse_url(env('APP_URL', 'http://localhost'), PHP_URL_HOST)),
        ],

        // Notifications mailer
        'notifications' => [
            'transport' => 'smtp',
            'scheme' => env('MAIL_NOTIFICATIONS_SCHEME', env('MAIL_SCHEME', 'smtps')),
            'encryption' => env('MAIL_NOTIFICATIONS_ENCRYPTION', env('MAIL_ENCRYPTION', 'ssl')),
            'url' => env('MAIL_NOTIFICATIONS_URL', env('MAIL_URL')),
            'host' => env('MAIL_NOTIFICATIONS_HOST', env('MAIL_HOST', 'mail.nurulhayah.com')),
            'port' => env('MAIL_NOTIFICATIONS_PORT', env('MAIL_PORT', 465)),
            'username' => env('MAIL_NOTIFICATIONS_USERNAME', env('MAIL_USERNAME', '<EMAIL>')),
            'password' => env('MAIL_NOTIFICATIONS_PASSWORD', env('MAIL_PASSWORD')),
            'timeout' => env('MAIL_NOTIFICATIONS_TIMEOUT', env('MAIL_TIMEOUT', 30)),
            'local_domain' => env('MAIL_EHLO_DOMAIN', parse_url(env('APP_URL', 'http://localhost'), PHP_URL_HOST)),
        ],

        // Confirmations mailer
        'confirmations' => [
            'transport' => 'smtp',
            'scheme' => env('MAIL_CONFIRMATIONS_SCHEME', env('MAIL_SCHEME', 'smtps')),
            'url' => env('MAIL_CONFIRMATIONS_URL', env('MAIL_URL')),
            'host' => env('MAIL_CONFIRMATIONS_HOST', env('MAIL_HOST', 'mail.nurulhayah.com')),
            'port' => env('MAIL_CONFIRMATIONS_PORT', env('MAIL_PORT', 465)),
            'username' => env('MAIL_CONFIRMATIONS_USERNAME', env('MAIL_USERNAME', '<EMAIL>')),
            'password' => env('MAIL_CONFIRMATIONS_PASSWORD', env('MAIL_PASSWORD')),
            'timeout' => null,
            'local_domain' => env('MAIL_EHLO_DOMAIN', parse_url(env('APP_URL', 'http://localhost'), PHP_URL_HOST)),
        ],

        // Transactional mailer
        'transactional' => [
            'transport' => 'smtp',
            'scheme' => env('MAIL_TRANSACTIONAL_SCHEME', env('MAIL_SCHEME', 'smtps')),
            'url' => env('MAIL_TRANSACTIONAL_URL', env('MAIL_URL')),
            'host' => env('MAIL_TRANSACTIONAL_HOST', env('MAIL_HOST', 'mail.nurulhayah.com')),
            'port' => env('MAIL_TRANSACTIONAL_PORT', env('MAIL_PORT', 465)),
            'username' => env('MAIL_TRANSACTIONAL_USERNAME', env('MAIL_USERNAME', '<EMAIL>')),
            'password' => env('MAIL_TRANSACTIONAL_PASSWORD', env('MAIL_PASSWORD')),
            'timeout' => null,
            'local_domain' => env('MAIL_EHLO_DOMAIN', parse_url(env('APP_URL', 'http://localhost'), PHP_URL_HOST)),
        ],

        // Legacy smtp mailer (for backward compatibility)
        'smtp' => [
            'transport' => 'smtp',
            'scheme' => env('MAIL_SCHEME', 'smtps'),
            'url' => env('MAIL_URL'),
            'host' => env('MAIL_HOST', 'mail.nurulhayah.com'),
            'port' => env('MAIL_PORT', 465),
            'username' => env('MAIL_USERNAME', '<EMAIL>'),
            'password' => env('MAIL_PASSWORD'),
            'timeout' => null,
            'local_domain' => env('MAIL_EHLO_DOMAIN', parse_url(env('APP_URL', 'http://localhost'), PHP_URL_HOST)),
        ],

        'ses' => [
            'transport' => 'ses',
        ],

        'postmark' => [
            'transport' => 'postmark',
            // 'message_stream_id' => env('POSTMARK_MESSAGE_STREAM_ID'),
            // 'client' => [
            //     'timeout' => 5,
            // ],
        ],

        'resend' => [
            'transport' => 'resend',
        ],

        'sendmail' => [
            'transport' => 'sendmail',
            'path' => env('MAIL_SENDMAIL_PATH', '/usr/sbin/sendmail -bs -i'),
        ],

        'log' => [
            'transport' => 'log',
            'channel' => env('MAIL_LOG_CHANNEL'),
        ],

        'array' => [
            'transport' => 'array',
        ],

        'failover' => [
            'transport' => 'failover',
            'mailers' => [
                'smtp',
                'log',
            ],
            'retry_after' => 60,
        ],

        'roundrobin' => [
            'transport' => 'roundrobin',
            'mailers' => [
                'ses',
                'postmark',
            ],
            'retry_after' => 60,
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | "From" Address Configuration
    |--------------------------------------------------------------------------
    |
    | You may wish for all emails sent by your application to be sent from
    | specific addresses based on the type of email. Here you may specify
    | the default and type-specific "from" addresses.
    |
    */

    'from' => [
        'address' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
        'name' => env('MAIL_FROM_NAME', 'Informasi NURUL HAYAH 4'),
    ],

    'from_addresses' => [
        'default' => [
            'address' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
            'name' => env('MAIL_FROM_NAME', 'Informasi NURUL HAYAH 4'),
        ],
        'password_reset' => [
            'address' => env('MAIL_PASSWORD_RESET_FROM_ADDRESS', '<EMAIL>'),
            'name' => env('MAIL_PASSWORD_RESET_FROM_NAME', 'ADMINISTRATOR'),
        ],
        'notifications' => [
            'address' => env('MAIL_NOTIFICATIONS_FROM_ADDRESS', env('MAIL_FROM_ADDRESS', '<EMAIL>')),
            'name' => env('MAIL_NOTIFICATIONS_FROM_NAME', env('MAIL_FROM_NAME', 'ADMINISTRATOR')),
        ],
        'confirmations' => [
            'address' => env('MAIL_CONFIRMATIONS_FROM_ADDRESS', env('MAIL_FROM_ADDRESS', '<EMAIL>')),
            'name' => env('MAIL_CONFIRMATIONS_FROM_NAME', env('MAIL_FROM_NAME', 'ADMINISTRATOR')),
        ],
        'transactional' => [
            'address' => env('MAIL_TRANSACTIONAL_FROM_ADDRESS', env('MAIL_FROM_ADDRESS', '<EMAIL>')),
            'name' => env('MAIL_TRANSACTIONAL_FROM_NAME', env('MAIL_FROM_NAME', 'ADMINISTRATOR')),
        ],
    ],

];
