<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Partnership;

class SamplePartnershipSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing partnerships
        Partnership::truncate();

        // Create sample partnerships
        Partnership::create([
            'name' => 'Universitas Indonesia',
            'name_en' => 'University of Indonesia',
            'description' => 'Kerjasama dalam bidang pendidikan dan penelitian dengan Universitas Indonesia.',
            'description_en' => 'Collaboration in education and research with the University of Indonesia.',
            'website' => 'https://www.ui.ac.id',
            'partnership_since' => now()->subYears(2),
            'order' => 1,
            'is_active' => true
        ]);

        Partnership::create([
            'name' => 'Institut Teknologi Bandung',
            'name_en' => 'Bandung Institute of Technology',
            'description' => 'Kerjasama dalam bidang teknologi dan inovasi dengan Institut Teknologi Bandung.',
            'description_en' => 'Collaboration in technology and innovation with the Bandung Institute of Technology.',
            'website' => 'https://www.itb.ac.id',
            'partnership_since' => now()->subYears(1),
            'order' => 2,
            'is_active' => true
        ]);

        Partnership::create([
            'name' => 'Universitas Gadjah Mada',
            'name_en' => 'Gadjah Mada University',
            'description' => 'Kerjasama dalam bidang pendidikan dan pengembangan kurikulum dengan Universitas Gadjah Mada.',
            'description_en' => 'Collaboration in education and curriculum development with Gadjah Mada University.',
            'website' => 'https://www.ugm.ac.id',
            'partnership_since' => now()->subMonths(8),
            'order' => 3,
            'is_active' => true
        ]);

        Partnership::create([
            'name' => 'Universitas Airlangga',
            'name_en' => 'Airlangga University',
            'description' => 'Kerjasama dalam bidang kesehatan dan pendidikan dengan Universitas Airlangga.',
            'description_en' => 'Collaboration in health and education with Airlangga University.',
            'website' => 'https://www.unair.ac.id',
            'partnership_since' => now()->subMonths(6),
            'order' => 4,
            'is_active' => true
        ]);

        Partnership::create([
            'name' => 'Kementerian Pendidikan dan Kebudayaan',
            'name_en' => 'Ministry of Education and Culture',
            'description' => 'Kerjasama dalam bidang pendidikan dan kebudayaan dengan Kementerian Pendidikan dan Kebudayaan Republik Indonesia.',
            'description_en' => 'Collaboration in education and culture with the Ministry of Education and Culture of the Republic of Indonesia.',
            'website' => 'https://www.kemdikbud.go.id',
            'partnership_since' => now()->subYears(3),
            'order' => 5,
            'is_active' => true
        ]);
    }
}
