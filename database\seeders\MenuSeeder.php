<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Menu;
use App\Models\MenuItem;

class MenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create main menu
        $menu = Menu::create([
            'name' => 'Main Menu',
            'location' => 'main',
            'is_active' => true,
        ]);

        // Create menu items
        
        // Home
        $home = MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Beranda',
            'title_en' => 'Home',
            'route_name' => 'home',
            'order' => 1,
            'is_active' => true,
        ]);
        
        // NUHA (Profile)
        $nuha = MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'NUHA',
            'title_en' => 'NUHA',
            'url' => '#',
            'order' => 2,
            'is_active' => true,
        ]);
        
        // Visi & Misi
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Visi & Misi',
            'title_en' => 'Vision & Mission',
            'route_name' => 'profile.vision-mission',
            'parent_id' => $nuha->id,
            'order' => 1,
            'is_active' => true,
        ]);
        
        // Sejarah
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Sejarah',
            'title_en' => 'History',
            'route_name' => 'profile.history',
            'parent_id' => $nuha->id,
            'order' => 2,
            'is_active' => true,
        ]);
        
        // Pimpinan
        $pimpinan = MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Pimpinan',
            'title_en' => 'Leaders',
            'url' => '#',
            'parent_id' => $nuha->id,
            'order' => 3,
            'is_active' => true,
        ]);
        
        // Pengasuh
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Pengasuh',
            'title_en' => 'Caregivers',
            'url' => '/leaders/pengasuh',
            'parent_id' => $pimpinan->id,
            'order' => 1,
            'is_active' => true,
        ]);
        
        // Direktur
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Direktur',
            'title_en' => 'Director',
            'url' => '/leaders/direktur',
            'parent_id' => $pimpinan->id,
            'order' => 2,
            'is_active' => true,
        ]);
        
        // Prestasi
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Prestasi',
            'title_en' => 'Achievements',
            'url' => '/achievements',
            'order' => 3,
            'is_active' => true,
        ]);
        
        // Akademik
        $akademik = MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Akademik',
            'title_en' => 'Academic',
            'url' => '#',
            'order' => 4,
            'is_active' => true,
        ]);
        
        // Kalender Pendidikan
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Kalender Pendidikan',
            'title_en' => 'Academic Calendar',
            'url' => '/academic-calendar',
            'parent_id' => $akademik->id,
            'order' => 1,
            'is_active' => true,
        ]);
        
        // Jadwal Kegiatan
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Jadwal Kegiatan',
            'title_en' => 'Activity Schedule',
            'url' => '/activity-schedule',
            'parent_id' => $akademik->id,
            'order' => 2,
            'is_active' => true,
        ]);
        
        // Unit Pendidikan
        $unitPendidikan = MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Unit Pendidikan',
            'title_en' => 'Education Units',
            'url' => '#',
            'order' => 5,
            'is_active' => true,
        ]);
        
        // Formal
        $formal = MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Formal',
            'title_en' => 'Formal',
            'url' => '#',
            'parent_id' => $unitPendidikan->id,
            'order' => 1,
            'is_active' => true,
        ]);
        
        // SMP
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'SMP',
            'title_en' => 'Junior High School',
            'url' => '/education-units/smp',
            'parent_id' => $formal->id,
            'order' => 1,
            'is_active' => true,
        ]);
        
        // SMA
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'SMA',
            'title_en' => 'Senior High School',
            'url' => '/education-units/sma',
            'parent_id' => $formal->id,
            'order' => 2,
            'is_active' => true,
        ]);
        
        // Non-Formal
        $nonFormal = MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Non-Formal',
            'title_en' => 'Non-Formal',
            'url' => '#',
            'parent_id' => $unitPendidikan->id,
            'order' => 2,
            'is_active' => true,
        ]);
        
        // TPQ
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'TPQ',
            'title_en' => 'TPQ',
            'url' => '/education-units/tpq',
            'parent_id' => $nonFormal->id,
            'order' => 1,
            'is_active' => true,
        ]);
        
        // MADIN
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'MADIN',
            'title_en' => 'MADIN',
            'url' => '/education-units/madin',
            'parent_id' => $nonFormal->id,
            'order' => 2,
            'is_active' => true,
        ]);
        
        // Media
        $media = MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Media',
            'title_en' => 'Media',
            'url' => '#',
            'order' => 6,
            'is_active' => true,
        ]);
        
        // Berita
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Berita',
            'title_en' => 'News',
            'route_name' => 'news',
            'parent_id' => $media->id,
            'order' => 1,
            'is_active' => true,
        ]);
        
        // Galeri
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Galeri',
            'title_en' => 'Gallery',
            'route_name' => 'gallery',
            'parent_id' => $media->id,
            'order' => 2,
            'is_active' => true,
        ]);
        
        // Video
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Video',
            'title_en' => 'Video',
            'url' => '/videos',
            'parent_id' => $media->id,
            'order' => 3,
            'is_active' => true,
        ]);
        
        // Konsultasi
        $konsultasi = MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Konsultasi',
            'title_en' => 'Consultation',
            'url' => '#',
            'order' => 7,
            'is_active' => true,
        ]);
        
        // Agama
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Agama',
            'title_en' => 'Religion',
            'url' => '/consultation/religion',
            'parent_id' => $konsultasi->id,
            'order' => 1,
            'is_active' => true,
        ]);
        
        // Informasi
        $informasi = MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Informasi',
            'title_en' => 'Information',
            'url' => '#',
            'order' => 8,
            'is_active' => true,
        ]);
        
        // PSB
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'PSB',
            'title_en' => 'New Student Registration',
            'route_name' => 'registration',
            'parent_id' => $informasi->id,
            'order' => 1,
            'is_active' => true,
        ]);
        
        // Q&A
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Q&A',
            'title_en' => 'Q&A',
            'url' => '/qa',
            'parent_id' => $informasi->id,
            'order' => 2,
            'is_active' => true,
        ]);
        
        // CHAT
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'CHAT',
            'title_en' => 'CHAT',
            'url' => '/chat',
            'parent_id' => $informasi->id,
            'order' => 3,
            'is_active' => true,
        ]);
        
        // Contact Us
        MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'CONTACT US',
            'title_en' => 'CONTACT US',
            'route_name' => 'contact',
            'order' => 9,
            'is_active' => true,
        ]);
    }
}
