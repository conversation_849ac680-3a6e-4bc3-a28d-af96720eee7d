@extends('layouts.app')

@section('title', app()->getLocale() == 'id' ? $page->title : ($page->title_en ?: $page->title))

@section('meta_description', $page->meta_description ?: (app()->getLocale() == 'id' ? strip_tags(Str::limit($page->content, 160)) : strip_tags(Str::limit($page->content_en ?: $page->content, 160))))

@section('meta_title', $page->meta_title ?: (app()->getLocale() == 'id' ? $page->title : ($page->title_en ?: $page->title)))

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? $page->title : ($page->title_en ?: $page->title) }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('pages') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Halaman' : 'Pages' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? $page->title : ($page->title_en ?: $page->title) }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Page Content -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    @if($page->image)
                        <div class="mb-4 text-center">
                            <img src="{{ asset('storage/' . $page->image) }}" class="img-fluid rounded shadow" alt="{{ app()->getLocale() == 'id' ? $page->title : ($page->title_en ?: $page->title) }}">
                        </div>
                    @endif

                    <div class="content-wrapper bg-white p-4 rounded shadow-sm">
                        {!! app()->getLocale() == 'id' ? $page->content : ($page->content_en ?: $page->content) !!}
                    </div>

                    <div class="mt-4 d-flex justify-content-between">
                        <a href="{{ route('pages') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>{{ app()->getLocale() == 'id' ? 'Kembali ke Halaman' : 'Back to Pages' }}
                        </a>
                        
                        <div class="social-share">
                            <span class="me-2">{{ app()->getLocale() == 'id' ? 'Bagikan:' : 'Share:' }}</span>
                            <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->url()) }}" target="_blank" class="btn btn-sm btn-outline-primary me-1">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->url()) }}&text={{ urlencode(app()->getLocale() == 'id' ? $page->title : ($page->title_en ?: $page->title)) }}" target="_blank" class="btn btn-sm btn-outline-info me-1">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://wa.me/?text={{ urlencode((app()->getLocale() == 'id' ? $page->title : ($page->title_en ?: $page->title)) . ' ' . request()->url()) }}" target="_blank" class="btn btn-sm btn-outline-success">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
    .content-wrapper {
        line-height: 1.8;
    }
    
    .content-wrapper img {
        max-width: 100%;
        height: auto;
        margin: 1rem 0;
    }
    
    .content-wrapper table {
        width: 100%;
        margin-bottom: 1rem;
        border-collapse: collapse;
    }
    
    .content-wrapper table, .content-wrapper th, .content-wrapper td {
        border: 1px solid #dee2e6;
    }
    
    .content-wrapper th, .content-wrapper td {
        padding: 0.75rem;
    }
    
    .social-share .btn {
        width: 36px;
        height: 36px;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
</style>
@endpush
