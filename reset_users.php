<?php

// Bootstrap the <PERSON><PERSON> application
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

try {
    // Disable foreign key checks
    DB::statement('SET FOREIGN_KEY_CHECKS=0');
    
    // Truncate the users table
    DB::table('users')->truncate();
    
    // Reset auto-increment to 1
    DB::statement('ALTER TABLE users AUTO_INCREMENT = 1');
    
    // Create a new admin user
    DB::table('users')->insert([
        'name' => 'Administrator',
        'username' => 'admin',
        'email' => '<EMAIL>',
        'password' => Hash::make('Salamullah'),
        'role' => 'admin',
        'email_verified_at' => now(),
        'created_at' => now(),
        'updated_at' => now(),
    ]);
    
    // Re-enable foreign key checks
    DB::statement('SET FOREIGN_KEY_CHECKS=1');
    
    echo "Users table has been reset successfully. A new admin user has been created.\n";
    echo "Username: admin\n";
    echo "Email: <EMAIL>\n";
    echo "Password: Salamullah\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
