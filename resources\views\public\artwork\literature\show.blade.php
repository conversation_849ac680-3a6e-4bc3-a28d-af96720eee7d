@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? $literature->title : $literature->title_en)
@section('meta_description', app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit(strip_tags($literature->content), 160) : \Illuminate\Support\Str::limit(strip_tags($literature->content_en), 160))

@section('content')
    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="page-title" data-aos="fade-up">{{ app()->getLocale() == 'id' ? $literature->title : $literature->title_en }}</h1>
                    <nav aria-label="breadcrumb" data-aos="fade-up" data-aos-delay="100">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('artwork') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Karya Seni' : 'Artwork' }}</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('artwork.literature') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Karya Sastra' : 'Literature' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? $literature->title : $literature->title_en }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Literature Detail -->
    <section class="section-padding">
        <div class="container">
            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8" data-aos="fade-up">
                    <div class="literature-detail bg-white p-4 rounded shadow-sm">
                        @if($literature->image)
                            <div class="literature-featured-image mb-4">
                                <img src="{{ asset('storage/' . $literature->image) }}" alt="{{ app()->getLocale() == 'id' ? $literature->title : $literature->title_en }}" class="img-fluid rounded">
                            </div>
                        @endif
                        
                        <h2 class="mb-3">{{ app()->getLocale() == 'id' ? $literature->title : $literature->title_en }}</h2>
                        
                        <div class="literature-meta mb-4">
                            <div class="d-flex flex-wrap">
                                <div class="me-4 mb-2">
                                    <i class="fas fa-user-alt text-success me-2"></i>
                                    <span>{{ app()->getLocale() == 'id' ? $literature->author : $literature->author_en }}</span>
                                </div>
                                
                                @if($literature->year)
                                <div class="me-4 mb-2">
                                    <i class="fas fa-calendar-alt text-success me-2"></i>
                                    <span>{{ $literature->year }}</span>
                                </div>
                                @endif
                                
                                @if($literature->litType)
                                <div class="mb-2">
                                    <i class="fas fa-tag text-success me-2"></i>
                                    <span>{{ app()->getLocale() == 'id' ? $literature->litType->name : $literature->litType->name_en }}</span>
                                </div>
                                @endif
                            </div>
                        </div>
                        
                        @if($literature->file)
                            <div class="literature-download mb-4">
                                <a href="{{ asset('storage/' . $literature->file) }}" class="btn btn-success" target="_blank">
                                    <i class="fas fa-download me-2"></i>
                                    {{ app()->getLocale() == 'id' ? 'Unduh Dokumen' : 'Download Document' }}
                                </a>
                            </div>
                        @endif
                        
                        <div class="literature-content mb-4">
                            <div class="content-wrapper">
                                {!! app()->getLocale() == 'id' ? $literature->content : $literature->content_en !!}
                            </div>
                        </div>
                        
                        <div class="literature-actions">
                            <a href="{{ route('artwork.literature') }}" class="btn btn-outline-success">
                                <i class="fas fa-arrow-left me-2"></i>
                                {{ app()->getLocale() == 'id' ? 'Kembali ke Karya Sastra' : 'Back to Literature' }}
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Sidebar -->
                <div class="col-lg-4 mt-4 mt-lg-0" data-aos="fade-up" data-aos-delay="100">
                    <!-- Author Info -->
                    <div class="sidebar-widget bg-white p-4 rounded shadow-sm mb-4">
                        <h5 class="widget-title">{{ app()->getLocale() == 'id' ? 'Tentang Penulis' : 'About the Author' }}</h5>
                        <div class="author-info">
                            <div class="d-flex align-items-center mb-3">
                                <div class="author-avatar me-3">
                                    <i class="fas fa-user-circle fa-3x text-success"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">{{ app()->getLocale() == 'id' ? $literature->author : $literature->author_en }}</h6>
                                    @if($literature->year)
                                        <small class="text-muted">{{ $literature->year }}</small>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Related Literature -->
                    @if($relatedLiterature->count() > 0)
                        <div class="sidebar-widget bg-white p-4 rounded shadow-sm">
                            <h5 class="widget-title">{{ app()->getLocale() == 'id' ? 'Karya Sastra Terkait' : 'Related Literature' }}</h5>
                            <div class="related-literature">
                                @foreach($relatedLiterature as $related)
                                    <div class="related-item mb-3 pb-3 {{ !$loop->last ? 'border-bottom' : '' }}">
                                        <h6 class="mb-1">
                                            <a href="{{ route('artwork.literature.show', $related->id) }}" class="text-decoration-none">
                                                {{ app()->getLocale() == 'id' ? $related->title : $related->title_en }}
                                            </a>
                                        </h6>
                                        <div class="small text-muted mb-2">
                                            <i class="fas fa-user-alt me-1"></i> {{ app()->getLocale() == 'id' ? $related->author : $related->author_en }}
                                            @if($related->year)
                                                <span class="mx-1">•</span>
                                                <i class="fas fa-calendar-alt me-1"></i> {{ $related->year }}
                                            @endif
                                        </div>
                                        <div class="small">
                                            {{ \Illuminate\Support\Str::limit(strip_tags(app()->getLocale() == 'id' ? $related->content : $related->content_en), 80) }}
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
    .page-header {
        background: linear-gradient(rgba(0, 0, 0, 0.65), rgba(0, 0, 0, 0.65)), url('{{ asset('images/header-bg.jpg') }}') center/cover no-repeat;
        padding: 80px 0;
        margin-bottom: 0;
    }

    .page-title {
        color: white;
        font-weight: 700;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .breadcrumb-item, .breadcrumb-item a {
        color: rgba(255, 255, 255, 0.8);
    }

    .breadcrumb-item.active {
        color: white;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        color: rgba(255, 255, 255, 0.6);
    }

    .section-padding {
        padding: 70px 0;
    }

    .literature-featured-image img {
        width: 100%;
        max-height: 400px;
        object-fit: cover;
    }

    .literature-meta {
        font-size: 0.95rem;
        color: #6c757d;
    }

    .literature-content {
        line-height: 1.8;
    }

    .literature-content img {
        max-width: 100%;
        height: auto;
    }

    .widget-title {
        position: relative;
        padding-bottom: 10px;
        margin-bottom: 20px;
        font-weight: 600;
    }

    .widget-title:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 50px;
        height: 2px;
        background-color: #198754;
    }

    .author-avatar {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .related-item:hover h6 {
        color: #198754;
    }
</style>
@endpush
