<?php

namespace App\Http\Controllers\Api;

use App\Models\Contact;
use App\Services\WebhookService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ContactController extends BaseApiController
{
    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\WebhookService  $webhookService
     * @return void
     */
    public function __construct(WebhookService $webhookService)
    {
        parent::__construct($webhookService);
        $this->model = new Contact();
        $this->modelName = 'contact';
        
        $this->storeRules = [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ];
        
        $this->updateRules = [
            'name' => 'string|max:255',
            'email' => 'email|max:255',
            'phone' => 'string|max:20',
            'subject' => 'string|max:255',
            'message' => 'string',
            'is_read' => 'boolean',
            'read_at' => 'nullable|date',
            'reply_message' => 'nullable|string',
            'replied_at' => 'nullable|date',
            'replied_by' => 'nullable|exists:users,id',
        ];
    }

    /**
     * Display a listing of the contacts.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = $this->model->query();

        // Apply filters
        if ($request->has('is_read')) {
            $query->where('is_read', $request->is_read);
        }

        if ($request->has('has_reply')) {
            if ($request->has_reply) {
                $query->whereNotNull('reply_message');
            } else {
                $query->whereNull('reply_message');
            }
        }

        // Apply search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('message', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        // Apply pagination
        $perPage = $request->input('per_page', 15);
        $contacts = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $contacts,
        ]);
    }

    /**
     * Mark a contact as read.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsRead($id)
    {
        $contact = $this->model->findOrFail($id);
        
        if ($contact->is_read) {
            return response()->json([
                'success' => false,
                'message' => 'Contact is already marked as read',
            ], 422);
        }
        
        $contact->markAsRead();
        
        // Dispatch webhook event
        $this->webhookService->dispatchEvent('contact.read', $contact->toArray());
        
        return response()->json([
            'success' => true,
            'message' => 'Contact marked as read successfully',
            'data' => $contact,
        ]);
    }
    
    /**
     * Reply to a contact.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function reply(Request $request, $id)
    {
        $contact = $this->model->findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'reply_message' => 'required|string',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }
        
        $contact->update([
            'reply_message' => $request->reply_message,
            'replied_at' => now(),
            'replied_by' => auth()->id(),
            'is_read' => true,
            'read_at' => now(),
        ]);
        
        // Dispatch webhook event
        $this->webhookService->dispatchEvent('contact.replied', $contact->toArray());
        
        return response()->json([
            'success' => true,
            'message' => 'Reply sent successfully',
            'data' => $contact,
        ]);
    }
}
