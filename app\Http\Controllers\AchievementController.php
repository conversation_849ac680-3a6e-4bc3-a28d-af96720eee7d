<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Achievement;

class AchievementController extends Controller
{
    /**
     * Display a listing of the achievements.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get featured achievements for the top section
        $featuredAchievements = Achievement::where('is_active', true)
            ->where('is_featured', true)
            ->orderBy('achievement_date', 'desc')
            ->take(3)
            ->get();

        // Get all active achievements, grouped by year
        $achievements = Achievement::where('is_active', true)
            ->orderBy('achievement_date', 'desc')
            ->get()
            ->groupBy(function($item) {
                return $item->achievement_date ? $item->achievement_date->format('Y') : 'Undated';
            });

        // Get latest news for sidebar
        $latestNews = \App\Models\News::published()
            ->orderBy('published_at', 'desc')
            ->take(3)
            ->get();

        // Get latest announcements for sidebar
        $latestAnnouncements = \App\Models\Announcement::where('is_active', true)
            ->orderBy('start_date', 'desc')
            ->take(3)
            ->get();

        return view('public.achievements.index', compact(
            'featuredAchievements',
            'achievements',
            'latestNews',
            'latestAnnouncements'
        ));
    }

    /**
     * Display the specified achievement.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $achievement = Achievement::where('is_active', true)
            ->findOrFail($id);

        // Get related achievements
        $relatedAchievements = Achievement::where('is_active', true)
            ->where('id', '!=', $achievement->id)
            ->orderBy('achievement_date', 'desc')
            ->take(3)
            ->get();

        // Get latest news
        $latestNews = \App\Models\News::published()
            ->orderBy('published_at', 'desc')
            ->take(3)
            ->get();

        // Get latest announcements
        $latestAnnouncements = \App\Models\Announcement::where('is_active', true)
            ->orderBy('start_date', 'desc')
            ->take(3)
            ->get();

        return view('public.achievements.show', compact(
            'achievement',
            'relatedAchievements',
            'latestNews',
            'latestAnnouncements'
        ));
    }
}
