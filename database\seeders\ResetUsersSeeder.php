<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class ResetUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Truncate the users table to remove all existing users
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('users')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        
        // Reset the auto-increment to 1
        DB::statement('ALTER TABLE users AUTO_INCREMENT = 1;');
        
        // Create a new admin user
        DB::table('users')->insert([
            'name' => 'Administrator',
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('Salamullah'),
            'role' => 'admin',
            'email_verified_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}
