<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\LitType;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class LitTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $types = LitType::latest()->paginate(10);
        return view('admin.artwork.types.index', compact('types'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.artwork.types.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:lit_type',
            'slug_en' => 'nullable|string|max:255|unique:lit_type',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
        ]);

        $data = $request->all();
        
        // Generate slugs if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['name']);
        }
        
        if (empty($data['slug_en'])) {
            $data['slug_en'] = Str::slug($data['name_en']);
        }
        
        // Set is_active to true by default
        $data['is_active'] = true;

        LitType::create($data);

        return redirect()->route('admin.artwork.types.index')
            ->with('success', 'Literature Type created successfully.');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $type = LitType::findOrFail($id);
        return view('admin.artwork.types.edit', compact('type'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $type = LitType::findOrFail($id);
        
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:lit_type,slug,' . $id,
            'slug_en' => 'nullable|string|max:255|unique:lit_type,slug_en,' . $id,
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();
        
        // Generate slugs if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['name']);
        }
        
        if (empty($data['slug_en'])) {
            $data['slug_en'] = Str::slug($data['name_en']);
        }
        
        // Set is_active based on checkbox
        $data['is_active'] = $request->has('is_active');

        $type->update($data);

        return redirect()->route('admin.artwork.types.index')
            ->with('success', 'Literature Type updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $type = LitType::findOrFail($id);
        
        // Check if the type is being used by any literature
        if ($type->literature()->count() > 0) {
            return redirect()->route('admin.artwork.types.index')
                ->with('error', 'Cannot delete this type because it is being used by literature.');
        }
        
        $type->delete();

        return redirect()->route('admin.artwork.types.index')
            ->with('success', 'Literature Type deleted successfully.');
    }
}
