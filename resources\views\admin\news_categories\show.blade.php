@extends('admin.layouts.app')

@section('title', 'View News Category')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View News Category</h1>
        <div>
            <a href="{{ route('admin.categories.edit', $category) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="{{ route('admin.categories.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Category Details</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 200px;">ID</th>
                            <td>{{ $category->id }}</td>
                        </tr>
                        <tr>
                            <th>Name (Indonesian)</th>
                            <td>{{ $category->name }}</td>
                        </tr>
                        <tr>
                            <th>Name (English)</th>
                            <td>{{ $category->name_en }}</td>
                        </tr>
                        <tr>
                            <th>Slug (Indonesian)</th>
                            <td>{{ $category->slug }}</td>
                        </tr>
                        <tr>
                            <th>Slug (English)</th>
                            <td>{{ $category->slug_en }}</td>
                        </tr>
                        <tr>
                            <th>Description (Indonesian)</th>
                            <td>{{ $category->description }}</td>
                        </tr>
                        <tr>
                            <th>Description (English)</th>
                            <td>{{ $category->description_en }}</td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td>
                                @if($category->is_active)
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-danger">Inactive</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Created At</th>
                            <td>{{ $category->created_at->format('d M Y H:i:s') }}</td>
                        </tr>
                        <tr>
                            <th>Updated At</th>
                            <td>{{ $category->updated_at->format('d M Y H:i:s') }}</td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">News in this Category</h5>
                </div>
                <div class="card-body">
                    @if($category->news->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Title</th>
                                        <th>Published Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($category->news as $news)
                                        <tr>
                                            <td>{{ $news->id }}</td>
                                            <td>{{ $news->title }}</td>
                                            <td>{{ $news->published_at->format('d M Y') }}</td>
                                            <td>
                                                <a href="{{ route('admin.news.show', $news) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted">No news articles in this category yet.</p>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <a href="{{ route('admin.categories.edit', $category) }}" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-edit me-1"></i> Edit Category
                    </a>
                    
                    <form action="{{ route('admin.categories.destroy', $category) }}" method="POST">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Are you sure you want to delete this category?')">
                            <i class="fas fa-trash me-1"></i> Delete Category
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
