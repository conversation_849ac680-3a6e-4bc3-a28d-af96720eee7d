<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class NewsController extends Controller
{
    /**
     * Display a listing of the news.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get all active categories
        $categories = \App\Models\NewsCategory::active()->orderBy('id')->get();

        // Get news for each category
        $newsByCategory = [];

        foreach ($categories as $category) {
            $newsByCategory[$category->id] = \App\Models\News::published()
                ->where('category_id', $category->id)
                ->paginate(9);
        }

        // If there are no categories, get all news
        if ($categories->isEmpty()) {
            $news = \App\Models\News::published()->paginate(9);
            return view('public.news.index', compact('news'));
        }

        return view('public.news.index', compact('categories', 'newsByCategory'));
    }

    /**
     * Display news by category.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function category($slug)
    {
        $locale = app()->getLocale();
        $slugField = $locale == 'en' ? 'slug_en' : 'slug';

        $category = \App\Models\NewsCategory::where($slugField, $slug)->firstOrFail();

        $news = \App\Models\News::published()
            ->where('category_id', $category->id)
            ->paginate(9);

        return view('public.news.category', compact('category', 'news'));
    }

    /**
     * Display the specified news.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {
        $locale = app()->getLocale();
        $slugField = $locale == 'en' ? 'slug_en' : 'slug';

        $news = \App\Models\News::published()
            ->where($slugField, $slug)
            ->firstOrFail();

        // Get related news
        $relatedNews = \App\Models\News::published()
            ->where('id', '!=', $news->id)
            ->take(3)
            ->get();

        // Get latest announcements
        $latestAnnouncements = \App\Models\Announcement::where('is_active', true)
            ->orderBy('start_date', 'desc')
            ->take(3)
            ->get();

        // Get latest agenda items
        $latestAgenda = \App\Models\Agenda::where('is_active', true)
            ->orderBy('date', 'asc')
            ->take(3)
            ->get();

        return view('public.news.show', compact(
            'news',
            'relatedNews',
            'latestAnnouncements',
            'latestAgenda'
        ));
    }
}
