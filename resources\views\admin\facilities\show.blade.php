@extends('admin.layouts.app')

@section('title', 'View Facility')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View Facility</h1>
        <div>
            <a href="{{ route('admin.facilities.edit', $facility) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="{{ route('admin.facilities.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Facility Details</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">ID:</div>
                        <div class="col-md-9">{{ $facility->id }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Icon:</div>
                        <div class="col-md-9">
                            @if($facility->icon)
                                <i class="{{ $facility->icon }} fa-2x me-2"></i> {{ $facility->icon }}
                            @else
                                <span class="text-muted">No icon available</span>
                            @endif
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Created At:</div>
                        <div class="col-md-9">{{ $facility->created_at->format('d M Y H:i:s') }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Updated At:</div>
                        <div class="col-md-9">{{ $facility->updated_at->format('d M Y H:i:s') }}</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Indonesian Content</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-3 fw-bold">Name:</div>
                                <div class="col-md-9">{{ $facility->name }}</div>
                            </div>

                            <div class="row">
                                <div class="col-md-3 fw-bold">Description:</div>
                                <div class="col-md-9">{!! $facility->description !!}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">English Content</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-3 fw-bold">Name:</div>
                                <div class="col-md-9">{{ $facility->name_en }}</div>
                            </div>

                            <div class="row">
                                <div class="col-md-3 fw-bold">Description:</div>
                                <div class="col-md-9">{!! $facility->description_en !!}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Image</h5>
                </div>
                <div class="card-body">
                    @if($facility->image)
                        <img src="{{ asset('storage/' . $facility->image) }}" alt="{{ $facility->name }}" class="img-fluid rounded">
                    @else
                        <div class="text-center py-5 bg-light rounded">
                            <i class="fas fa-image fa-3x text-muted"></i>
                            <p class="mt-3 text-muted">No image available</p>
                        </div>
                    @endif
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <a href="{{ route('facilities') }}" class="btn btn-info w-100 mb-2" target="_blank">
                        <i class="fas fa-external-link-alt me-1"></i> View on Website
                    </a>

                    <a href="{{ route('admin.facilities.edit', $facility) }}" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-edit me-1"></i> Edit Facility
                    </a>

                    <form action="{{ route('admin.facilities.destroy', $facility) }}" method="POST">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Are you sure you want to delete this facility?')">
                            <i class="fas fa-trash me-1"></i> Delete Facility
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
