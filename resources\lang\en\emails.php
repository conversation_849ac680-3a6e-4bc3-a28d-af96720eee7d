<?php

return [
    // General
    'auto_email_notice' => 'This email is sent automatically. Please do not reply to this email.',
    'regards' => 'Regards,',
    'islamic_greeting' => 'Assalamu\'alaikum Wr. Wb. (Peace be upon you)',
    'islamic_closing' => 'Wassalamu\'alaikum Wr. Wb. (Peace be upon you)',

    // Button
    'button_trouble' => 'If you\'re having trouble clicking the ":actionText" button, copy and paste the URL below into your web browser:',

    // Reset Password
    'reset_password_subject' => 'Password Reset Request',
    'reset_password_intro' => 'You are receiving this email because we received a password reset request for your account at NURUL HAYAH 4.',
    'reset_password_action' => 'Reset Password',
    'reset_password_expiry' => 'This password reset link will expire in :count minutes.',
    'reset_password_ignore' => 'If you did not request a password reset, no further action is required.',

    // Registration
    'registration_subject' => 'NURUL HAYAH 4 Registration Confirmation',
    'registration_thank_you' => 'Thank you for registering with NURUL HAYAH 4.',
    'registration_details' => 'Your registration details:',
    'registration_name' => 'Name',
    'registration_email' => 'Email',
    'registration_number' => 'Registration Number',
    'registration_save' => 'Please save this email as proof of your registration!',

    // Confirmation
    'confirmation_subject' => 'Account Confirmation',
    'confirmation_intro' => 'Thank you for registering. Please confirm your account by clicking the button below.',
    'confirmation_action' => 'Confirm Account',
    'confirmation_expiry' => 'This confirmation link will expire in :count hours.',

    // Notification
    'notification_subject' => 'Notification from NURUL HAYAH 4',
    'notification_intro' => 'We would like to inform you about the following important information:',

    // Contact Form
    'contact_subject' => 'Thank You for Contacting NURUL HAYAH 4',
    'contact_thank_you' => 'Thank you for contacting NURUL HAYAH 4.',
    'contact_received' => 'We have received your message and will respond to your inquiry as soon as possible.',
    'contact_details' => 'Your message details',
    'contact_subject' => 'Subject',
    'contact_date' => 'Date',
    'contact_response_time' => 'We strive to respond to all inquiries within 2-3 business days.',
    'contact_further_info' => 'For further information, please contact us at',

    // Contact Reply
    'contact_reply_subject' => 'Response to Your Inquiry - NURUL HAYAH 4',
    'contact_reply_intro' => 'Thank you for your message. Here is our response to your inquiry:',
    'contact_original_message' => 'Your original message:',
    'contact_reply_closing' => 'If you have any further questions, please feel free to contact us.',

    // New User Account
    'new_user_subject' => 'Your New Account at NURUL HAYAH 4',
    'new_user_intro' => 'An account has been created for you at NURUL HAYAH 4.',
    'new_user_details' => 'Your login details:',
    'new_user_username' => 'Username',
    'new_user_email' => 'Email',
    'new_user_password' => 'Password',
    'new_user_password_change' => 'Please login and change your password as soon as possible for security reasons.',
    'new_user_login' => 'Login Now',

    // Password Reset
    'password_reset_subject' => 'Password Reset Notification',
    'password_reset_intro' => 'Your password has been reset by an administrator.',
    'password_reset_details' => 'Your new login details:',
    'password_reset_name' => 'Name',
    'password_reset_username' => 'Username',
    'password_reset_email' => 'Email',
    'password_reset_password' => 'New Password',
    'password_reset_password_change' => 'Please login and change your password as soon as possible for security reasons.',
    'password_reset_login' => 'Login Now',
];
