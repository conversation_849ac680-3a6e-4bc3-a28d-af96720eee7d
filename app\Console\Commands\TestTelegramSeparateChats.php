<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\TelegramBotService;
use App\Models\Setting;

class TestTelegramSeparateChats extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:test-separate-chats';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test sending messages to separate admin personal chat and group chat';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Telegram separate chats configuration...');
        
        $telegramService = new TelegramBotService();
        $adminPersonalChatId = Setting::getValue('telegram_admin_chat_id');
        $groupChatId = Setting::getValue('telegram_chat_id');
        $topicId = (int)Setting::getValue('telegram_group_topic_id');
        
        // Check if the group chat ID is a group chat (starts with -)
        $isGroupChat = !empty($groupChatId) && strpos($groupChatId, '-') === 0;
        
        $this->info('Current Configuration:');
        $this->info('Admin Personal Chat ID: ' . ($adminPersonalChatId ?: 'Not configured'));
        $this->info('Group Chat ID: ' . ($groupChatId ?: 'Not configured'));
        $this->info('Is Group Chat: ' . ($isGroupChat ? 'Yes' : 'No'));
        $this->info('Topic ID: ' . ($topicId > 0 ? $topicId : 'Not configured'));
        
        $this->newLine();
        
        // Test sending to admin personal chat
        if (!empty($adminPersonalChatId)) {
            $this->info('Sending test message to Admin Personal Chat...');
            $message = 'This is a test message to Admin Personal Chat from the telegram:test-separate-chats command at ' . now()->format('Y-m-d H:i:s');
            
            try {
                $result = $telegramService->sendMessage($adminPersonalChatId, $message);
                
                if ($result && isset($result['ok']) && $result['ok']) {
                    $this->info('✅ Message sent to Admin Personal Chat successfully!');
                } else {
                    $this->error('❌ Failed to send message to Admin Personal Chat: ' . json_encode($result));
                }
            } catch (\Exception $e) {
                $this->error('❌ Error sending to Admin Personal Chat: ' . $e->getMessage());
            }
        } else {
            $this->warn('⚠️ Admin Personal Chat ID is not configured.');
        }
        
        $this->newLine();
        
        // Test sending to group chat
        if ($isGroupChat) {
            $this->info('Sending test message to Group Chat...');
            $message = 'This is a test message to Group Chat from the telegram:test-separate-chats command at ' . now()->format('Y-m-d H:i:s');
            
            try {
                if ($topicId > 0) {
                    $this->info('Using Topic ID: ' . $topicId);
                    $result = $telegramService->sendMessage($groupChatId, $message, [], $topicId);
                } else {
                    $result = $telegramService->sendMessage($groupChatId, $message);
                }
                
                if ($result && isset($result['ok']) && $result['ok']) {
                    $this->info('✅ Message sent to Group Chat successfully!');
                } else {
                    $this->error('❌ Failed to send message to Group Chat: ' . json_encode($result));
                }
            } catch (\Exception $e) {
                $this->error('❌ Error sending to Group Chat: ' . $e->getMessage());
            }
        } else if (!empty($groupChatId)) {
            $this->warn('⚠️ Group Chat ID is configured but does not appear to be a group chat ID (should start with -).');
        } else {
            $this->warn('⚠️ Group Chat ID is not configured.');
        }
        
        $this->newLine();
        
        // Test sending to both chats using sendAdminMessage
        $this->info('Testing sendAdminMessage to both chats...');
        $message = 'This is a test message using sendAdminMessage from the telegram:test-separate-chats command at ' . now()->format('Y-m-d H:i:s');
        
        try {
            $result = $telegramService->sendAdminMessage(
                $message, 
                [], 
                $topicId > 0, // Use topic ID if configured
                !empty($adminPersonalChatId), // Send to personal chat if configured
                $isGroupChat // Send to group chat if it's a group
            );
            
            if ($result) {
                $this->info('✅ Message sent using sendAdminMessage successfully!');
            } else {
                $this->error('❌ Failed to send message using sendAdminMessage.');
            }
        } catch (\Exception $e) {
            $this->error('❌ Error using sendAdminMessage: ' . $e->getMessage());
        }
        
        $this->newLine();
        $this->info('Test completed!');
        
        return 0;
    }
}
