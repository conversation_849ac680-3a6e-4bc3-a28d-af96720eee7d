@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? $news->title : $news->title_en)

@section('meta_description', app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit(html_entity_decode(strip_tags($news->content)), 160) : \Illuminate\Support\Str::limit(html_entity_decode(strip_tags($news->content_en)), 160))

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? $news->title : $news->title_en }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('news') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Berita' : 'News' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit($news->title, 30) : \Illuminate\Support\Str::limit($news->title_en, 30) }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- News Content -->
    <section class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 main-content">
                    <!-- News Detail -->
                    <div class="card border-0 shadow-sm mb-4 fade-in">
                        <div class="card-body p-4">
                            <div class="news-meta mb-3">
                                <span class="me-3"><i class="far fa-calendar-alt me-1"></i> {{ $news->published_at->format('d M Y') }}</span>
                                <span><i class="far fa-user me-1"></i> {{ $news->user->name }}</span>
                            </div>

                            <div class="news-image mb-4">
                                <img src="{{ \App\Helpers\ImageHelper::getImageUrl($news->image, 'news') }}" class="img-fluid rounded" alt="{{ app()->getLocale() == 'id' ? $news->title : $news->title_en }}">
                            </div>

                            <div class="news-content">
                                {!! app()->getLocale() == 'id' ? $news->content : $news->content_en !!}
                            </div>

                            <!-- Social Share -->
                            <div class="social-share mt-4 pt-4 border-top">
                                <h5>{{ app()->getLocale() == 'id' ? 'Bagikan' : 'Share' }}</h5>
                                <div class="d-flex">
                                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(route('news.show', app()->getLocale() == 'id' ? $news->slug : $news->slug_en)) }}" class="btn btn-sm btn-outline-primary me-2" target="_blank">
                                        <i class="fab fa-facebook-f me-1"></i> Facebook
                                    </a>
                                    <a href="https://twitter.com/intent/tweet?url={{ urlencode(route('news.show', app()->getLocale() == 'id' ? $news->slug : $news->slug_en)) }}&text={{ urlencode(app()->getLocale() == 'id' ? $news->title : $news->title_en) }}" class="btn btn-sm btn-outline-info me-2" target="_blank">
                                        <i class="fab fa-twitter me-1"></i> Twitter
                                    </a>
                                    <a href="https://wa.me/?text={{ urlencode((app()->getLocale() == 'id' ? $news->title : $news->title_en) . ' ' . route('news.show', app()->getLocale() == 'id' ? $news->slug : $news->slug_en)) }}" class="btn btn-sm btn-outline-success" target="_blank">
                                        <i class="fab fa-whatsapp me-1"></i> WhatsApp
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="sticky-sidebar">
                    <!-- Related News -->
                    <div class="card border-0 shadow-sm mb-4 fade-in">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">{{ app()->getLocale() == 'id' ? 'Berita Terkait' : 'Related News' }}</h5>
                        </div>
                        <div class="card-body">
                            @forelse($relatedNews as $related)
                                <div class="related-news-item mb-3 pb-3 {{ !$loop->last ? 'border-bottom' : '' }}">
                                    <div class="row g-0">
                                        <div class="col-4">
                                            <img src="{{ \App\Helpers\ImageHelper::getImageUrl($related->thumbnail, 'news') }}" class="img-fluid rounded" alt="{{ app()->getLocale() == 'id' ? $related->title : $related->title_en }}">
                                        </div>
                                        <div class="col-8 ps-3">
                                            <div class="small text-muted mb-1">{{ $related->published_at->format('d M Y') }}</div>
                                            <h6 class="mb-0"><a href="{{ route('news.show', app()->getLocale() == 'id' ? $related->slug : $related->slug_en) }}" class="text-decoration-none">{{ app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit($related->title, 50) : \Illuminate\Support\Str::limit($related->title_en, 50) }}</a></h6>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <p class="mb-0">{{ app()->getLocale() == 'id' ? 'Tidak ada berita terkait.' : 'No related news.' }}</p>
                            @endforelse
                        </div>
                    </div>

                    <!-- Latest Announcements -->
                    @include('public.partials.latest-announcements')

                    <!-- Latest Agenda -->
                    @include('public.partials.latest-agenda')

                    <!-- CTA -->
                    <div class="card border-0 bg-success text-white shadow-sm fade-in">
                        <div class="card-body p-4 text-center">
                            <h5 class="mb-3">{{ app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now' }}</h5>
                            <p>{{ app()->getLocale() == 'id' ? 'Jadilah bagian dari keluarga besar Pondok Pesantren Nurul Hayah 4.' : 'Be a part of the Nurul Hayah 4 Islamic Boarding School family.' }}</p>
                            <a href="{{ route('registration') }}" class="btn btn-light">{{ app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now' }}</a>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

@push('scripts')
    <script src="{{ asset('js/sticky-sidebar.js') }}"></script>
@endpush
@endsection
