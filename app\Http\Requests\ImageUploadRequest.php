<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

abstract class ImageUploadRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge($this->baseRules(), $this->imageRules());
    }

    /**
     * Get the base validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    abstract protected function baseRules(): array;

    /**
     * Get the image validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    protected function imageRules(): array
    {
        $rules = [];
        
        foreach ($this->getImageFields() as $field => $isRequired) {
            $rule = 'image|mimes:jpeg,png,jpg,gif';
            
            if ($isRequired) {
                $rule = 'required|' . $rule;
            } else {
                $rule = 'nullable|' . $rule;
            }
            
            $rules[$field] = $rule;
        }
        
        return $rules;
    }

    /**
     * Get the image fields for this request.
     * 
     * Override this method to define which fields are images and whether they are required.
     * Format: [field_name => is_required]
     * 
     * @return array
     */
    protected function getImageFields(): array
    {
        return [];
    }
}
