@extends('admin.layouts.app')

@php
use App\Models\Setting;
@endphp

@section('title', 'Configuration')

@section('content')
    <div class="container-fluid px-4">
        <h1 class="mt-4">Configuration</h1>
        <ol class="breadcrumb mb-4">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item">Settings</li>
            <li class="breadcrumb-item active">Configuration</li>
        </ol>

        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-cogs me-1"></i>
                System Configuration
            </div>
            <div class="card-body">
                <ul class="nav nav-tabs mb-4" id="configTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">General</button>
                    </li>
                </ul>

                <div class="tab-content" id="configTabsContent">
                    <!-- General Configuration Tab -->
                    <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                        <form action="{{ route('admin.settings.configuration.operator.update') }}" method="POST">
                            @csrf
                            <input type="hidden" name="config_type" value="general">

                            <div class="mb-3">
                                <label for="site_name" class="form-label">Site Name</label>
                                <input type="text" name="site_name" id="site_name" class="form-control" value="{{ $settings['site_name'] ?? 'Nurul Hayah 4' }}">
                                <div class="form-text">The name of your website.</div>
                            </div>

                            <div class="mb-3">
                                <label for="site_description" class="form-label">Site Description</label>
                                <textarea name="site_description" id="site_description" class="form-control" rows="2">{{ $settings['site_description'] ?? '' }}</textarea>
                                <div class="form-text">A short description of your website.</div>
                            </div>

                            <button type="submit" class="btn btn-primary">Save General Settings</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // Initialize tabs
        const triggerTabList = [].slice.call(document.querySelectorAll('#configTabs button'));
        triggerTabList.forEach(function (triggerEl) {
            const tabTrigger = new bootstrap.Tab(triggerEl);
            triggerEl.addEventListener('click', function (event) {
                event.preventDefault();
                tabTrigger.show();

                // Update the URL with the tab parameter
                const tabId = $(this).attr('id').replace('-tab', '');
                const url = new URL(window.location.href);
                url.searchParams.set('tab', tabId);
                window.history.replaceState({}, '', url);
            });
        });

        // Activate the correct tab based on URL parameter
        const urlParams = new URLSearchParams(window.location.search);
        const activeTab = urlParams.get('tab');
        if (activeTab) {
            const tabEl = document.querySelector(`#${activeTab}-tab`);
            if (tabEl) {
                const tab = new bootstrap.Tab(tabEl);
                tab.show();
            }
        }
    });
</script>
@endpush
