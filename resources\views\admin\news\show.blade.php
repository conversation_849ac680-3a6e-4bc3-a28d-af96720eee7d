@extends('admin.layouts.app')

@section('title', 'View News')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View News</h1>
        <div>
            <a href="{{ route('admin.news.edit', $news) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="{{ route('admin.news.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">News Details</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">ID:</div>
                        <div class="col-md-9">{{ $news->id }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Published Date:</div>
                        <div class="col-md-9">{{ $news->published_at->format('d M Y') }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Category:</div>
                        <div class="col-md-9">
                            @if($news->category)
                                <a href="{{ route('admin.categories.show', $news->category) }}" class="badge bg-info text-decoration-none">
                                    {{ $news->category->name }}
                                </a>
                            @else
                                <span class="text-muted">Uncategorized</span>
                            @endif
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Author:</div>
                        <div class="col-md-9">{{ $news->user->name }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Created At:</div>
                        <div class="col-md-9">{{ $news->created_at->format('d M Y H:i:s') }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Updated At:</div>
                        <div class="col-md-9">{{ $news->updated_at->format('d M Y H:i:s') }}</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Indonesian Content</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-3 fw-bold">Title:</div>
                                <div class="col-md-9">{{ $news->title }}</div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-3 fw-bold">Slug:</div>
                                <div class="col-md-9">{{ $news->slug }}</div>
                            </div>

                            <div class="row">
                                <div class="col-md-3 fw-bold">Content:</div>
                                <div class="col-md-9">{!! $news->content !!}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">English Content</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-3 fw-bold">Title:</div>
                                <div class="col-md-9">{{ $news->title_en }}</div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-3 fw-bold">Slug:</div>
                                <div class="col-md-9">{{ $news->slug_en }}</div>
                            </div>

                            <div class="row">
                                <div class="col-md-3 fw-bold">Content:</div>
                                <div class="col-md-9">{!! $news->content_en !!}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Images</h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="fw-bold">Thumbnail:</h6>
                        @if($news->thumbnail)
                            <img src="{{ asset('storage/' . $news->thumbnail) }}" alt="{{ $news->title }}" class="img-fluid rounded">
                        @else
                            <p class="text-muted">No thumbnail available</p>
                        @endif
                    </div>

                    <div>
                        <h6 class="fw-bold">Main Image:</h6>
                        @if($news->image)
                            <img src="{{ asset('storage/' . $news->image) }}" alt="{{ $news->title }}" class="img-fluid rounded">
                        @else
                            <p class="text-muted">No main image available</p>
                        @endif
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <a href="{{ route('news.show', $news->slug) }}" class="btn btn-info w-100 mb-2" target="_blank">
                        <i class="fas fa-external-link-alt me-1"></i> View on Website
                    </a>

                    <a href="{{ route('admin.news.edit', $news) }}" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-edit me-1"></i> Edit News
                    </a>

                    <form action="{{ route('admin.news.destroy', $news) }}" method="POST">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Are you sure you want to delete this news?')">
                            <i class="fas fa-trash me-1"></i> Delete News
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
