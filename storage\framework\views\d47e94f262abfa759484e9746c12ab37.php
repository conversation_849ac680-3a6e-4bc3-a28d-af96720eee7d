<?php $__env->startSection('title', app()->getLocale() == 'id' ? 'Pendaftaran Berhasil' : 'Registration Successful'); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3"><?php echo e(app()->getLocale() == 'id' ? 'Pendaftaran Berhasil' : 'Registration Successful'); ?></h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-white"><?php echo e(app()->getLocale() == 'id' ? 'Beranda' : 'Home'); ?></a></li>
                            <li class="breadcrumb-item"><a href="<?php echo e(route('registration')); ?>" class="text-white"><?php echo e(app()->getLocale() == 'id' ? 'Pendaftaran' : 'Registration'); ?></a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page"><?php echo e(app()->getLocale() == 'id' ? 'Berhasil' : 'Success'); ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Success Message -->
    <section class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto" data-aos="fade-up">
                    <div class="card border-0 shadow-sm text-center">
                        <div class="card-body p-5">
                            <div class="mb-4">
                                <i class="fas fa-check-circle text-success fa-5x"></i>
                            </div>

                            <h2 class="mb-4"><?php echo e(app()->getLocale() == 'id' ? 'Terima Kasih!' : 'Thank You!'); ?></h2>

                            <p class="lead mb-4"><?php echo e(app()->getLocale() == 'id' ? 'Pendaftaran Anda telah berhasil dikirim. Kami akan memproses pendaftaran Anda dan menghubungi Anda segera.' : 'Your registration has been successfully submitted. We will process your registration and contact you soon.'); ?></p>

                            <div class="alert alert-success mb-4">
                                <h5 class="mb-3"><?php echo e(app()->getLocale() == 'id' ? 'Informasi Pendaftaran:' : 'Registration Information:'); ?></h5>
                                <p class="mb-2"><strong><?php echo e(app()->getLocale() == 'id' ? 'Nomor Pendaftaran:' : 'Registration Number:'); ?></strong> <?php echo e($registration->registration_number); ?></p>
                                <p class="mb-2"><strong><?php echo e(app()->getLocale() == 'id' ? 'Nama:' : 'Name:'); ?></strong> <?php echo e($registration->full_name); ?></p>
                                <p class="mb-0"><strong><?php echo e(app()->getLocale() == 'id' ? 'Tanggal Pendaftaran:' : 'Registration Date:'); ?></strong> <?php echo e($registration->created_at->format('d M Y')); ?></p>
                            </div>

                            <p class="mb-4"><?php echo e(app()->getLocale() == 'id' ? 'Silakan simpan nomor pendaftaran Anda untuk memeriksa status pendaftaran di kemudian hari.' : 'Please save your registration number to check your registration status later.'); ?></p>

                            <div class="d-flex justify-content-center gap-3">
                                <a href="<?php echo e(route('home')); ?>" class="btn btn-outline-success"><?php echo e(app()->getLocale() == 'id' ? 'Kembali ke Beranda' : 'Back to Home'); ?></a>
                                <a href="<?php echo e(route('registration.print', ['registration' => $registration->id])); ?>" class="btn btn-success"><?php echo e(app()->getLocale() == 'id' ? 'Cetak Bukti Pendaftaran' : 'Print Registration Proof'); ?></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Next Steps -->
    <section class="section-padding bg-light">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2><?php echo e(app()->getLocale() == 'id' ? 'Langkah Selanjutnya' : 'Next Steps'); ?></h2>
                <p><?php echo e(app()->getLocale() == 'id' ? 'Berikut adalah langkah-langkah yang perlu Anda lakukan selanjutnya' : 'Here are the steps you need to take next'); ?></p>
            </div>

            <div class="row">
                <div class="col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="step-number mb-3">1</div>
                            <h4 class="card-title"><?php echo e(app()->getLocale() == 'id' ? 'Pembayaran Biaya Pendaftaran' : 'Pay Registration Fee'); ?></h4>
                            <p class="card-text"><?php echo e(app()->getLocale() == 'id' ? 'Lakukan pembayaran biaya pendaftaran sebesar Rp 500.000 melalui transfer bank atau langsung di kantor kami.' : 'Make payment of the registration fee of IDR 500,000 via bank transfer or directly at our office.'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="step-number mb-3">2</div>
                            <h4 class="card-title"><?php echo e(app()->getLocale() == 'id' ? 'Konfirmasi Pembayaran' : 'Confirm Payment'); ?></h4>
                            <p class="card-text"><?php echo e(app()->getLocale() == 'id' ? 'Konfirmasikan pembayaran Anda dengan mengirimkan bukti transfer ke email atau WhatsApp kami.' : 'Confirm your payment by sending the transfer proof to our email or WhatsApp.'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="300">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="step-number mb-3">3</div>
                            <h4 class="card-title"><?php echo e(app()->getLocale() == 'id' ? 'Tunggu Informasi Selanjutnya' : 'Wait for Further Information'); ?></h4>
                            <p class="card-text"><?php echo e(app()->getLocale() == 'id' ? 'Kami akan menghubungi Anda untuk memberikan informasi selanjutnya mengenai proses pendaftaran.' : 'We will contact you to provide further information regarding the registration process.'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .step-number {
        display: inline-block;
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        background-color: #198754;
        color: #fff;
        font-size: 20px;
        font-weight: 600;
        border-radius: 50%;
    }

    @media print {
        header, footer, .section-padding.bg-light, .btn-outline-success {
            display: none !important;
        }

        .section-padding {
            padding: 0 !important;
        }

        .card {
            box-shadow: none !important;
            border: 1px solid #ddd !important;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('public.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\IT\nurul-hayah-4\resources\views/public/registration/success.blade.php ENDPATH**/ ?>