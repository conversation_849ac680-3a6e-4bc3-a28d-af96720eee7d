@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? 'Profil' : 'Profile')

@push('styles')
<style>
    .motto-content {
        font-weight: 500;
        font-size: 1.3rem;
        color: #198754;
        display: inline;
    }

    .motto-text i {
        color: #198754;
        font-size: 1.3rem;
        vertical-align: middle;
        flex-shrink: 0;
    }

    .text-success.d-flex {
        flex-wrap: nowrap;
    }
</style>
@endpush

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? 'Profil Pondok Pesantren' : 'Islamic Boarding School Profile' }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? 'Profil' : 'Profile' }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Motto Section -->
    <section class="section-padding">
        <div class="container">
            <div class="section-title text-center" data-aos="fade-up">
                <h2>{{ app()->getLocale() == 'id' ? 'Motto' : 'Motto' }}</h2>
                <p>{{ app()->getLocale() == 'id' ? 'Semangat dan nilai-nilai Pondok Pesantren Nurul Hayah 4' : 'Spirit and values of Nurul Hayah 4 Islamic Boarding School' }}</p>
            </div>

            @if($motto)
            <div class="row justify-content-center">
                <div class="col-lg-8 mb-5" data-aos="fade-up">
                    <div class="card border-0 shadow-sm text-center motto-card">
                        <div class="card-body p-5">

                            <div class="card-text motto-text mt-2">
                                <div class="text-success">
                                    <i class="fas fa-quote-left me-2"></i>
                                    <span class="motto-content">{!! app()->getLocale() == 'id' ? $motto->content : $motto->content_en !!}</span>
                                    <i class="fas fa-quote-right ms-2"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </section>

    <!-- Vision & Mission Section -->
    <section class="section-padding bg-light">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>{{ app()->getLocale() == 'id' ? 'Visi & Misi' : 'Vision & Mission' }}</h2>
                <p>{{ app()->getLocale() == 'id' ? 'Tujuan dan arah pengembangan Pondok Pesantren Nurul Hayah 4' : 'Goals and development direction of Nurul Hayah 4 Islamic Boarding School' }}</p>
            </div>

            <div class="row">
                @if($vision)
                <div class="col-lg-6 mb-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body p-4">
                            <h3 class="card-title text-success mb-3">{{ app()->getLocale() == 'id' ? $vision->title : $vision->title_en }}</h3>
                            <div class="card-text">
                                {!! app()->getLocale() == 'id' ? $vision->content : $vision->content_en !!}
                            </div>
                        </div>
                    </div>
                </div>
                @endif

                @if($mission)
                <div class="col-lg-6 mb-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body p-4">
                            <h3 class="card-title text-success mb-3">{{ app()->getLocale() == 'id' ? $mission->title : $mission->title_en }}</h3>
                            <div class="card-text">
                                {!! app()->getLocale() == 'id' ? $mission->content : $mission->content_en !!}
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </div>

            <div class="text-center mt-4">
                <a href="{{ route('profile.vision-mission') }}" class="btn btn-success">{{ app()->getLocale() == 'id' ? 'Selengkapnya' : 'Read More' }}</a>
            </div>
        </div>
    </section>

    <!-- History Section -->
    <section class="section-padding">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>{{ app()->getLocale() == 'id' ? 'Sejarah Singkat' : 'Brief History' }}</h2>
                <p>{{ app()->getLocale() == 'id' ? 'Perjalanan Pondok Pesantren Nurul Hayah 4 sejak didirikan' : 'The journey of Nurul Hayah 4 Islamic Boarding School since its establishment' }}</p>
            </div>

            <div class="row align-items-center">
                <div class="col-lg-6 mb-4 mb-lg-0" data-aos="fade-right">
                    @if($history && $history->image)
                        <img src="{{ asset('storage/' . $history->image) }}" alt="{{ app()->getLocale() == 'id' ? $history->title : $history->title_en }}" class="img-fluid rounded shadow-sm">
                    @else
                        <img src="{{ asset('images/history.jpg') }}" alt="History" class="img-fluid rounded shadow-sm">
                    @endif
                </div>
                <div class="col-lg-6" data-aos="fade-left">
                    @if($history)
                    <h3 class="text-success mb-3">{{ app()->getLocale() == 'id' ? $history->title : $history->title_en }}</h3>
                    <div class="mb-4">
                        {!! app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit(strip_tags($history->content), 500) : \Illuminate\Support\Str::limit(strip_tags($history->content_en), 500) !!}
                    </div>
                    <a href="{{ route('profile.history') }}" class="btn btn-success">{{ app()->getLocale() == 'id' ? 'Baca Selengkapnya' : 'Read More' }}</a>
                    @endif
                </div>
            </div>
        </div>
    </section>
@endsection
