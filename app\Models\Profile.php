<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Profile extends Model
{
    protected $fillable = [
        'type',
        'title',
        'title_en',
        'content',
        'content_en',
        'image',
        'order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the timeline entries for the profile.
     */
    public function timelines()
    {
        return $this->hasMany(Timeline::class)->orderBy('order');
    }
}
