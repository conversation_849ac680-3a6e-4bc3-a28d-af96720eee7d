@extends('admin.layouts.app')

@section('title', 'Edit Profile')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Edit Profile</h1>
        <a href="{{ route('admin.profiles.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="{{ route('admin.profiles.update', $profile) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="title" class="form-label">Title (Indonesian)</label>
                        <input type="text" class="form-control @error('title') is-invalid @enderror" id="title" name="title" value="{{ old('title', $profile->title) }}" required>
                        @error('title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label for="title_en" class="form-label">Title (English)</label>
                        <input type="text" class="form-control @error('title_en') is-invalid @enderror" id="title_en" name="title_en" value="{{ old('title_en', $profile->title_en) }}" required>
                        @error('title_en')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required>
                            <option value="" disabled>Select Type</option>
                            <option value="motto" {{ old('type', $profile->type) == 'motto' ? 'selected' : '' }}>Motto</option>
                            <option value="vision" {{ old('type', $profile->type) == 'vision' ? 'selected' : '' }}>Vision</option>
                            <option value="mission" {{ old('type', $profile->type) == 'mission' ? 'selected' : '' }}>Mission</option>
                            <option value="history" {{ old('type', $profile->type) == 'history' ? 'selected' : '' }}>History</option>
                        </select>
                        @error('type')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="content" class="form-label">Content (Indonesian)</label>
                        <textarea class="form-control summernote @error('content') is-invalid @enderror" id="content" name="content" rows="6" required>{{ old('content', $profile->content) }}</textarea>
                        @error('content')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label for="content_en" class="form-label">Content (English)</label>
                        <textarea class="form-control summernote @error('content_en') is-invalid @enderror" id="content_en" name="content_en" rows="6" required>{{ old('content_en', $profile->content_en) }}</textarea>
                        @error('content_en')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-3" id="image-section" style="{{ $profile->type === 'history' ? '' : 'display: none;' }}">
                    <div class="col-md-12">
                        <label for="image" class="form-label">Content Image</label>
                        <input type="file" class="form-control @error('image') is-invalid @enderror" id="image" name="image" accept="image/*">
                        <small class="form-text text-muted">Upload an image to be displayed with the history content.</small>

                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror

                        @if($profile->image)
                        <div class="mt-2">
                            <img src="{{ asset('storage/' . $profile->image) }}" alt="Content Image" class="img-thumbnail" style="max-height: 200px;">
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Timeline Section (Only for History type) -->
                <div id="timeline-section" class="mt-4" style="{{ $profile->type === 'history' ? '' : 'display: none;' }}">
                    <h4 class="mb-3">Timeline Entries</h4>
                    <p class="text-muted">Add important milestones to be displayed in the timeline section of the history page.</p>

                    <div id="timeline-container">
                        @if(count($timelines) > 0)
                            @foreach($timelines as $index => $timeline)
                                <div class="timeline-item card mb-3">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">Timeline Entry #{{ $index + 1 }}</h5>
                                        <button type="button" class="btn btn-sm btn-danger remove-timeline">
                                            <i class="fas fa-trash"></i> Remove
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <input type="hidden" name="timelines[{{ $index }}][id]" value="{{ $timeline->id }}">

                                        <div class="row mb-3">
                                            <div class="col-md-12">
                                                <label class="form-label">Year</label>
                                                <input type="text" class="form-control" name="timelines[{{ $index }}][year]" value="{{ $timeline->year }}" placeholder="e.g., 2010">
                                            </div>
                                        </div>

                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label class="form-label">Title (Indonesian)</label>
                                                <input type="text" class="form-control" name="timelines[{{ $index }}][title]" value="{{ $timeline->title }}">
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">Title (English)</label>
                                                <input type="text" class="form-control" name="timelines[{{ $index }}][title_en]" value="{{ $timeline->title_en }}">
                                            </div>
                                        </div>

                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label class="form-label">Content (Indonesian)</label>
                                                <textarea class="form-control summernote" name="timelines[{{ $index }}][content]" rows="3">{{ $timeline->content }}</textarea>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">Content (English)</label>
                                                <textarea class="form-control summernote" name="timelines[{{ $index }}][content_en]" rows="3">{{ $timeline->content_en }}</textarea>
                                            </div>
                                        </div>

                                        <div class="row mb-3">
                                            <div class="col-md-12">
                                                <label class="form-label">Image</label>
                                                <input type="file" class="form-control" name="timelines[{{ $index }}][image]" accept="image/*">
                                                <small class="form-text text-muted">Upload an image to be displayed with this timeline entry.</small>

                                                @if($timeline->image)
                                                <div class="mt-2">
                                                    <img src="{{ asset('storage/' . $timeline->image) }}" alt="Timeline Image" class="img-thumbnail" style="max-height: 150px;">
                                                </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @endif
                    </div>

                    <div class="text-center mt-3 mb-3">
                        <button type="button" class="btn btn-success" id="add-timeline">
                            <i class="fas fa-plus"></i> Add Timeline Entry
                        </button>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Show/hide timeline and image sections based on profile type
        const typeSelect = document.getElementById('type');
        const timelineSection = document.getElementById('timeline-section');
        const imageSection = document.getElementById('image-section');

        typeSelect.addEventListener('change', function() {
            if (this.value === 'history') {
                timelineSection.style.display = 'block';
                imageSection.style.display = 'block';
            } else {
                timelineSection.style.display = 'none';
                imageSection.style.display = 'none';
            }
        });

        // Add new timeline entry
        const addTimelineBtn = document.getElementById('add-timeline');
        const timelineContainer = document.getElementById('timeline-container');
        let timelineIndex = {{ count($timelines) }};

        addTimelineBtn.addEventListener('click', function() {
            const newTimeline = document.createElement('div');
            newTimeline.className = 'timeline-item card mb-3';
            newTimeline.innerHTML = `
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Timeline Entry #${timelineIndex + 1}</h5>
                    <button type="button" class="btn btn-sm btn-danger remove-timeline">
                        <i class="fas fa-trash"></i> Remove
                    </button>
                </div>
                <div class="card-body">
                    <input type="hidden" name="timelines[${timelineIndex}][id]" value="">

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label">Year</label>
                            <input type="text" class="form-control" name="timelines[${timelineIndex}][year]" placeholder="e.g., 2010">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Title (Indonesian)</label>
                            <input type="text" class="form-control" name="timelines[${timelineIndex}][title]">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Title (English)</label>
                            <input type="text" class="form-control" name="timelines[${timelineIndex}][title_en]">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Content (Indonesian)</label>
                            <textarea class="form-control summernote" name="timelines[${timelineIndex}][content]" rows="3"></textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Content (English)</label>
                            <textarea class="form-control summernote" name="timelines[${timelineIndex}][content_en]" rows="3"></textarea>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label">Image</label>
                            <input type="file" class="form-control" name="timelines[${timelineIndex}][image]" accept="image/*">
                            <small class="form-text text-muted">Upload an image to be displayed with this timeline entry.</small>
                        </div>
                    </div>
                </div>
            `;

            timelineContainer.appendChild(newTimeline);
            timelineIndex++;

            // Add event listener to the new remove button
            const removeBtn = newTimeline.querySelector('.remove-timeline');
            removeBtn.addEventListener('click', function() {
                newTimeline.remove();
                updateTimelineNumbers();
            });

            // Initialize Summernote for the new timeline entries
            $(newTimeline).find('.summernote').each(function() {
                $(this).summernote({
                    height: 150,
                    toolbar: [
                        ['font', ['bold', 'underline', 'italic', 'clear']],
                        ['fontname', ['fontname']],
                        ['fontsize', ['fontsize']],
                        ['color', ['color']],
                        ['para', ['ul', 'ol', 'paragraph']],
                        ['table', ['table']],
                        ['insert', ['link', 'picture']]
                    ]
                });
            });
        });

        // Remove timeline entry
        document.querySelectorAll('.remove-timeline').forEach(button => {
            button.addEventListener('click', function() {
                this.closest('.timeline-item').remove();
                updateTimelineNumbers();
            });
        });

        // Update timeline entry numbers
        function updateTimelineNumbers() {
            document.querySelectorAll('.timeline-item').forEach((item, index) => {
                item.querySelector('h5').textContent = `Timeline Entry #${index + 1}`;
            });
        }
    });
</script>
@endpush
