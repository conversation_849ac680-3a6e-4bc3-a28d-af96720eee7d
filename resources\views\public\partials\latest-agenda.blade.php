<!-- Latest Agenda -->
<div class="card border-0 shadow-sm mb-4 fade-in">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">{{ app()->getLocale() == 'id' ? 'Agenda Terbaru' : 'Latest Agenda' }}</h5>
    </div>
    <div class="card-body">
        @forelse($latestAgenda as $agenda)
            <div class="related-item mb-3 pb-3 {{ !$loop->last ? 'border-bottom' : '' }}">
                <div class="d-flex">
                    <div class="agenda-date-small me-3">
                        <div class="day">{{ $agenda->date->format('d') }}</div>
                        <div class="month">{{ $agenda->date->format('M') }}</div>
                    </div>
                    <div>
                        <h6 class="mb-1">
                            <a href="{{ route('agenda.show', $agenda->id) }}" class="text-decoration-none">
                                {{ app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit($agenda->title, 50) : \Illuminate\Support\Str::limit($agenda->title_en, 50) }}
                            </a>
                        </h6>
                        <div class="small text-muted">
                            <i class="far fa-clock me-1"></i> {{ $agenda->time->format('H:i') }}
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <p class="mb-0">{{ app()->getLocale() == 'id' ? 'Tidak ada agenda terbaru.' : 'No latest agenda.' }}</p>
        @endforelse
    </div>
</div>
