<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Achievement;
use App\Services\ImageService;
use Illuminate\Http\Request;

class AchievementController extends Controller
{
    /**
     * The image service instance.
     *
     * @var \App\Services\ImageService
     */
    protected $imageService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\ImageService  $imageService
     * @return void
     */
    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $achievements = Achievement::orderBy('order', 'asc')->get();
        return view('admin.achievements.index', compact('achievements'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.achievements.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'achievement_date' => 'nullable|date',
            'award_by' => 'nullable|string|max:255',
            'award_by_en' => 'nullable|string|max:255',
            'order' => 'nullable|integer',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif',
        ]);

        $data = $request->except('image');

        // Set is_active to true by default and is_featured based on checkbox
        $data['is_active'] = true;
        $data['is_featured'] = $request->has('is_featured');



        // Handle image upload with compression
        if ($request->hasFile('image')) {
            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'achievements',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }

        Achievement::create($data);

        return redirect()->route('admin.achievements.index')
            ->with('success', 'Achievement created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Achievement  $achievement
     * @return \Illuminate\View\View
     */
    public function show(Achievement $achievement)
    {
        return view('admin.achievements.show', compact('achievement'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Achievement  $achievement
     * @return \Illuminate\View\View
     */
    public function edit(Achievement $achievement)
    {
        return view('admin.achievements.edit', compact('achievement'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Achievement  $achievement
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Achievement $achievement)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'achievement_date' => 'nullable|date',
            'award_by' => 'nullable|string|max:255',
            'award_by_en' => 'nullable|string|max:255',
            'order' => 'nullable|integer',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif',
        ]);

        $data = $request->except('image');

        // Set is_active to true by default and is_featured based on checkbox
        $data['is_active'] = true;
        $data['is_featured'] = $request->has('is_featured');



        // Handle image upload with compression
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($achievement->image) {
                $this->imageService->deleteImage($achievement->image);
            }

            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'achievements',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }

        $achievement->update($data);

        return redirect()->route('admin.achievements.index')
            ->with('success', 'Achievement updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Achievement  $achievement
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Achievement $achievement)
    {
        // Delete image if exists
        if ($achievement->image) {
            $this->imageService->deleteImage($achievement->image);
        }

        $achievement->delete();

        return redirect()->route('admin.achievements.index')
            ->with('success', 'Achievement deleted successfully.');
    }
}
