<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Announcement;
use App\Models\Agenda;
use App\Models\News;

class AnnouncementController extends Controller
{
    /**
     * Display a listing of the announcements.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get all active announcements
        $announcements = Announcement::where('is_active', true)
            ->orderBy('order', 'asc')
            ->paginate(10);

        return view('public.announcements.index', compact('announcements'));
    }

    /**
     * Display the specified announcement.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $announcement = Announcement::where('is_active', true)
            ->findOrFail($id);

        // Get related announcements
        $relatedAnnouncements = Announcement::where('is_active', true)
            ->where('id', '!=', $announcement->id)
            ->orderBy('start_date', 'desc')
            ->take(3)
            ->get();

        // Get latest agenda items
        $latestAgenda = \App\Models\Agenda::where('is_active', true)
            ->orderBy('date', 'asc')
            ->take(3)
            ->get();

        // Get latest news
        $latestNews = \App\Models\News::published()
            ->orderBy('published_at', 'desc')
            ->take(3)
            ->get();

        return view('public.announcements.show', compact(
            'announcement',
            'relatedAnnouncements',
            'latestAgenda',
            'latestNews'
        ));
    }
}
