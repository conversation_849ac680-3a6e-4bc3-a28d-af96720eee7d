@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? 'Galeri' : 'Gallery')

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? 'Galeri Kegiatan' : 'Activity Gallery' }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? 'Galeri' : 'Gallery' }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Filter -->
    <section class="py-4 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="gallery-filter text-center" data-aos="fade-up">
                        <a href="{{ route('gallery') }}" class="btn {{ !isset($category) ? 'btn-success' : 'btn-outline-success' }} me-2 mb-2">{{ app()->getLocale() == 'id' ? 'Semua' : 'All' }}</a>

                        @foreach($categories as $cat)
                            <a href="{{ route('gallery.category', $cat) }}" class="btn {{ isset($category) && $category == $cat ? 'btn-success' : 'btn-outline-success' }} me-2 mb-2">{{ $cat }}</a>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Grid -->
    <section class="section-padding">
        <div class="container">
            <div class="row justify-content-center">
                @forelse($galleries as $gallery)
                    <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="{{ $loop->iteration % 3 * 100 }}">
                        <div class="gallery-item">
                            <div class="gallery-image-wrapper">
                                @if($gallery->image)
                                    <img src="{{ asset('storage/' . $gallery->image) }}" alt="{{ app()->getLocale() == 'id' ? $gallery->title : $gallery->title_en }}">
                                @else
                                    <img src="{{ asset('images/gallery-placeholder.jpg') }}" alt="{{ app()->getLocale() == 'id' ? $gallery->title : $gallery->title_en }}">
                                @endif
                                <div class="gallery-overlay">
                                    <a href="{{ asset('storage/' . $gallery->image) }}" class="gallery-link">
                                        <i class="fas fa-search-plus"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="gallery-caption bg-white p-3">
                                <h5>{{ app()->getLocale() == 'id' ? $gallery->title : $gallery->title_en }}</h5>
                                @if($gallery->description || $gallery->description_en)
                                    <div class="mb-0 text-muted gallery-description summernote-content">
                                        {!! app()->getLocale() == 'id' ? $gallery->description : $gallery->description_en !!}
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12 text-center">
                        <div class="alert alert-info">
                            {{ app()->getLocale() == 'id' ? 'Belum ada galeri yang tersedia.' : 'No gallery available yet.' }}
                        </div>
                    </div>
                @endforelse
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $galleries->links() }}
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
    .gallery-item {
        position: relative;
        overflow: hidden;
        border-radius: 5px;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .gallery-image-wrapper {
        position: relative;
        overflow: hidden;
        height: 250px;
        border-radius: 5px 5px 0 0;
    }

    .gallery-item img {
        width: 100%;
        height: 250px;
        object-fit: cover;
        transition: all 0.5s ease;
    }

    .gallery-item:hover img {
        transform: scale(1.1);
    }

    .gallery-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        background: rgba(25, 135, 84, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: all 0.3s ease;
        z-index: 2;
        border-radius: 5px 5px 0 0;
    }

    .gallery-link {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
    }

    .gallery-item:hover .gallery-overlay {
        opacity: 1;
    }

    .gallery-overlay i {
        color: #fff;
        font-size: 24px;
    }

    .gallery-caption {
        position: relative;
        z-index: 1;
    }

    /* Gallery description with formatting support */
    .gallery-description.summernote-content {
        font-size: 0.9rem;
        color: #6c757d;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        max-height: 60px;
    }

    .gallery-description.summernote-content p {
        margin-bottom: 0.5em;
        font-size: 0.9rem;
    }

    .gallery-description.summernote-content strong,
    .gallery-description.summernote-content b {
        font-weight: bold;
    }

    .gallery-description.summernote-content em,
    .gallery-description.summernote-content i {
        font-style: italic;
    }

    .gallery-filter .btn {
        border-radius: 30px;
        padding: 8px 20px;
        transition: all 0.3s ease;
    }
</style>
@endpush

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Gallery lightbox
        const galleryItems = document.querySelectorAll('.gallery-item');
        if (galleryItems.length > 0) {
            galleryItems.forEach(item => {
                const link = item.querySelector('.gallery-link');
                if (link) {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const imgSrc = this.getAttribute('href');
                        const lightbox = document.createElement('div');
                        lightbox.classList.add('lightbox');

                        lightbox.innerHTML = `
                            <div class="lightbox-content">
                                <img src="${imgSrc}" alt="Gallery Image">
                                <span class="lightbox-close">&times;</span>
                            </div>
                        `;

                        document.body.appendChild(lightbox);

                        // Prevent scrolling when lightbox is open
                        document.body.style.overflow = 'hidden';

                        // Close lightbox when clicking on the close button or outside the image
                        lightbox.addEventListener('click', function(e) {
                            if (e.target === this || e.target.classList.contains('lightbox-close')) {
                                document.body.removeChild(lightbox);
                                document.body.style.overflow = 'auto';
                            }
                        });
                    });
                }
            });
        }
    });
</script>
@endpush
