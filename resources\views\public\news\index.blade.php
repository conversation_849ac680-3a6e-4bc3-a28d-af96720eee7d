@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? 'Berita' : 'News')

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? 'Berita & Artikel' : 'News & Articles' }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? 'Berita' : 'News' }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- News List -->
    <section class="section-padding">
        <div class="container">
            @if(isset($categories) && $categories->count() > 0)
                <!-- Category Tabs -->
                <ul class="nav nav-tabs mb-4" id="newsCategoryTabs" role="tablist">
                    @foreach($categories as $index => $category)
                        <li class="nav-item" role="presentation">
                            <button class="nav-link {{ $index === 0 ? 'active' : '' }}"
                                id="category-{{ $category->id }}-tab"
                                data-bs-toggle="tab"
                                data-bs-target="#category-{{ $category->id }}"
                                type="button"
                                role="tab"
                                aria-controls="category-{{ $category->id }}"
                                aria-selected="{{ $index === 0 ? 'true' : 'false' }}">
                                {{ app()->getLocale() == 'id' ? $category->name : $category->name_en }}
                            </button>
                        </li>
                    @endforeach
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="newsCategoryTabsContent">
                    @foreach($categories as $index => $category)
                        <div class="tab-pane fade {{ $index === 0 ? 'show active' : '' }}"
                            id="category-{{ $category->id }}"
                            role="tabpanel"
                            aria-labelledby="category-{{ $category->id }}-tab">

                            <div class="row">
                                @forelse($newsByCategory[$category->id] as $item)
                                    <div class="col-lg-4 col-md-6 mb-4 fade-in">
                                        <div class="news-card card h-100">
                                            <img src="{{ \App\Helpers\ImageHelper::getImageUrl($item->thumbnail, 'news') }}" class="card-img-top" alt="{{ app()->getLocale() == 'id' ? $item->title : $item->title_en }}">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <div class="news-date">
                                                        <i class="far fa-calendar-alt me-1"></i> {{ $item->published_at->format('d M Y') }}
                                                    </div>
                                                    <div class="news-category">
                                                        <span class="badge bg-success">
                                                            {{ app()->getLocale() == 'id' ? $category->name : $category->name_en }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <h5 class="card-title">{{ app()->getLocale() == 'id' ? $item->title : $item->title_en }}</h5>
                                                <p class="card-text">
                                                    @php
                                                        $content = app()->getLocale() == 'id' ? $item->content : $item->content_en;
                                                        $plainText = strip_tags($content);
                                                        // Hapus karakter HTML entities seperti &nbsp;
                                                        $plainText = html_entity_decode($plainText);
                                                        // Hapus whitespace berlebih
                                                        $plainText = preg_replace('/\s+/', ' ', $plainText);
                                                        $plainText = trim($plainText);
                                                        $excerpt = \Illuminate\Support\Str::limit($plainText, 150);
                                                    @endphp
                                                    {{ $excerpt }}
                                                </p>
                                                <a href="{{ route('news.show', app()->getLocale() == 'id' ? $item->slug : $item->slug_en) }}" class="btn btn-sm btn-outline-success">{{ app()->getLocale() == 'id' ? 'Baca Selengkapnya' : 'Read More' }}</a>
                                            </div>
                                        </div>
                                    </div>
                                @empty
                                    <div class="col-12 text-center">
                                        <div class="alert alert-info">
                                            {{ app()->getLocale() == 'id' ? 'Belum ada berita dalam kategori ini.' : 'No news available in this category yet.' }}
                                        </div>
                                    </div>
                                @endforelse
                            </div>

                            <!-- Pagination -->
                            <div class="d-flex justify-content-center mt-4">
                                {{ $newsByCategory[$category->id]->links() }}
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <!-- Fallback if no categories exist -->
                <div class="row">
                    @forelse($news as $item)
                        <div class="col-lg-4 col-md-6 mb-4 fade-in">
                            <div class="news-card card h-100">
                                <img src="{{ \App\Helpers\ImageHelper::getImageUrl($item->thumbnail, 'news') }}" class="card-img-top" alt="{{ app()->getLocale() == 'id' ? $item->title : $item->title_en }}">
                                <div class="card-body">
                                    <div class="news-date mb-2">
                                        <i class="far fa-calendar-alt me-1"></i> {{ $item->published_at->format('d M Y') }}
                                    </div>
                                    <h5 class="card-title">{{ app()->getLocale() == 'id' ? $item->title : $item->title_en }}</h5>
                                    <p class="card-text">
                                        @php
                                            $content = app()->getLocale() == 'id' ? $item->content : $item->content_en;
                                            $plainText = strip_tags($content);
                                            // Hapus karakter HTML entities seperti &nbsp;
                                            $plainText = html_entity_decode($plainText);
                                            // Hapus whitespace berlebih
                                            $plainText = preg_replace('/\s+/', ' ', $plainText);
                                            $plainText = trim($plainText);
                                            $excerpt = \Illuminate\Support\Str::limit($plainText, 150);
                                        @endphp
                                        {{ $excerpt }}
                                    </p>
                                    <a href="{{ route('news.show', app()->getLocale() == 'id' ? $item->slug : $item->slug_en) }}" class="btn btn-sm btn-outline-success">{{ app()->getLocale() == 'id' ? 'Baca Selengkapnya' : 'Read More' }}</a>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="col-12 text-center">
                            <div class="alert alert-info">
                                {{ app()->getLocale() == 'id' ? 'Belum ada berita yang tersedia.' : 'No news available yet.' }}
                            </div>
                        </div>
                    @endforelse
                </div>

                <!-- Pagination -->
                @if(isset($news))
                    <div class="d-flex justify-content-center mt-4">
                        {{ $news->links() }}
                    </div>
                @endif
            @endif
        </div>
    </section>
@endsection
