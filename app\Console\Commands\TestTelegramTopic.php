<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\TelegramBotService;
use App\Models\Setting;

class TestTelegramTopic extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:test-topic';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test sending a message to a Telegram group topic';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Telegram topic message...');
        
        $telegramService = new TelegramBotService();
        $chatId = Setting::getValue('telegram_chat_id');
        $topicId = (int)Setting::getValue('telegram_group_topic_id');
        
        // Check if the chat ID is a group chat (starts with -)
        $isGroupChat = strpos($chatId, '-') === 0;
        
        $this->info('Chat ID: ' . $chatId);
        $this->info('Is Group Chat: ' . ($isGroupChat ? 'Yes' : 'No'));
        $this->info('Topic ID: ' . $topicId);
        
        if (empty($chatId)) {
            $this->error('Chat ID is missing. Please configure it in the admin panel.');
            return 1;
        }
        
        // Send test message to Telegram
        $message = 'This is a test message from the telegram:test-topic command at ' . now()->format('Y-m-d H:i:s');
        
        try {
            // Use topic ID if it's a group chat and topic ID is set
            if ($isGroupChat && $topicId > 0) {
                $this->info('Sending message to group chat with topic ID: ' . $topicId);
                $result = $telegramService->sendMessage($chatId, $message, [], $topicId);
            } else {
                $this->info('Sending message to chat without topic ID');
                $result = $telegramService->sendMessage($chatId, $message);
            }
            
            if ($result && isset($result['ok']) && $result['ok']) {
                $this->info('Message sent successfully!');
                return 0;
            } else {
                $this->error('Failed to send message: ' . json_encode($result));
                return 1;
            }
        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
            return 1;
        }
    }
}
