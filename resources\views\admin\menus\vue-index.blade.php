@extends('admin.layouts.app')

@section('title', 'Menu Management')

@push('styles')
    <link rel="stylesheet" href="{{ asset('build/assets/menu-manager-zSNw2kbs.css') }}">
@endpush



@section('content')
<div class="container-fluid px-4">
    <h1 class="mt-4">Menu Management</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
        <li class="breadcrumb-item active">Menu Management</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-bars me-1"></i>
            Menu Manager
        </div>
        <div class="card-body">
            <div id="menu-manager-app">
                <!-- Vue app will mount here -->
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Check if Vue is loaded
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, checking for Vue app');
        setTimeout(function() {
            const menuManagerEl = document.getElementById('menu-manager-app');
            if (menuManagerEl) {
                console.log('Menu manager element found:', menuManagerEl.innerHTML);
            } else {
                console.error('Menu manager element not found');
            }
        }, 1000);
    });
</script>
    <script type="module" src="{{ asset('build/assets/menu-manager-CrCgstmg.js') }}"></script>
@endpush
@endsection
