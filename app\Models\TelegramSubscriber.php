<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TelegramSubscriber extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'chat_id',
        'username',
        'first_name',
        'last_name',
        'language',
        'is_active',
        'subscribed_at',
        'last_interaction_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'subscribed_at' => 'datetime',
        'last_interaction_at' => 'datetime',
    ];

    /**
     * Scope a query to only include active subscribers.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the full name of the subscriber.
     *
     * @return string
     */
    public function getFullNameAttribute()
    {
        $parts = [];
        if ($this->first_name) {
            $parts[] = $this->first_name;
        }
        if ($this->last_name) {
            $parts[] = $this->last_name;
        }

        return count($parts) > 0 ? implode(' ', $parts) : 'Unknown';
    }

    /**
     * Get the display name of the subscriber.
     *
     * @return string
     */
    public function getDisplayNameAttribute()
    {
        if ($this->username) {
            return '@' . $this->username;
        }

        return $this->full_name;
    }

    /**
     * Update the last interaction timestamp.
     *
     * @return bool
     */
    public function updateLastInteraction()
    {
        $this->last_interaction_at = now();
        return $this->save();
    }
}
