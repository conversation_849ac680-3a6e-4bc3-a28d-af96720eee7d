<nav class="navbar navbar-expand-lg navbar-light bg-white py-3 shadow-sm">
    <div class="container-fluid">
        <div class="d-flex align-items-center">
            <button class="btn btn-dark" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            <a href="<?php echo e(route('home')); ?>" class="btn btn-outline-success ms-2" target="_blank" title="Visit Site">
                <i class="fas fa-external-link-alt me-1"></i> Visit Site
            </a>
        </div>
        <div class="d-flex ms-auto">
            <!-- Simple dropdown without Bootstrap JS dependency -->
            <div class="user-dropdown">
                <a href="#" class="user-dropdown-toggle" id="userMenuToggle">
                    <i class="fas fa-user-circle me-2"></i><?php echo e(Auth::user()->name); ?>

                    <i class="fas fa-chevron-down ms-2 dropdown-arrow"></i>
                </a>
                <div class="user-dropdown-menu">
                    <div class="user-info">
                        <strong><?php echo e(Auth::user()->name); ?></strong><br>
                        <span class="text-muted"><?php echo e(Auth::user()->email); ?></span>
                    </div>
                    <div class="dropdown-divider"></div>
                    <a href="<?php echo e(route('admin.profile.edit')); ?>" class="user-dropdown-item">
                        <i class="fas fa-user me-2"></i>Profil
                    </a>
                    <a href="<?php echo e(route('admin.profile.change-password')); ?>" class="user-dropdown-item">
                        <i class="fas fa-key me-2"></i>Ganti Password
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="user-dropdown-item text-danger" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </div>
            </div>
        </div>
    </div>
</nav>

<style>
    /* Custom dropdown styles */
    .user-dropdown {
        position: relative;
        display: inline-block;
    }

    .user-dropdown-toggle {
        display: flex;
        align-items: center;
        color: #333;
        text-decoration: none;
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;
    }

    .user-dropdown-toggle:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .user-dropdown-menu {
        display: none;
        position: absolute;
        right: 0;
        top: 100%;
        min-width: 220px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        z-index: 1000;
        margin-top: 8px;
        padding: 8px 0;
    }

    .user-dropdown.active .user-dropdown-menu {
        display: block;
    }

    .dropdown-arrow {
        font-size: 0.75rem;
        transition: transform 0.2s ease;
    }

    .user-dropdown.active .dropdown-arrow {
        transform: rotate(180deg);
    }

    .user-info {
        padding: 8px 16px;
        font-size: 0.9rem;
        color: #666;
    }

    .user-dropdown-item {
        display: block;
        padding: 8px 16px;
        color: #333;
        text-decoration: none;
        font-size: 0.9rem;
    }

    .user-dropdown-item:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .user-dropdown-item.text-danger {
        color: #dc3545;
    }

    .dropdown-divider {
        height: 0;
        margin: 8px 0;
        overflow: hidden;
        border-top: 1px solid #e9ecef;
    }
</style>

<form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" class="d-none">
    <?php echo csrf_field(); ?>
</form>

<script>
    // Simple vanilla JavaScript dropdown toggle
    document.addEventListener('DOMContentLoaded', function() {
        const userDropdown = document.querySelector('.user-dropdown');
        const userMenuToggle = document.getElementById('userMenuToggle');

        if (userMenuToggle && userDropdown) {
            // Toggle dropdown on click
            userMenuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                userDropdown.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!userDropdown.contains(e.target)) {
                    userDropdown.classList.remove('active');
                }
            });
        }
    });
</script>
<?php /**PATH D:\IT\www\nurul-hayah-4\resources\views/admin/layouts/navbar.blade.php ENDPATH**/ ?>