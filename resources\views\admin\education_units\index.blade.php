@extends('admin.layouts.app')

@section('title', 'Education Units')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Education Units</h1>
        <a href="{{ route('admin.education-units.create') }}" class="btn btn-success">
            <i class="fas fa-plus"></i> Add New
        </a>
    </div>

    <div class="card">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {{ $activeTab == 'formal' ? 'active' : '' }}" href="{{ route('admin.education-units.index', ['tab' => 'formal']) }}">
                        Formal
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ $activeTab == 'non-formal' ? 'active' : '' }}" href="{{ route('admin.education-units.index', ['tab' => 'non-formal']) }}">
                        Non-Formal
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Name</th>
                            <th>Level</th>
                            <th>Order</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($educationUnits as $unit)
                            <tr>
                                <td>
                                    @if($unit->image)
                                        <img src="{{ asset('storage/' . $unit->image) }}" alt="{{ $unit->name }}" width="50" height="50" class="img-thumbnail">
                                    @else
                                        <span class="text-muted">No image</span>
                                    @endif
                                </td>
                                <td>{{ $unit->name }}</td>
                                <td>{{ $unit->level }}</td>
                                <td>{{ $unit->order }}</td>
                                <td>
                                    @if($unit->is_active)
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <span class="badge bg-danger">Inactive</span>
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('admin.education-units.show', $unit) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.education-units.edit', $unit) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.education-units.destroy', $unit) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger btn-delete" onclick="return confirm('Are you sure you want to delete this education unit?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center">No education units found.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection
