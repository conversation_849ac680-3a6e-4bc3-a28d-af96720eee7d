@extends('admin.layouts.app')

@section('title', 'Literature')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Literature</h1>
        <a href="{{ route('admin.artwork.literature.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Literature
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            @if($literature->isEmpty())
                <div class="alert alert-info">
                    No literature found. <a href="{{ route('admin.artwork.literature.create') }}">Create your first literature</a>.
                </div>
            @else
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Author</th>
                                <th>Type</th>
                                <th>Year</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($literature as $item)
                                <tr>
                                    <td>{{ $item->title }}</td>
                                    <td>{{ $item->author }}</td>
                                    <td>{{ $item->litType ? $item->litType->name : $item->type }}</td>
                                    <td>{{ $item->year ?: '-' }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.artwork.literature.edit', $item->id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <form action="{{ route('admin.artwork.literature.destroy', $item->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this literature?');">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i> Delete
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div class="mt-4">
                    {{ $literature->links() }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
