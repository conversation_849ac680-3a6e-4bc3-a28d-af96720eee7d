<?php

$target = '/home/<USER>/Laravel/storage/app/public';
$link = '/home/<USER>/public_html/storage';

if (is_link($link) || file_exists($link)) {
    echo "📁 Sudah ada folder atau link di: $link. Silakan hapus dulu jika ingin membuat ulang.";
} else {
    if (symlink($target, $link)) {
        echo "✅ Symlink berhasil dibuat dari $link ke $target";
    } else {
        echo "❌ Gagal membuat symlink. Mungkin karena izin atau fungsi symlink() dinonaktifkan.";
    }
}
