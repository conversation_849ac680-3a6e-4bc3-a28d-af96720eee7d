@extends('admin.layouts.app')

@section('title', 'View Video')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View Video</h1>
        <div>
            <a href="{{ route('admin.videos.edit', $video) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="{{ route('admin.videos.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Video Details</h5>
                </div>
                <div class="card-body">
                    <div class="ratio ratio-16x9 mb-4">
                        <iframe src="{{ $video->youtube_embed_url }}" title="{{ $video->title }}" allowfullscreen></iframe>
                    </div>
                    
                    <div class="mb-3">
                        <h5>{{ $video->title }}</h5>
                        <p class="text-muted">{{ $video->description }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-muted">English Version</h6>
                        <h5>{{ $video->title_en }}</h5>
                        <p class="text-muted">{{ $video->description_en }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>YouTube URL:</h6>
                        <a href="{{ $video->youtube_url }}" target="_blank" class="d-block text-break">
                            {{ $video->youtube_url }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Information</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>ID</span>
                            <span class="badge bg-secondary">{{ $video->id }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Status</span>
                            @if($video->is_active)
                                <span class="badge bg-success">Active</span>
                            @else
                                <span class="badge bg-secondary">Inactive</span>
                            @endif
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Display Order</span>
                            <span>{{ $video->order }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Created At</span>
                            <span>{{ $video->created_at->format('d M Y H:i') }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Last Updated</span>
                            <span>{{ $video->updated_at->format('d M Y H:i') }}</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <a href="{{ route('admin.videos.edit', $video) }}" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-edit me-1"></i> Edit Video
                    </a>
                    
                    <form action="{{ route('admin.videos.destroy', $video) }}" method="POST">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Are you sure you want to delete this video?')">
                            <i class="fas fa-trash me-1"></i> Delete Video
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
