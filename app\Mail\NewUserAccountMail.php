<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class NewUserAccountMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The user's username.
     *
     * @var string
     */
    public $username;

    /**
     * The user's email.
     *
     * @var string
     */
    public $email;

    /**
     * The user's password.
     *
     * @var string
     */
    public $password;

    /**
     * Create a new message instance.
     *
     * @param string $username
     * @param string $email
     * @param string $password
     * @return void
     */
    public function __construct($username, $email, $password)
    {
        $this->username = $username;
        $this->email = $email;
        $this->password = $password;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        $fromConfig = config('mail.from_addresses.notifications', config('mail.from'));

        return new Envelope(
            from: $fromConfig['address'],
            to: [$this->email],
            subject: __('emails.new_user_subject'),
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(
            view: 'emails.new-user-account',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
