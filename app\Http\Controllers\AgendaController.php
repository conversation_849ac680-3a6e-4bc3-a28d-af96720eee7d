<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Agenda;
use App\Models\Announcement;
use App\Models\News;

class AgendaController extends Controller
{
    /**
     * Display a listing of the agenda items.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get all upcoming agenda items
        $agendaItems = Agenda::upcoming()->paginate(10);

        // If no upcoming agenda items, get any active agenda items
        if ($agendaItems->isEmpty()) {
            $agendaItems = Agenda::active()->paginate(10);
        }

        return view('public.agenda.index', compact('agendaItems'));
    }

    /**
     * Display the specified agenda item.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $agendaItem = Agenda::where('is_active', true)
            ->findOrFail($id);

        // Get related agenda items
        $relatedAgendaItems = Agenda::where('is_active', true)
            ->where('id', '!=', $agendaItem->id)
            ->orderBy('date', 'asc')
            ->take(3)
            ->get();

        // Get latest announcements
        $latestAnnouncements = \App\Models\Announcement::where('is_active', true)
            ->orderBy('start_date', 'desc')
            ->take(3)
            ->get();

        // Get latest news
        $latestNews = \App\Models\News::published()
            ->orderBy('published_at', 'desc')
            ->take(3)
            ->get();

        return view('public.agenda.show', compact(
            'agendaItem',
            'relatedAgendaItems',
            'latestAnnouncements',
            'latestNews'
        ));
    }
}
