<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Setting;

class ConfigurationController extends Controller
{
    /**
     * Display the configuration page.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        // Get all settings
        $settings = Setting::pluck('value', 'key')->toArray();

        // Get the active tab from the request or default to 'general'
        $activeTab = $request->query('tab', 'general');

        // If user is operator and trying to access a tab other than general, redirect to general tab
        if (auth()->user()->isOperator() && $activeTab !== 'general') {
            return redirect()->route('admin.settings.configuration.operator.index', ['tab' => 'general']);
        }

        return view('admin.settings.configuration.index', compact('settings', 'activeTab'));
    }

    /**
     * Update the configuration settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        // Get all form data except CSRF token and config_type
        $data = $request->except(['_token', 'config_type']);
        $configType = $request->input('config_type', 'general');

        // Group settings by type
        $group = $configType;

        // Update or create settings for all fields
        foreach ($data as $key => $value) {
            Setting::updateOrCreate(
                ['key' => $key],
                [
                    'value' => $value,
                    'group' => $group
                ]
            );
        }

        // Redirect to the appropriate tab
        return redirect()->route('admin.settings.configuration.operator.index', ['tab' => $configType])
            ->with('success', 'Configuration updated successfully.');
    }
}
