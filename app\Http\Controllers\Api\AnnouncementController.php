<?php

namespace App\Http\Controllers\Api;

use App\Models\Announcement;
use App\Services\WebhookService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AnnouncementController extends BaseApiController
{
    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\WebhookService  $webhookService
     * @return void
     */
    public function __construct(WebhookService $webhookService)
    {
        parent::__construct($webhookService);
        $this->model = new Announcement();
        $this->modelName = 'announcement';
        
        $this->storeRules = [
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'content' => 'required|string',
            'content_en' => 'required|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'order' => 'nullable|integer',
            'is_featured' => 'nullable|boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'file' => 'nullable|file|mimes:pdf|max:10240',
        ];
        
        $this->updateRules = [
            'title' => 'sometimes|required|string|max:255',
            'title_en' => 'sometimes|required|string|max:255',
            'content' => 'sometimes|required|string',
            'content_en' => 'sometimes|required|string',
            'start_date' => 'sometimes|required|date',
            'end_date' => 'sometimes|required|date|after_or_equal:start_date',
            'order' => 'nullable|integer',
            'is_featured' => 'nullable|boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'file' => 'nullable|file|mimes:pdf|max:10240',
        ];
    }

    /**
     * Display a listing of the announcements.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $announcements = Announcement::where('is_active', true)
            ->orderBy('order', 'asc')
            ->get();
        
        return response()->json([
            'success' => true,
            'data' => $announcements,
        ]);
    }

    /**
     * Display the specified announcement.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $announcement = Announcement::where('is_active', true)
            ->findOrFail($id);
        
        return response()->json([
            'success' => true,
            'data' => $announcement,
        ]);
    }

    /**
     * Store a newly created announcement in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), $this->storeRules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $data = $request->all();

        // Set default order if not provided
        if (!isset($data['order'])) {
            $maxOrder = Announcement::max('order');
            $data['order'] = $maxOrder ? $maxOrder + 1 : 1;
        }

        // Set is_active to true by default
        $data['is_active'] = true;

        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image'] = $this->model->handleImageUpload($request->file('image'), 'image');
        }

        // Handle file upload (PDF)
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('announcements/files', $fileName, 'public');
            $data['file'] = $filePath;
        }

        $announcement = Announcement::create($data);

        // Dispatch webhook event
        $this->webhookService->dispatchEvent('announcement.created', $announcement->toArray());

        return response()->json([
            'success' => true,
            'message' => 'Announcement created successfully',
            'data' => $announcement,
        ], 201);
    }

    /**
     * Update the specified announcement in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $announcement = Announcement::findOrFail($id);

        $validator = Validator::make($request->all(), $this->updateRules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $data = $request->all();

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image first
            $announcement->deleteImage('image');

            // Upload new image
            $data['image'] = $announcement->handleImageUpload($request->file('image'), 'image');
        }

        // Handle file upload (PDF)
        if ($request->hasFile('file')) {
            // Delete old file if exists
            if ($announcement->file) {
                \Illuminate\Support\Facades\Storage::disk('public')->delete($announcement->file);
            }

            // Upload new file
            $file = $request->file('file');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('announcements/files', $fileName, 'public');
            $data['file'] = $filePath;
        }

        $announcement->update($data);

        // Dispatch webhook event
        $this->webhookService->dispatchEvent('announcement.updated', $announcement->toArray());

        return response()->json([
            'success' => true,
            'message' => 'Announcement updated successfully',
            'data' => $announcement,
        ]);
    }

    /**
     * Remove the specified announcement from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $announcement = Announcement::findOrFail($id);
        $announcement->delete();

        // Dispatch webhook event
        $this->webhookService->dispatchEvent('announcement.deleted', ['id' => $id]);

        return response()->json([
            'success' => true,
            'message' => 'Announcement deleted successfully',
        ]);
    }
}
