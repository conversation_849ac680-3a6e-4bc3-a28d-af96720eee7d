<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Contact extends Model
{
    protected $fillable = [
        'name',
        'email',
        'phone',
        'subject',
        'message',
        'is_read',
        'read_at',
        'reply_message',
        'replied_at',
        'replied_by',
    ];

    protected $casts = [
        'is_read' => 'boolean',
        'read_at' => 'datetime',
        'replied_at' => 'datetime',
    ];

    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    public function markAsRead()
    {
        $this->update([
            'is_read' => true,
            'read_at' => now(),
        ]);
    }

    /**
     * Get the user who replied to this contact.
     */
    public function repliedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'replied_by');
    }

    /**
     * Check if the contact has been replied to.
     *
     * @return bool
     */
    public function hasReply()
    {
        return !is_null($this->reply_message);
    }
}
