@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? 'Visi & Misi' : 'Vision & Mission')

@push('styles')
<style>
    .motto-content {
        font-weight: 500;
        font-size: 1.3rem;
        color: #198754;
    }

    .motto-text i {
        color: #198754;
        font-size: 1.2rem;
    }
</style>
@endpush

@section('content')
    <!-- <PERSON>er -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? 'Visi & Misi' : 'Vision & Mission' }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('profile') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Profil' : 'Profile' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? 'Visi & Misi' : 'Vision & Mission' }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Motto Section -->
    <section class="section-padding">
        <div class="container">
            <div class="section-title text-center" data-aos="fade-up">
                <h2>{{ app()->getLocale() == 'id' ? 'Motto' : 'Motto' }}</h2>
                <p>{{ app()->getLocale() == 'id' ? 'Semangat dan nilai-nilai Pondok Pesantren Nurul Hayah 4' : 'Spirit and values of Nurul Hayah 4 Islamic Boarding School' }}</p>
            </div>

            @if($motto)
            <div class="row justify-content-center">
                <div class="col-lg-8 mb-5" data-aos="fade-up">
                    <div class="card border-0 shadow-sm text-center motto-card">
                        <div class="card-body p-5">

                            <div class="card-text motto-text mt-2">
                                <div class="text-success">
                                    <i class="fas fa-quote-left me-2"></i>
                                    <span class="motto-content">{!! app()->getLocale() == 'id' ? $motto->content : $motto->content_en !!}</span>
                                    <i class="fas fa-quote-right ms-2"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </section>

    <!-- Vision & Mission Content -->
    <section class="section-padding bg-light">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>{{ app()->getLocale() == 'id' ? 'Visi & Misi' : 'Vision & Mission' }}</h2>
                <p>{{ app()->getLocale() == 'id' ? 'Tujuan dan arah pengembangan Pondok Pesantren Nurul Hayah 4' : 'Goals and development direction of Nurul Hayah 4 Islamic Boarding School' }}</p>
            </div>

            <div class="row">
                @if($vision)
                <div class="col-lg-12 mb-5" data-aos="fade-up" data-aos-delay="100">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-4">
                            <h2 class="card-title text-success mb-4">{{ app()->getLocale() == 'id' ? $vision->title : $vision->title_en }}</h2>
                            <div class="card-text">
                                {!! app()->getLocale() == 'id' ? $vision->content : $vision->content_en !!}
                            </div>
                        </div>
                    </div>
                </div>
                @endif

                @if($mission)
                <div class="col-lg-12 mb-5" data-aos="fade-up" data-aos-delay="200">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-4">
                            <h2 class="card-title text-success mb-4">{{ app()->getLocale() == 'id' ? $mission->title : $mission->title_en }}</h2>
                            <div class="card-text">
                                {!! app()->getLocale() == 'id' ? $mission->content : $mission->content_en !!}
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="section-padding">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 text-center" data-aos="fade-up">
                    <h2 class="mb-4">{{ app()->getLocale() == 'id' ? 'Bergabunglah dengan Kami' : 'Join Us' }}</h2>
                    <p class="mb-4">{{ app()->getLocale() == 'id' ? 'Jadilah bagian dari keluarga besar Pondok Pesantren Nurul Hayah 4 dan raih masa depan yang cerah.' : 'Be a part of the Nurul Hayah 4 Islamic Boarding School family and achieve a bright future.' }}</p>
                    <a href="{{ route('registration') }}" class="btn btn-success btn-lg">{{ app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now' }}</a>
                </div>
            </div>
        </div>
    </section>
@endsection
