<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Literature;
use App\Models\LitType;
use Illuminate\Http\Request;

class LiteratureController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $literature = Literature::with('litType')->latest()->paginate(10);
        return view('admin.artwork.literature.index', compact('literature'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $types = LitType::where('is_active', true)->orderBy('name')->get();
        return view('admin.artwork.literature.create', compact('types'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'content' => 'required|string',
            'content_en' => 'required|string',
            'author' => 'nullable|string|max:255',
            'author_en' => 'nullable|string|max:255',
            'year' => 'nullable|integer',
            'type' => 'nullable|string|max:255|exists:lit_type,slug',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'file' => 'nullable|file|mimes:pdf,doc,docx|max:10240',
        ]);

        $data = $request->all();

        // Set is_active to true by default
        $data['is_active'] = true;

        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image'] = Literature::make()->handleImageUpload($request->file('image'));
        }

        // Handle file upload
        if ($request->hasFile('file')) {
            $data['file'] = Literature::make()->handleFileUpload($request->file('file'));
        }

        Literature::create($data);

        return redirect()->route('admin.artwork.literature.index')
            ->with('success', 'Literature created successfully.');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $literature = Literature::findOrFail($id);
        $types = LitType::where('is_active', true)->orderBy('name')->get();
        return view('admin.artwork.literature.edit', compact('literature', 'types'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $literature = Literature::findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'content' => 'required|string',
            'content_en' => 'required|string',
            'author' => 'nullable|string|max:255',
            'author_en' => 'nullable|string|max:255',
            'year' => 'nullable|integer',
            'type' => 'nullable|string|max:255|exists:lit_type,slug',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'file' => 'nullable|file|mimes:pdf,doc,docx|max:10240',
        ]);

        $data = $request->all();

        // Keep is_active status unchanged
        $data['is_active'] = $literature->is_active;

        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image'] = $literature->handleImageUpload($request->file('image'));
        }

        // Handle file upload
        if ($request->hasFile('file')) {
            $data['file'] = $literature->handleFileUpload($request->file('file'));
        }

        $literature->update($data);

        return redirect()->route('admin.artwork.literature.index')
            ->with('success', 'Literature updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $literature = Literature::findOrFail($id);

        // Delete the image and file
        $literature->deleteImage();
        $literature->deleteFile();

        // Delete the record
        $literature->delete();

        return redirect()->route('admin.artwork.literature.index')
            ->with('success', 'Literature deleted successfully.');
    }
}
