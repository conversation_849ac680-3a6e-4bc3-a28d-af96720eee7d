@extends('admin.layouts.app')

@section('title', 'Edit Leader')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Edit Leader</h1>
        <div>
            <a href="{{ route('admin.leaders.show', $leader) }}" class="btn btn-info">
                <i class="fas fa-eye"></i> View
            </a>
            <a href="{{ route('admin.leaders.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="{{ route('admin.leaders.update', $leader) }}" method="POST" enctype="multipart/form-data" id="leaderForm">
                @csrf
                @method('PUT')

                <!-- Section tabs -->
                <ul class="nav nav-tabs mb-4" id="sectionTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="identity-tab" data-bs-toggle="tab" data-bs-target="#identity-section" type="button" role="tab" aria-controls="identity-section" aria-selected="true">Identity</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="background-tab" data-bs-toggle="tab" data-bs-target="#background-section" type="button" role="tab" aria-controls="background-section" aria-selected="false">Background</button>
                    </li>
                </ul>

                <!-- Tab content -->
                <div class="tab-content" id="sectionTabsContent">
                    <!-- Section 1: Identity -->
                    <div class="tab-pane fade show active" id="identity-section" role="tabpanel" aria-labelledby="identity-tab">
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="name" class="form-label">Name</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $leader->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <input type="hidden" id="name_en" name="name_en" value="{{ old('name_en', $leader->name_en ?? $leader->name) }}">
                                <small class="text-muted">Name will be displayed the same in both Indonesian and English.</small>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="position" class="form-label">Position (Indonesian)</label>
                                <input type="text" class="form-control @error('position') is-invalid @enderror" id="position" name="position" value="{{ old('position', $leader->position) }}" required>
                                @error('position')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="position_en" class="form-label">Position (English)</label>
                                <input type="text" class="form-control @error('position_en') is-invalid @enderror" id="position_en" name="position_en" value="{{ old('position_en', $leader->position_en) }}">
                                @error('position_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="motto" class="form-label">Motto (Indonesian)</label>
                                <input type="text" class="form-control @error('motto') is-invalid @enderror" id="motto" name="motto" value="{{ old('motto', $leader->motto) }}">
                                @error('motto')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="motto_en" class="form-label">Motto (English)</label>
                                <input type="text" class="form-control @error('motto_en') is-invalid @enderror" id="motto_en" name="motto_en" value="{{ old('motto_en', $leader->motto_en) }}">
                                @error('motto_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="bio" class="form-label">Bio (Indonesian) <small class="text-muted">(max 100 words)</small></label>
                                <textarea class="form-control summernote @error('bio') is-invalid @enderror" id="bio" name="bio" rows="4">{{ old('bio', $leader->bio) }}</textarea>
                                <small class="form-text text-muted">Please keep your bio concise, maximum 100 words.</small>
                                @error('bio')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="bio_en" class="form-label">Bio (English) <small class="text-muted">(max 100 words)</small></label>
                                <textarea class="form-control summernote @error('bio_en') is-invalid @enderror" id="bio_en" name="bio_en" rows="4">{{ old('bio_en', $leader->bio_en) }}</textarea>
                                <small class="form-text text-muted">Please keep your bio concise, maximum 100 words.</small>
                                @error('bio_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email', $leader->email) }}">
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="phone" class="form-label">Phone</label>
                                <input type="text" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" value="{{ old('phone', $leader->phone) }}">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="image" class="form-label">Image</label>
                                <input type="file" class="form-control @error('image') is-invalid @enderror" id="image" name="image" accept="image/*">
                                <small class="text-muted">Recommended size: 400x400 pixels. Max size: 2MB. Leave empty to keep current image.</small>
                                @error('image')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror

                                @if($leader->image)
                                    <div class="mt-2">
                                        <p>Current image:</p>
                                        <img src="{{ asset('storage/' . $leader->image) }}" alt="{{ $leader->name }}" class="img-thumbnail" style="max-height: 150px;">
                                    </div>
                                @endif
                            </div>

                            <div class="col-md-6">
                                <label for="order" class="form-label">Display Order</label>
                                <input type="number" class="form-control @error('order') is-invalid @enderror" id="order" name="order" value="{{ old('order', $leader->order) }}" min="1">
                                <small class="text-muted">Lower numbers will be displayed first.</small>
                                @error('order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <h4 class="mt-4 mb-3">Social Media</h4>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="social_facebook" class="form-label">Facebook</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fab fa-facebook-f"></i></span>
                                    <input type="text" class="form-control @error('social_facebook') is-invalid @enderror" id="social_facebook" name="social_facebook" value="{{ old('social_facebook', $leader->social_facebook) }}" placeholder="https://facebook.com/username">
                                </div>
                                @error('social_facebook')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="social_twitter" class="form-label">X</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fab fa-x-twitter"></i></span>
                                    <input type="text" class="form-control @error('social_twitter') is-invalid @enderror" id="social_twitter" name="social_twitter" value="{{ old('social_twitter', $leader->social_twitter) }}" placeholder="https://x.com/username">
                                </div>
                                @error('social_twitter')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="social_instagram" class="form-label">Instagram</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fab fa-instagram"></i></span>
                                    <input type="text" class="form-control @error('social_instagram') is-invalid @enderror" id="social_instagram" name="social_instagram" value="{{ old('social_instagram', $leader->social_instagram) }}" placeholder="https://instagram.com/username">
                                </div>
                                @error('social_instagram')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="social_linkedin" class="form-label">Telegram</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fab fa-telegram"></i></span>
                                    <input type="text" class="form-control @error('social_linkedin') is-invalid @enderror" id="social_linkedin" name="social_linkedin" value="{{ old('social_linkedin', $leader->social_linkedin) }}" placeholder="https://t.me/username">
                                </div>
                                @error('social_linkedin')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="mt-3">
                                    <img id="image-preview" src="#" alt="Preview" class="img-thumbnail d-none" style="max-height: 300px;">
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <button type="button" class="btn btn-primary" id="goToBackgroundBtn">
                                Next <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Section 2: Background -->
                    <div class="tab-pane fade" id="background-section" role="tabpanel" aria-labelledby="background-tab">
                        <!-- Education History -->
                        <h4 class="mb-3">Education History</h4>
                        <div id="education-container">
                            @if(old('education_history') || (isset($leader->education_history) && $leader->education_history))
                                @php
                                    $educationHistory = old('education_history') ?? json_decode($leader->education_history, true) ?? [];
                                    $educationHistoryEn = old('education_history_en') ?? json_decode($leader->education_history_en, true) ?? [];
                                @endphp

                                @foreach($educationHistory as $index => $education)
                                    <div class="education-item card mb-3">
                                        <div class="card-body">
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label class="form-label">Institution (Indonesian)</label>
                                                    <input type="text" class="form-control" name="education_history[{{ $index }}][institution]" value="{{ $education['institution'] ?? '' }}">
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">Institution (English)</label>
                                                    <input type="text" class="form-control" name="education_history_en[{{ $index }}][institution]" value="{{ $educationHistoryEn[$index]['institution'] ?? '' }}">
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label class="form-label">Degree (Indonesian)</label>
                                                    <input type="text" class="form-control" name="education_history[{{ $index }}][degree]" value="{{ $education['degree'] ?? '' }}">
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">Degree (English)</label>
                                                    <input type="text" class="form-control" name="education_history_en[{{ $index }}][degree]" value="{{ $educationHistoryEn[$index]['degree'] ?? '' }}">
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-md-12">
                                                    <label class="form-label">Year</label>
                                                    <input type="text" class="form-control" name="education_history[{{ $index }}][year]" value="{{ $education['year'] ?? '' }}" placeholder="e.g., 2010-2014">
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-md-12">
                                                    <label class="form-label">Location</label>
                                                    <input type="text" class="form-control" name="education_history[{{ $index }}][location]" value="{{ $education['location'] ?? '' }}">
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-sm btn-danger remove-education" style="{{ count($educationHistory) <= 1 ? 'display: none;' : '' }}">Remove</button>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="education-item card mb-3">
                                    <div class="card-body">
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label class="form-label">Institution (Indonesian)</label>
                                                <input type="text" class="form-control" name="education_history[0][institution]" value="">
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">Institution (English)</label>
                                                <input type="text" class="form-control" name="education_history_en[0][institution]" value="">
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label class="form-label">Degree (Indonesian)</label>
                                                <input type="text" class="form-control" name="education_history[0][degree]" value="">
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">Degree (English)</label>
                                                <input type="text" class="form-control" name="education_history_en[0][degree]" value="">
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-12">
                                                <label class="form-label">Year</label>
                                                <input type="text" class="form-control" name="education_history[0][year]" value="" placeholder="e.g., 2010-2014">
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-12">
                                                <label class="form-label">Location</label>
                                                <input type="text" class="form-control" name="education_history[0][location]" value="">
                                            </div>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-danger remove-education" style="display: none;">Remove</button>
                                    </div>
                                </div>
                            @endif
                        </div>
                        <button type="button" class="btn btn-sm btn-success mb-4" id="add-education">
                            <i class="fas fa-plus"></i> Add Education
                        </button>

                        <!-- Achievements -->
                        <h4 class="mb-3">Achievements</h4>
                        <div id="achievements-container">
                            @if(old('achievements') || (isset($leader->achievements) && $leader->achievements))
                                @php
                                    $achievements = old('achievements') ?? json_decode($leader->achievements, true) ?? [];
                                    $achievementsEn = old('achievements_en') ?? json_decode($leader->achievements_en, true) ?? [];
                                @endphp

                                @foreach($achievements as $index => $achievement)
                                    <div class="achievement-item card mb-3">
                                        <div class="card-body">
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label class="form-label">Title (Indonesian)</label>
                                                    <input type="text" class="form-control" name="achievements[{{ $index }}][title]" value="{{ $achievement['title'] ?? '' }}">
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">Title (English)</label>
                                                    <input type="text" class="form-control" name="achievements_en[{{ $index }}][title]" value="{{ $achievementsEn[$index]['title'] ?? '' }}">
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label class="form-label">Description (Indonesian)</label>
                                                    <textarea class="form-control" name="achievements[{{ $index }}][description]" rows="2">{{ $achievement['description'] ?? '' }}</textarea>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">Description (English)</label>
                                                    <textarea class="form-control" name="achievements_en[{{ $index }}][description]" rows="2">{{ $achievementsEn[$index]['description'] ?? '' }}</textarea>
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-md-12">
                                                    <label class="form-label">Year</label>
                                                    <input type="text" class="form-control" name="achievements[{{ $index }}][year]" value="{{ $achievement['year'] ?? '' }}">
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-sm btn-danger remove-achievement" style="{{ count($achievements) <= 1 ? 'display: none;' : '' }}">Remove</button>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="achievement-item card mb-3">
                                    <div class="card-body">
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label class="form-label">Title (Indonesian)</label>
                                                <input type="text" class="form-control" name="achievements[0][title]" value="">
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">Title (English)</label>
                                                <input type="text" class="form-control" name="achievements_en[0][title]" value="">
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label class="form-label">Description (Indonesian)</label>
                                                <textarea class="form-control" name="achievements[0][description]" rows="2"></textarea>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">Description (English)</label>
                                                <textarea class="form-control" name="achievements_en[0][description]" rows="2"></textarea>
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-12">
                                                <label class="form-label">Year</label>
                                                <input type="text" class="form-control" name="achievements[0][year]" value="">
                                            </div>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-danger remove-achievement" style="display: none;">Remove</button>
                                    </div>
                                </div>
                            @endif
                        </div>
                        <button type="button" class="btn btn-sm btn-success mb-4" id="add-achievement">
                            <i class="fas fa-plus"></i> Add Achievement
                        </button>

                        <!-- Work Experience -->
                        <h4 class="mb-3">Work Experience</h4>
                        <div id="work-experience-container">
                            @if(old('work_experience') || (isset($leader->work_experience) && $leader->work_experience))
                                @php
                                    $workExperience = old('work_experience') ?? json_decode($leader->work_experience, true) ?? [];
                                    $workExperienceEn = old('work_experience_en') ?? json_decode($leader->work_experience_en, true) ?? [];
                                @endphp

                                @foreach($workExperience as $index => $experience)
                                    <div class="work-experience-item card mb-3">
                                        <div class="card-body">
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label class="form-label">Company/Organization (Indonesian)</label>
                                                    <input type="text" class="form-control" name="work_experience[{{ $index }}][company]" value="{{ $experience['company'] ?? '' }}">
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">Company/Organization (English)</label>
                                                    <input type="text" class="form-control" name="work_experience_en[{{ $index }}][company]" value="{{ $workExperienceEn[$index]['company'] ?? '' }}">
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label class="form-label">Position (Indonesian)</label>
                                                    <input type="text" class="form-control" name="work_experience[{{ $index }}][position]" value="{{ $experience['position'] ?? '' }}">
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">Position (English)</label>
                                                    <input type="text" class="form-control" name="work_experience_en[{{ $index }}][position]" value="{{ $workExperienceEn[$index]['position'] ?? '' }}">
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-md-12">
                                                    <label class="form-label">Period</label>
                                                    <input type="text" class="form-control" name="work_experience[{{ $index }}][period]" value="{{ $experience['period'] ?? '' }}" placeholder="e.g., 2015-2020">
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-md-12">
                                                    <label class="form-label">Location</label>
                                                    <input type="text" class="form-control" name="work_experience[{{ $index }}][location]" value="{{ $experience['location'] ?? '' }}">
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label class="form-label">Description (Indonesian)</label>
                                                    <textarea class="form-control" name="work_experience[{{ $index }}][description]" rows="2">{{ $experience['description'] ?? '' }}</textarea>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">Description (English)</label>
                                                    <textarea class="form-control" name="work_experience_en[{{ $index }}][description]" rows="2">{{ $workExperienceEn[$index]['description'] ?? '' }}</textarea>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-sm btn-danger remove-work-experience" style="{{ count($workExperience) <= 1 ? 'display: none;' : '' }}">Remove</button>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="work-experience-item card mb-3">
                                    <div class="card-body">
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label class="form-label">Company/Organization (Indonesian)</label>
                                                <input type="text" class="form-control" name="work_experience[0][company]" value="">
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">Company/Organization (English)</label>
                                                <input type="text" class="form-control" name="work_experience_en[0][company]" value="">
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label class="form-label">Position (Indonesian)</label>
                                                <input type="text" class="form-control" name="work_experience[0][position]" value="">
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">Position (English)</label>
                                                <input type="text" class="form-control" name="work_experience_en[0][position]" value="">
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-12">
                                                <label class="form-label">Period</label>
                                                <input type="text" class="form-control" name="work_experience[0][period]" value="" placeholder="e.g., 2015-2020">
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-12">
                                                <label class="form-label">Location</label>
                                                <input type="text" class="form-control" name="work_experience[0][location]" value="">
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label class="form-label">Description (Indonesian)</label>
                                                <textarea class="form-control" name="work_experience[0][description]" rows="2"></textarea>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">Description (English)</label>
                                                <textarea class="form-control" name="work_experience_en[0][description]" rows="2"></textarea>
                                            </div>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-danger remove-work-experience" style="display: none;">Remove</button>
                                    </div>
                                </div>
                            @endif
                        </div>
                        <button type="button" class="btn btn-sm btn-success mb-4" id="add-work-experience">
                            <i class="fas fa-plus"></i> Add Work Experience
                        </button>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-between mt-4">
                            <button type="button" class="btn btn-secondary" id="backToIdentityBtn">
                                <i class="fas fa-arrow-left"></i> Back
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // Image preview
    document.getElementById('image').addEventListener('change', function(e) {
        const preview = document.getElementById('image-preview');
        const file = e.target.files[0];

        if (file) {
            const reader = new FileReader();

            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.classList.remove('d-none');
            }

            reader.readAsDataURL(file);
        } else {
            preview.src = '#';
            preview.classList.add('d-none');
        }
    });

    // Initialize Summernote for bio fields with word limit
    $(document).ready(function() {
        // Special initialization for bio fields with word limit
        $('#bio, #bio_en').each(function() {
            $(this).summernote({
                placeholder: 'Write your bio here (max 100 words)...',
                tabsize: 2,
                height: 200,
                toolbar: [
                    ['font', ['bold', 'underline', 'italic', 'clear']],
                    ['fontname', ['fontname']],
                    ['fontsize', ['fontsize']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['table', ['table']],
                    ['insert', ['link', 'picture']]
                ],
                callbacks: {
                    onKeydown: function(e) {
                        var t = e.currentTarget.innerText;
                        var wordCount = t.trim().split(/\s+/).length;

                        // If we're over the word limit and not pressing backspace/delete
                        if (wordCount >= 100 && e.keyCode !== 8 && e.keyCode !== 46) {
                            // Check if it's a word-creating key (space or enter)
                            if (e.keyCode === 32 || e.keyCode === 13) {
                                e.preventDefault();
                                return false;
                            }
                        }
                    },
                    onKeyup: function(e) {
                        updateWordCount(this);
                    },
                    onPaste: function(e) {
                        setTimeout(function() {
                            updateWordCount(this);
                        }.bind(this), 100);
                    },
                    onInit: function() {
                        updateWordCount(this);
                    }
                }
            });
        });

        // Function to update word count display
        function updateWordCount(editor) {
            var text = $(editor).summernote('code');
            var plainText = $('<div>').html(text).text();
            var words = plainText.trim().split(/\s+/);
            var wordCount = plainText.trim() === '' ? 0 : words.length;

            // Find the counter element
            var $counter = $(editor).siblings('.word-counter');
            if ($counter.length === 0) {
                // Create counter if it doesn't exist
                $counter = $('<div class="word-counter mt-1 text-muted small"></div>');
                $(editor).after($counter);
            }

            // Update counter text and style
            $counter.text('Word count: ' + wordCount + ' / 100');

            // Add warning style if approaching limit
            if (wordCount > 90) {
                $counter.removeClass('text-muted').addClass('text-warning');
            } else if (wordCount >= 100) {
                $counter.removeClass('text-muted text-warning').addClass('text-danger');
            } else {
                $counter.removeClass('text-warning text-danger').addClass('text-muted');
            }
        }

        // Initialize word counters
        setTimeout(function() {
            $('#bio, #bio_en').each(function() {
                updateWordCount(this);
            });
        }, 500);
    });

    // Section navigation
    document.getElementById('goToBackgroundBtn').addEventListener('click', function() {
        const backgroundTab = document.getElementById('background-tab');
        bootstrap.Tab.getOrCreateInstance(backgroundTab).show();
    });

    document.getElementById('backToIdentityBtn').addEventListener('click', function() {
        const identityTab = document.getElementById('identity-tab');
        bootstrap.Tab.getOrCreateInstance(identityTab).show();
    });

    // Sync name to English version
    document.getElementById('name').addEventListener('input', function() {
        document.getElementById('name_en').value = this.value;
    });

    // Dynamic Education History fields
    let educationIndex = {{ isset($leader->education_history) && is_array(json_decode($leader->education_history, true)) ? count(json_decode($leader->education_history, true)) - 1 : 0 }};

    document.getElementById('add-education').addEventListener('click', function() {
        educationIndex++;
        const template = `
            <div class="education-item card mb-3">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Institution (Indonesian)</label>
                            <input type="text" class="form-control" name="education_history[${educationIndex}][institution]">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Institution (English)</label>
                            <input type="text" class="form-control" name="education_history_en[${educationIndex}][institution]">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Degree (Indonesian)</label>
                            <input type="text" class="form-control" name="education_history[${educationIndex}][degree]">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Degree (English)</label>
                            <input type="text" class="form-control" name="education_history_en[${educationIndex}][degree]">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label">Year</label>
                            <input type="text" class="form-control" name="education_history[${educationIndex}][year]" placeholder="e.g., 2010-2014">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label">Location</label>
                            <input type="text" class="form-control" name="education_history[${educationIndex}][location]">
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn-danger remove-education">Remove</button>
                </div>
            </div>
        `;

        document.getElementById('education-container').insertAdjacentHTML('beforeend', template);

        // Show remove buttons if there's more than one education item
        if (document.querySelectorAll('.education-item').length > 1) {
            document.querySelectorAll('.remove-education').forEach(btn => {
                btn.style.display = 'inline-block';
            });
        }
    });

    // Remove education item
    document.getElementById('education-container').addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-education')) {
            e.target.closest('.education-item').remove();

            // Hide remove buttons if there's only one education item left
            if (document.querySelectorAll('.education-item').length <= 1) {
                document.querySelectorAll('.remove-education').forEach(btn => {
                    btn.style.display = 'none';
                });
            }
        }
    });

    // Dynamic Achievements fields
    let achievementIndex = {{ isset($leader->achievements) && is_array(json_decode($leader->achievements, true)) ? count(json_decode($leader->achievements, true)) - 1 : 0 }};

    document.getElementById('add-achievement').addEventListener('click', function() {
        achievementIndex++;
        const template = `
            <div class="achievement-item card mb-3">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Title (Indonesian)</label>
                            <input type="text" class="form-control" name="achievements[${achievementIndex}][title]">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Title (English)</label>
                            <input type="text" class="form-control" name="achievements_en[${achievementIndex}][title]">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Description (Indonesian)</label>
                            <textarea class="form-control" name="achievements[${achievementIndex}][description]" rows="2"></textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Description (English)</label>
                            <textarea class="form-control" name="achievements_en[${achievementIndex}][description]" rows="2"></textarea>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label">Year</label>
                            <input type="text" class="form-control" name="achievements[${achievementIndex}][year]">
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn-danger remove-achievement">Remove</button>
                </div>
            </div>
        `;

        document.getElementById('achievements-container').insertAdjacentHTML('beforeend', template);

        // Show remove buttons if there's more than one achievement item
        if (document.querySelectorAll('.achievement-item').length > 1) {
            document.querySelectorAll('.remove-achievement').forEach(btn => {
                btn.style.display = 'inline-block';
            });
        }
    });

    // Remove achievement item
    document.getElementById('achievements-container').addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-achievement')) {
            e.target.closest('.achievement-item').remove();

            // Hide remove buttons if there's only one achievement item left
            if (document.querySelectorAll('.achievement-item').length <= 1) {
                document.querySelectorAll('.remove-achievement').forEach(btn => {
                    btn.style.display = 'none';
                });
            }
        }
    });

    // Dynamic Work Experience fields
    let workExperienceIndex = {{ isset($leader->work_experience) && is_array(json_decode($leader->work_experience, true)) ? count(json_decode($leader->work_experience, true)) - 1 : 0 }};

    document.getElementById('add-work-experience').addEventListener('click', function() {
        workExperienceIndex++;
        const template = `
            <div class="work-experience-item card mb-3">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Company/Organization (Indonesian)</label>
                            <input type="text" class="form-control" name="work_experience[${workExperienceIndex}][company]">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Company/Organization (English)</label>
                            <input type="text" class="form-control" name="work_experience_en[${workExperienceIndex}][company]">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Position (Indonesian)</label>
                            <input type="text" class="form-control" name="work_experience[${workExperienceIndex}][position]">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Position (English)</label>
                            <input type="text" class="form-control" name="work_experience_en[${workExperienceIndex}][position]">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label">Period</label>
                            <input type="text" class="form-control" name="work_experience[${workExperienceIndex}][period]" placeholder="e.g., 2015-2020">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label">Location</label>
                            <input type="text" class="form-control" name="work_experience[${workExperienceIndex}][location]">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Description (Indonesian)</label>
                            <textarea class="form-control" name="work_experience[${workExperienceIndex}][description]" rows="2"></textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Description (English)</label>
                            <textarea class="form-control" name="work_experience_en[${workExperienceIndex}][description]" rows="2"></textarea>
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn-danger remove-work-experience">Remove</button>
                </div>
            </div>
        `;

        document.getElementById('work-experience-container').insertAdjacentHTML('beforeend', template);

        // Show remove buttons if there's more than one work experience item
        if (document.querySelectorAll('.work-experience-item').length > 1) {
            document.querySelectorAll('.remove-work-experience').forEach(btn => {
                btn.style.display = 'inline-block';
            });
        }
    });

    // Remove work experience item
    document.getElementById('work-experience-container').addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-work-experience')) {
            e.target.closest('.work-experience-item').remove();

            // Hide remove buttons if there's only one work experience item left
            if (document.querySelectorAll('.work-experience-item').length <= 1) {
                document.querySelectorAll('.remove-work-experience').forEach(btn => {
                    btn.style.display = 'none';
                });
            }
        }
    });
</script>
@endpush
