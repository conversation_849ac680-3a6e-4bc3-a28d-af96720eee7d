<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\TelegramBotService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TelegramWebhookController extends Controller
{
    /**
     * The Telegram Bot service instance.
     *
     * @var \App\Services\TelegramBotService
     */
    protected $telegramService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\TelegramBotService  $telegramService
     * @return void
     */
    public function __construct(TelegramBotService $telegramService)
    {
        $this->telegramService = $telegramService;
    }

    /**
     * Handle the incoming webhook request from Telegram.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function handle(Request $request)
    {
        try {
            // Log the incoming webhook with detailed information
            $update = $request->all();

            Log::info('Telegram webhook received', [
                'has_message' => isset($update['message']),
                'has_callback_query' => isset($update['callback_query']),
                'update_id' => $update['update_id'] ?? 'unknown',
                'client_ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent')
            ]);

            // Debug the message structure if it exists
            if (isset($update['message'])) {
                $this->telegramService->debugMessageStructure($update['message']);



                // Log specific information about replies
                if (isset($update['message']['reply_to_message'])) {
                    Log::info('Reply to message detected', [
                        'chat_id' => $update['message']['chat']['id'] ?? 'unknown',
                        'from_id' => $update['message']['from']['id'] ?? 'unknown',
                        'reply_to_message_id' => $update['message']['reply_to_message']['message_id'] ?? 'unknown',
                        'text_preview' => isset($update['message']['text']) ?
                            substr($update['message']['text'], 0, 50) . (strlen($update['message']['text']) > 50 ? '...' : '') :
                            'no text'
                    ]);

                    // Log the reply_to_message text for debugging
                    if (isset($update['message']['reply_to_message']['text'])) {
                        $replyToText = $update['message']['reply_to_message']['text'];
                        Log::debug('Reply to message text', [
                            'text_length' => strlen($replyToText),
                            'text_preview' => substr($replyToText, 0, 100) . (strlen($replyToText) > 100 ? '...' : '')
                        ]);


                    } else {
                        Log::warning('Reply to message has no text field');
                    }

                    // Check for entities in the reply_to_message
                    if (isset($update['message']['reply_to_message']['entities'])) {
                        Log::debug('Reply to message has entities', [
                            'entities' => $update['message']['reply_to_message']['entities']
                        ]);
                    }
                }
            }

            // Process the update
            $result = $this->telegramService->processUpdate($update);

            // Log the processing result
            Log::info('Telegram update processed', [
                'success' => !is_null($result),
                'result' => $result ? 'Response sent' : 'No response needed'
            ]);

            // Return a successful response
            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            // Log the error with detailed information
            Log::error('Error processing Telegram webhook', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return an error response
            return response()->json(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }
}
