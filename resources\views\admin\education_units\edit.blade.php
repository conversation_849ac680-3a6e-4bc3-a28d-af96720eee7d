@extends('admin.layouts.app')

@section('title', 'Edit Education Unit')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Edit Education Unit</h1>
        <a href="{{ route('admin.education-units.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="{{ route('admin.education-units.update', $educationUnit) }}" method="POST" enctype="multipart/form-data" id="educationUnitForm">
                @csrf
                @method('PUT')

                <!-- Section tabs -->
                <ul class="nav nav-tabs mb-4" id="sectionTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic-section" type="button" role="tab" aria-controls="basic-section" aria-selected="true">Basic Information</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="details-tab" data-bs-toggle="tab" data-bs-target="#details-section" type="button" role="tab" aria-controls="details-section" aria-selected="false">Details</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="principal-tab" data-bs-toggle="tab" data-bs-target="#principal-section" type="button" role="tab" aria-controls="principal-section" aria-selected="false">School Principal</button>
                    </li>
                </ul>

                <!-- Tab content -->
                <div class="tab-content" id="sectionTabsContent">
                    <!-- Section 1: Basic Information -->
                    <div class="tab-pane fade show active" id="basic-section" role="tabpanel" aria-labelledby="basic-tab">
                        <!-- Hidden input for education type -->
                        <input type="hidden" name="edu_type" value="{{ old('edu_type', $educationUnit->edu_type) }}">

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">Name (Indonesian)</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $educationUnit->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="name_en" class="form-label">Name (English)</label>
                                <input type="text" class="form-control @error('name_en') is-invalid @enderror" id="name_en" name="name_en" value="{{ old('name_en', $educationUnit->name_en) }}" required>
                                @error('name_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="level" class="form-label">Level (Indonesian)</label>
                                <input type="text" class="form-control @error('level') is-invalid @enderror" id="level" name="level" value="{{ old('level', $educationUnit->level) }}">
                                <small class="text-muted">Example: SD, SMP, SMA, etc.</small>
                                @error('level')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="level_en" class="form-label">Level (English)</label>
                                <input type="text" class="form-control @error('level_en') is-invalid @enderror" id="level_en" name="level_en" value="{{ old('level_en', $educationUnit->level_en) }}">
                                <small class="text-muted">Example: Elementary School, Junior High School, etc.</small>
                                @error('level_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="description" class="form-label">Description (Indonesian)</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="5">{{ old('description', $educationUnit->description) }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="description_en" class="form-label">Description (English)</label>
                                <textarea class="form-control @error('description_en') is-invalid @enderror" id="description_en" name="description_en" rows="5">{{ old('description_en', $educationUnit->description_en) }}</textarea>
                                @error('description_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="image" class="form-label">Image</label>
                                @if($educationUnit->image)
                                    <div class="mb-2">
                                        <img src="{{ asset('storage/' . $educationUnit->image) }}" alt="{{ $educationUnit->name }}" class="img-thumbnail" style="max-height: 150px;">
                                    </div>
                                @endif
                                <input type="file" class="form-control @error('image') is-invalid @enderror" id="image" name="image" accept="image/*">
                                <small class="text-muted">Leave empty to keep the current image. Recommended size: 800x600 pixels. Max size: 2MB.</small>
                                @error('image')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="order" class="form-label">Display Order</label>
                                <input type="number" class="form-control @error('order') is-invalid @enderror" id="order" name="order" value="{{ old('order', $educationUnit->order) }}" min="0">
                                <small class="text-muted">Lower numbers will be displayed first.</small>
                                @error('order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <img id="image-preview" src="#" alt="Preview" class="img-thumbnail d-none" style="max-height: 200px;">
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="button" id="goToDetailsBtn" class="btn btn-primary">
                                Next <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Section 2: Details -->
                    <div class="tab-pane fade" id="details-section" role="tabpanel" aria-labelledby="details-tab">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="facilities" class="form-label">Facilities (Indonesian)</label>
                                <textarea class="form-control @error('facilities') is-invalid @enderror" id="facilities" name="facilities" rows="5">{{ old('facilities', $educationUnit->facilities) }}</textarea>
                                <small class="text-muted">List the facilities available at this education unit.</small>
                                @error('facilities')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="facilities_en" class="form-label">Facilities (English)</label>
                                <textarea class="form-control @error('facilities_en') is-invalid @enderror" id="facilities_en" name="facilities_en" rows="5">{{ old('facilities_en', $educationUnit->facilities_en) }}</textarea>
                                @error('facilities_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>



                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="address" class="form-label">Address</label>
                                <input type="text" class="form-control @error('address') is-invalid @enderror" id="address" name="address" value="{{ old('address', $educationUnit->address) }}">
                                @error('address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="phone" class="form-label">Phone</label>
                                <input type="text" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" value="{{ old('phone', $educationUnit->phone) }}">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email', $educationUnit->email) }}">
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="website" class="form-label">Website</label>
                                <input type="text" class="form-control @error('website') is-invalid @enderror" id="website" name="website" value="{{ old('website', $educationUnit->website) }}">
                                @error('website')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="button" id="backToBasicBtn" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back
                            </button>
                            <button type="button" id="goToPrincipalBtn" class="btn btn-primary">
                                Next <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Section 3: School Principal -->
                    <div class="tab-pane fade" id="principal-section" role="tabpanel" aria-labelledby="principal-tab">
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="principal_name" class="form-label">Principal Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('principal_name') is-invalid @enderror" id="principal_name" name="principal_name" value="{{ old('principal_name', $educationUnit->principal_name) }}" required>
                                @error('principal_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <input type="hidden" id="principal_name_en" name="principal_name_en" value="{{ old('principal_name_en', $educationUnit->principal_name_en) }}">
                                <small class="text-muted">The principal name will be displayed the same in both Indonesian and English.</small>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="principal_education" class="form-label">Education History (Indonesian)</label>
                                <textarea class="form-control @error('principal_education') is-invalid @enderror" id="principal_education" name="principal_education" rows="5">{{ old('principal_education', $educationUnit->principal_education) }}</textarea>
                                @error('principal_education')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="principal_education_en" class="form-label">Education History (English)</label>
                                <textarea class="form-control @error('principal_education_en') is-invalid @enderror" id="principal_education_en" name="principal_education_en" rows="5">{{ old('principal_education_en', $educationUnit->principal_education_en) }}</textarea>
                                @error('principal_education_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="principal_experience" class="form-label">Experience (Indonesian)</label>
                                <textarea class="form-control @error('principal_experience') is-invalid @enderror" id="principal_experience" name="principal_experience" rows="5">{{ old('principal_experience', $educationUnit->principal_experience) }}</textarea>
                                @error('principal_experience')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="principal_experience_en" class="form-label">Experience (English)</label>
                                <textarea class="form-control @error('principal_experience_en') is-invalid @enderror" id="principal_experience_en" name="principal_experience_en" rows="5">{{ old('principal_experience_en', $educationUnit->principal_experience_en) }}</textarea>
                                @error('principal_experience_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="principal_achievements" class="form-label">Achievements (Indonesian)</label>
                                <textarea class="form-control @error('principal_achievements') is-invalid @enderror" id="principal_achievements" name="principal_achievements" rows="5">{{ old('principal_achievements', $educationUnit->principal_achievements) }}</textarea>
                                @error('principal_achievements')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="principal_achievements_en" class="form-label">Achievements (English)</label>
                                <textarea class="form-control @error('principal_achievements_en') is-invalid @enderror" id="principal_achievements_en" name="principal_achievements_en" rows="5">{{ old('principal_achievements_en', $educationUnit->principal_achievements_en) }}</textarea>
                                @error('principal_achievements_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="principal_image" class="form-label">Principal Photo</label>
                                @if($educationUnit->principal_image)
                                    <div class="mb-2">
                                        <img src="{{ asset('storage/' . $educationUnit->principal_image) }}" alt="{{ $educationUnit->principal_name }}" class="img-thumbnail" style="max-height: 150px;">
                                    </div>
                                @endif
                                <input type="file" class="form-control @error('principal_image') is-invalid @enderror" id="principal_image" name="principal_image" accept="image/*">
                                <small class="text-muted">Leave empty to keep the current image. Recommended size: 400x500 pixels (4:5 aspect ratio). Max size: 2MB.</small>
                                @error('principal_image')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <img id="principal-image-preview" src="#" alt="Preview" class="img-thumbnail d-none" style="max-height: 200px;">
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="button" id="backToDetailsBtn" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back
                            </button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> Update Education Unit
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // Image preview
    document.getElementById('image').addEventListener('change', function(e) {
        const preview = document.getElementById('image-preview');
        const file = e.target.files[0];

        if (file) {
            const reader = new FileReader();

            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.classList.remove('d-none');
            }

            reader.readAsDataURL(file);
        } else {
            preview.src = '#';
            preview.classList.add('d-none');
        }
    });

    // Section navigation
    document.getElementById('goToDetailsBtn').addEventListener('click', function() {
        const detailsTab = document.getElementById('details-tab');
        bootstrap.Tab.getOrCreateInstance(detailsTab).show();
    });

    document.getElementById('backToBasicBtn').addEventListener('click', function() {
        const basicTab = document.getElementById('basic-tab');
        bootstrap.Tab.getOrCreateInstance(basicTab).show();
    });

    // Principal image preview
    document.getElementById('principal_image').addEventListener('change', function(e) {
        const preview = document.getElementById('principal-image-preview');
        const file = e.target.files[0];

        if (file) {
            const reader = new FileReader();

            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.classList.remove('d-none');
            }

            reader.readAsDataURL(file);
        } else {
            preview.src = '#';
            preview.classList.add('d-none');
        }
    });

    // Additional tab navigation
    document.getElementById('goToPrincipalBtn').addEventListener('click', function() {
        const principalTab = document.getElementById('principal-tab');
        bootstrap.Tab.getOrCreateInstance(principalTab).show();
    });

    document.getElementById('backToDetailsBtn').addEventListener('click', function() {
        const detailsTab = document.getElementById('details-tab');
        bootstrap.Tab.getOrCreateInstance(detailsTab).show();
    });

    // Sync principal name to English version
    document.getElementById('principal_name').addEventListener('input', function() {
        document.getElementById('principal_name_en').value = this.value;
    });

    // Rich text editors are now handled by Summernote
    // Add summernote class to all rich text fields
    const textareaIds = [
        'description', 'description_en',
        'facilities', 'facilities_en',
        'principal_education', 'principal_education_en',
        'principal_experience', 'principal_experience_en',
        'principal_achievements', 'principal_achievements_en'
    ];

    textareaIds.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.classList.add('summernote');
        }
    });
</script>
@endpush
